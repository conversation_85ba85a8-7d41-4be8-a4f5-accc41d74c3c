#!/usr/bin/env python3
"""
MinerU API Server
启动MinerU的FastAPI服务
"""

import os
import sys

# 设置环境变量
os.environ['MINERU_MODEL_SOURCE'] = 'local'

print("🚀 Starting MinerU API Server...")
print("📍 Server will be available at:")
print("   - API Base: http://0.0.0.0:8000")
print("   - Swagger UI: http://0.0.0.0:8000/docs")
print("   - ReDoc: http://0.0.0.0:8000/redoc")
print("📝 API Endpoints:")
print("   - POST /file_parse - Parse PDF/Image files")
print("=" * 50)

# 直接导入并运行
from mineru.cli.fast_api import app
import uvicorn

if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False
    )
