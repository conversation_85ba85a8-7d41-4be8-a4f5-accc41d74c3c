#!/usr/bin/env python3
"""
MinerU API 客户端调用示例
服务器地址: http://************:18010
"""

import requests
import json
import os
from pathlib import Path

# API配置
API_BASE_URL = "http://localhost:18010"  # 使用本地地址测试
API_ENDPOINTS = {
    "file_parse": f"{API_BASE_URL}/file_parse",
    "docs": f"{API_BASE_URL}/docs",
    "openapi": f"{API_BASE_URL}/openapi.json"
}

def test_api_connection():
    """测试API连接"""
    try:
        print("🔗 测试API连接...")
        response = requests.get(API_ENDPOINTS["docs"], timeout=10)
        if response.status_code == 200:
            print("✅ API服务连接成功!")
            return True
        else:
            print(f"❌ API连接失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到API服务: {e}")
        return False

def parse_pdf_file(file_path, output_dir="./api_output", **kwargs):
    """
    解析PDF文件
    
    Args:
        file_path: PDF文件路径
        output_dir: 输出目录
        **kwargs: 其他参数
    """
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    try:
        print(f"📤 上传文件: {file_path}")
        
        with open(file_path, 'rb') as f:
            files = {'files': (os.path.basename(file_path), f, 'application/pdf')}
            
            # 默认参数
            data = {
                'output_dir': output_dir,
                'lang_list': kwargs.get('lang_list', ['ch']),
                'backend': kwargs.get('backend', 'pipeline'),
                'parse_method': kwargs.get('parse_method', 'auto'),
                'formula_enable': kwargs.get('formula_enable', True),
                'table_enable': kwargs.get('table_enable', True),
                'return_md': kwargs.get('return_md', True),
                'return_middle_json': kwargs.get('return_middle_json', False),
                'return_model_output': kwargs.get('return_model_output', False),
                'return_content_list': kwargs.get('return_content_list', False),
                'return_images': kwargs.get('return_images', False)
            }
            
            print("⏳ 正在处理文件...")
            response = requests.post(
                API_ENDPOINTS["file_parse"],
                files=files,
                data=data,
                timeout=300  # 5分钟超时
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 文件解析成功!")
                return result
            else:
                print(f"❌ 文件解析失败，状态码: {response.status_code}")
                print(f"错误信息: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ 解析文件时出错: {e}")
        return None

def parse_image_file(file_path, output_dir="./api_output", **kwargs):
    """
    解析图片文件
    
    Args:
        file_path: 图片文件路径
        output_dir: 输出目录
        **kwargs: 其他参数
    """
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    try:
        print(f"📤 上传图片: {file_path}")
        
        # 检测文件类型
        file_ext = Path(file_path).suffix.lower()
        content_type = {
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg'
        }.get(file_ext, 'image/jpeg')
        
        with open(file_path, 'rb') as f:
            files = {'files': (os.path.basename(file_path), f, content_type)}
            
            data = {
                'output_dir': output_dir,
                'lang_list': kwargs.get('lang_list', ['ch']),
                'backend': kwargs.get('backend', 'pipeline'),
                'parse_method': kwargs.get('parse_method', 'ocr'),  # 图片默认使用OCR
                'formula_enable': kwargs.get('formula_enable', True),
                'table_enable': kwargs.get('table_enable', True),
                'return_md': kwargs.get('return_md', True)
            }
            
            print("⏳ 正在处理图片...")
            response = requests.post(
                API_ENDPOINTS["file_parse"],
                files=files,
                data=data,
                timeout=300
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 图片解析成功!")
                return result
            else:
                print(f"❌ 图片解析失败，状态码: {response.status_code}")
                print(f"错误信息: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ 解析图片时出错: {e}")
        return None

def main():
    """主函数 - 演示API调用"""
    print("🚀 MinerU API 客户端")
    print(f"📍 API地址: {API_BASE_URL}")
    print("=" * 60)
    
    # 测试连接
    if not test_api_connection():
        print("❌ 请检查API服务是否正常运行")
        return
    
    # 示例：解析PDF文件
    file_path = "/data/MinerU/input/PDF"

    # 如果是目录，处理目录中的所有PDF文件
    if os.path.isdir(file_path):
        pdf_files = [f for f in os.listdir(file_path) if f.lower().endswith('.pdf')]
        if pdf_files:
            pdf_file = os.path.join(file_path, pdf_files[0])  # 使用第一个PDF文件
        else:
            print(f"❌ 目录中没有找到PDF文件: {file_path}")
            return
    else:
        pdf_file = file_path
    if os.path.exists(pdf_file):
        print("\n📄 测试PDF解析...")
        result = parse_pdf_file(
            pdf_file,
            output_dir="./api_output",
            lang_list=['ch'],
            backend='pipeline',
            return_md=True
        )
        
        if result:
            print("📋 解析结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False)[:1000] + "...")
    
    print("\n" + "=" * 60)
    print("🎯 API调用示例完成!")
    print(f"📖 API文档: {API_ENDPOINTS['docs']}")

if __name__ == "__main__":
    main()
