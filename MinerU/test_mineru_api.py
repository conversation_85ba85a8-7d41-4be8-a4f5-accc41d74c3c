#!/usr/bin/env python3
"""
MinerU API 测试脚本
"""

import requests
import json

def test_api_health():
    """测试API健康状态"""
    try:
        response = requests.get("http://localhost:8000/docs")
        if response.status_code == 200:
            print("✅ API服务运行正常")
            return True
        else:
            print(f"❌ API服务异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到API服务: {e}")
        return False

def test_file_parse_api():
    """测试文件解析API"""
    try:
        # 准备测试文件
        test_file = "./coze-studio/backend/infra/impl/document/parser/builtin/test_data/test_pdf.pdf"
        
        with open(test_file, 'rb') as f:
            files = {'files': ('test.pdf', f, 'application/pdf')}
            data = {
                'output_dir': './api_output',
                'lang_list': ['ch'],
                'backend': 'pipeline',
                'parse_method': 'auto',
                'formula_enable': True,
                'table_enable': True,
                'return_md': True
            }
            
            print("📤 发送文件解析请求...")
            response = requests.post(
                "http://localhost:8000/file_parse",
                files=files,
                data=data,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 文件解析成功!")
                print(f"📄 结果: {json.dumps(result, indent=2, ensure_ascii=False)[:500]}...")
                return True
            else:
                print(f"❌ 文件解析失败，状态码: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 测试文件解析API时出错: {e}")
        return False

def main():
    print("🧪 开始测试MinerU API...")
    print("=" * 50)
    
    # 测试API健康状态
    if not test_api_health():
        return
    
    # 测试文件解析API
    test_file_parse_api()
    
    print("=" * 50)
    print("🎯 API测试完成!")

if __name__ == "__main__":
    main()
