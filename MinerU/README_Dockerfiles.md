# Dockerfile 说明文档

本目录包含了MinerU项目的Docker构建文件，以下是各文件的用途说明：

## 📁 保留的Dockerfile

### 1. `Dockerfile` (主要版本)
- **基础镜像**: Ubuntu 22.04
- **用途**: 基础MinerU环境，CPU版本
- **状态**: ✅ 已成功构建 (`mineru-sglang:latest`)
- **特点**: 
  - 轻量级Ubuntu基础
  - 包含中文字体支持
  - 适合CPU环境或测试使用

### 2. `Dockerfile.cuda124` (GPU兼容版本)
- **基础镜像**: nvidia/cuda:12.4-runtime-ubuntu22.04
- **用途**: GPU加速版本，兼容服务器CUDA 12.4
- **状态**: 🔄 待构建
- **特点**:
  - 完整CUDA 12.4支持
  - 与当前服务器驱动兼容
  - 包含GPU加速的PyTorch
  - 推荐用于生产环境

### 3. `Dockerfile.nvidia` (备用GPU版本)
- **基础镜像**: pytorch/pytorch:2.1.0-cuda12.1-cudnn8-runtime
- **用途**: 基于NVIDIA官方PyTorch镜像的GPU版本
- **状态**: 🔄 备用方案
- **特点**:
  - 预装PyTorch和CUDA
  - 镜像体积较小
  - 适合快速部署

## 🗑️ 已删除的文件

以下文件已被清理，原因如下：

- `Dockerfile.1-4`: 重复的sglang基础镜像，网络问题无法构建
- `Dockerfile.gpu`: 复杂GPU配置，构建失败
- `Dockerfile.gpu-upgrade`: 基于现有镜像升级，未使用
- `Dockerfile.simple-gpu`: 简化版本，已有更好替代方案

## 🚀 使用建议

### 当前环境推荐
由于MinerU已经通过pip直接安装在宿主机上，并且GPU功能正常工作，建议：

1. **生产使用**: 继续使用当前的宿主机安装方式
2. **容器化需求**: 使用 `Dockerfile.cuda124` 构建GPU版本
3. **测试环境**: 使用现有的 `mineru-sglang:latest` 镜像

### 构建命令

```bash
# 构建GPU版本（推荐）
docker build -t mineru-gpu:cuda124 -f Dockerfile.cuda124 .

# 构建NVIDIA版本（备用）
docker build -t mineru-gpu:nvidia -f Dockerfile.nvidia .

# 使用现有基础版本
docker run -it mineru-sglang:latest bash
```

## 📊 存储空间节省

通过清理重复和无用的Dockerfile，节省了约 **7KB** 的磁盘空间，并提高了项目的整洁度。

---
*最后更新: 2025-08-01*
