nohup: ignoring input
2025-08-06 19:16:35.880 | WARNING  | mineru.backend.vlm.predictor:<module>:35 - sglang is not installed. If you are not using sglang, you can ignore this warning.
INFO:     Started server process [2504112]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
ERROR:    [<PERSON><PERSON><PERSON> 98] error while attempting to bind on address ('0.0.0.0', 18010): [errno 98] address already in use
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
🚀 Starting MinerU API Server on Port 18010...
📍 Server will be available at:
   - API Base: http://0.0.0.0:18010
   - External: http://************:18010
   - Swagger UI: http://************:18010/docs
   - ReDoc: http://************:18010/redoc
📝 API Endpoints:
   - POST /file_parse - Parse PDF/Image files
==================================================
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  INFO:     111.4.10.85:10894 - "GET /docs HTTP/1.1" 200 OK
2025-08-06 19:24:02.849 | WARNING  | mineru.cli.common:convert_pdf_bytes_to_bytes_by_pypdfium2:52 - end_page_id is out of range, use pdf_docs length
Cannot set gray non-stroke color because /'P1298' is an invalid float value
Cannot set gray non-stroke color because /'P1283' is an invalid float value
Cannot set gray non-stroke color because /'P124' is an invalid float value
Cannot set gray non-stroke color because /'P1305' is an invalid float value
Cannot set gray non-stroke color because /'P1245' is an invalid float value
Cannot set gray non-stroke color because /'P1259' is an invalid float value
Cannot set gray non-stroke color because /'P1273' is an invalid float value
Cannot set gray non-stroke color because /'P1298' is an invalid float value
Cannot set gray non-stroke color because /'P1283' is an invalid float value
Cannot set gray non-stroke color because /'P124' is an invalid float value
Cannot set gray non-stroke color because /'P1305' is an invalid float value
Cannot set gray non-stroke color because /'P1245' is an invalid float value
Cannot set gray non-stroke color because /'P1259' is an invalid float value
Cannot set gray non-stroke color because /'P1273' is an invalid float value
2025-08-06 19:24:22.216 | INFO     | mineru.backend.pipeline.pipeline_analyze:doc_analyze:124 - Batch 1/1: 50 pages/50 pages
2025-08-06 19:24:22.216 | INFO     | mineru.backend.pipeline.pipeline_analyze:batch_image_analyze:187 - gpu_memory: 40 GB, batch_ratio: 16

Layout Predict:   0%|          | 0/50 [00:00<?, ?it/s]
Layout Predict:  16%|█▌        | 8/50 [00:00<00:01, 23.72it/s]
Layout Predict:  32%|███▏      | 16/50 [00:00<00:01, 24.61it/s]
Layout Predict:  48%|████▊     | 24/50 [00:00<00:01, 24.81it/s]
Layout Predict:  64%|██████▍   | 32/50 [00:01<00:00, 24.85it/s]
Layout Predict:  80%|████████  | 40/50 [00:01<00:00, 25.30it/s]
Layout Predict:  96%|█████████▌| 48/50 [00:01<00:00, 25.23it/s]
Layout Predict: 100%|██████████| 50/50 [00:02<00:00, 24.92it/s]

MFD Predict:   0%|          | 0/50 [00:00<?, ?it/s]
MFD Predict:   4%|▍         | 2/50 [00:00<00:02, 17.45it/s]
MFD Predict:   8%|▊         | 4/50 [00:00<00:02, 17.04it/s]
MFD Predict:  12%|█▏        | 6/50 [00:00<00:02, 17.14it/s]
MFD Predict:  16%|█▌        | 8/50 [00:00<00:02, 17.13it/s]
MFD Predict:  20%|██        | 10/50 [00:00<00:02, 17.10it/s]
MFD Predict:  24%|██▍       | 12/50 [00:00<00:02, 17.08it/s]
MFD Predict:  28%|██▊       | 14/50 [00:00<00:02, 17.08it/s]
MFD Predict:  32%|███▏      | 16/50 [00:00<00:01, 17.11it/s]
MFD Predict:  36%|███▌      | 18/50 [00:01<00:01, 17.10it/s]
MFD Predict:  40%|████      | 20/50 [00:01<00:01, 17.11it/s]
MFD Predict:  44%|████▍     | 22/50 [00:01<00:01, 17.14it/s]
MFD Predict:  48%|████▊     | 24/50 [00:01<00:01, 17.19it/s]
MFD Predict:  52%|█████▏    | 26/50 [00:01<00:01, 17.20it/s]
MFD Predict:  56%|█████▌    | 28/50 [00:01<00:01, 17.19it/s]
MFD Predict:  60%|██████    | 30/50 [00:01<00:01, 17.16it/s]
MFD Predict:  64%|██████▍   | 32/50 [00:01<00:01, 17.07it/s]
MFD Predict:  68%|██████▊   | 34/50 [00:01<00:00, 16.95it/s]
MFD Predict:  72%|███████▏  | 36/50 [00:02<00:00, 16.86it/s]
MFD Predict:  76%|███████▌  | 38/50 [00:02<00:00, 16.77it/s]
MFD Predict:  80%|████████  | 40/50 [00:02<00:00, 16.78it/s]
MFD Predict:  84%|████████▍ | 42/50 [00:02<00:00, 16.72it/s]
MFD Predict:  88%|████████▊ | 44/50 [00:02<00:00, 16.62it/s]
MFD Predict:  92%|█████████▏| 46/50 [00:02<00:00, 16.61it/s]
MFD Predict:  96%|█████████▌| 48/50 [00:02<00:00, 16.57it/s]
MFD Predict: 100%|██████████| 50/50 [00:02<00:00, 16.59it/s]
MFD Predict: 100%|██████████| 50/50 [00:02<00:00, 16.93it/s]

MFR Predict:   0%|          | 0/57 [00:00<?, ?it/s]
MFR Predict:  56%|█████▌    | 32/57 [00:00<00:00, 187.73it/s]
MFR Predict: 100%|██████████| 57/57 [00:00<00:00, 78.74it/s] 
MFR Predict: 100%|██████████| 57/57 [00:00<00:00, 87.22it/s]

OCR-det ch:   0%|          | 0/242 [00:00<?, ?it/s]
OCR-det ch:   0%|          | 1/242 [00:00<00:54,  4.42it/s]
OCR-det ch:   2%|▏         | 5/242 [00:00<00:13, 17.20it/s]
OCR-det ch:   3%|▎         | 8/242 [00:00<00:14, 16.07it/s]
OCR-det ch:   5%|▍         | 12/242 [00:00<00:10, 21.78it/s]
OCR-det ch:   6%|▌         | 15/242 [00:00<00:09, 23.39it/s]
OCR-det ch:   8%|▊         | 19/242 [00:00<00:08, 25.86it/s]
OCR-det ch:  10%|▉         | 23/242 [00:01<00:07, 28.13it/s]
OCR-det ch:  12%|█▏        | 28/242 [00:01<00:06, 33.54it/s]
OCR-det ch:  13%|█▎        | 32/242 [00:01<00:07, 29.63it/s]
OCR-det ch:  15%|█▍        | 36/242 [00:01<00:07, 29.00it/s]
OCR-det ch:  17%|█▋        | 40/242 [00:01<00:09, 22.33it/s]
OCR-det ch:  18%|█▊        | 43/242 [00:01<00:09, 22.06it/s]
OCR-det ch:  19%|█▉        | 46/242 [00:01<00:08, 23.14it/s]
OCR-det ch:  21%|██        | 51/242 [00:02<00:06, 28.22it/s]
OCR-det ch:  23%|██▎       | 56/242 [00:02<00:05, 31.58it/s]
OCR-det ch:  25%|██▌       | 61/242 [00:02<00:05, 35.15it/s]
OCR-det ch:  27%|██▋       | 65/242 [00:02<00:05, 31.44it/s]
OCR-det ch:  29%|██▉       | 70/242 [00:02<00:05, 33.02it/s]
OCR-det ch:  31%|███       | 74/242 [00:02<00:05, 32.76it/s]
OCR-det ch:  32%|███▏      | 78/242 [00:02<00:05, 31.36it/s]
OCR-det ch:  34%|███▍      | 82/242 [00:03<00:05, 28.06it/s]
OCR-det ch:  36%|███▌      | 86/242 [00:03<00:05, 28.19it/s]
OCR-det ch:  37%|███▋      | 89/242 [00:03<00:05, 28.13it/s]
OCR-det ch:  38%|███▊      | 92/242 [00:03<00:05, 28.24it/s]
OCR-det ch:  39%|███▉      | 95/242 [00:03<00:05, 27.82it/s]
OCR-det ch:  41%|████      | 99/242 [00:03<00:06, 22.86it/s]
OCR-det ch:  43%|████▎     | 103/242 [00:03<00:05, 25.63it/s]
OCR-det ch:  44%|████▍     | 107/242 [00:03<00:04, 27.76it/s]
OCR-det ch:  45%|████▌     | 110/242 [00:04<00:04, 26.66it/s]
OCR-det ch:  47%|████▋     | 113/242 [00:04<00:06, 20.36it/s]
OCR-det ch:  48%|████▊     | 116/242 [00:04<00:07, 17.35it/s]
OCR-det ch:  49%|████▉     | 118/242 [00:04<00:07, 17.53it/s]
OCR-det ch:  50%|████▉     | 120/242 [00:04<00:08, 13.65it/s]
OCR-det ch:  51%|█████     | 124/242 [00:05<00:06, 18.01it/s]
OCR-det ch:  53%|█████▎    | 129/242 [00:05<00:04, 23.38it/s]
OCR-det ch:  55%|█████▍    | 132/242 [00:05<00:04, 22.42it/s]
OCR-det ch:  56%|█████▌    | 136/242 [00:05<00:04, 24.59it/s]
OCR-det ch:  58%|█████▊    | 140/242 [00:05<00:03, 27.08it/s]
OCR-det ch:  59%|█████▉    | 143/242 [00:05<00:03, 27.43it/s]
OCR-det ch:  60%|██████    | 146/242 [00:05<00:03, 26.56it/s]
OCR-det ch:  62%|██████▏   | 149/242 [00:06<00:04, 21.38it/s]
OCR-det ch:  63%|██████▎   | 153/242 [00:06<00:03, 25.05it/s]
OCR-det ch:  64%|██████▍   | 156/242 [00:06<00:03, 24.46it/s]
OCR-det ch:  66%|██████▌   | 160/242 [00:06<00:02, 27.57it/s]
OCR-det ch:  68%|██████▊   | 165/242 [00:06<00:02, 32.93it/s]
OCR-det ch:  70%|██████▉   | 169/242 [00:06<00:02, 33.38it/s]
OCR-det ch:  72%|███████▏  | 174/242 [00:06<00:01, 36.71it/s]
OCR-det ch:  74%|███████▍  | 179/242 [00:06<00:01, 38.00it/s]
OCR-det ch:  76%|███████▌  | 184/242 [00:06<00:01, 39.29it/s]
OCR-det ch:  78%|███████▊  | 188/242 [00:07<00:01, 37.40it/s]
OCR-det ch:  79%|███████▉  | 192/242 [00:07<00:01, 37.27it/s]
OCR-det ch:  81%|████████  | 196/242 [00:07<00:01, 36.93it/s]
OCR-det ch:  83%|████████▎ | 200/242 [00:07<00:01, 36.30it/s]
OCR-det ch:  84%|████████▍ | 204/242 [00:07<00:01, 36.39it/s]
OCR-det ch:  86%|████████▌ | 208/242 [00:07<00:00, 36.70it/s]
OCR-det ch:  88%|████████▊ | 212/242 [00:07<00:00, 35.64it/s]
OCR-det ch:  90%|████████▉ | 217/242 [00:07<00:00, 35.61it/s]
OCR-det ch:  91%|█████████▏| 221/242 [00:08<00:00, 34.89it/s]
OCR-det ch:  93%|█████████▎| 225/242 [00:08<00:00, 33.25it/s]
OCR-det ch:  95%|█████████▍| 229/242 [00:08<00:00, 34.37it/s]
OCR-det ch:  97%|█████████▋| 234/242 [00:08<00:00, 37.04it/s]
OCR-det ch:  98%|█████████▊| 238/242 [00:08<00:00, 36.18it/s]
OCR-det ch: 100%|██████████| 242/242 [00:08<00:00, 28.22it/s]

Table Predict:   0%|          | 0/16 [00:00<?, ?it/s]
Table Predict:   6%|▋         | 1/16 [00:00<00:06,  2.40it/s]
Table Predict:  12%|█▎        | 2/16 [00:01<00:10,  1.29it/s]
Table Predict:  19%|█▉        | 3/16 [00:01<00:06,  2.07it/s]
Table Predict:  25%|██▌       | 4/16 [00:01<00:04,  2.88it/s]
Table Predict:  31%|███▏      | 5/16 [00:01<00:03,  3.38it/s]
Table Predict:  38%|███▊      | 6/16 [00:02<00:02,  3.46it/s]
Table Predict:  44%|████▍     | 7/16 [00:02<00:02,  4.16it/s]
Table Predict:  50%|█████     | 8/16 [00:02<00:01,  4.33it/s]
Table Predict:  56%|█████▋    | 9/16 [00:02<00:01,  5.04it/s]
Table Predict:  62%|██████▎   | 10/16 [00:02<00:01,  4.96it/s]
Table Predict:  69%|██████▉   | 11/16 [00:03<00:00,  5.50it/s]
Table Predict:  75%|███████▌  | 12/16 [00:03<00:00,  5.92it/s]
Table Predict:  81%|████████▏ | 13/16 [00:03<00:00,  6.24it/s]
Table Predict:  88%|████████▊ | 14/16 [00:03<00:00,  4.73it/s]
Table Predict:  94%|█████████▍| 15/16 [00:03<00:00,  5.02it/s]
Table Predict: 100%|██████████| 16/16 [00:04<00:00,  4.43it/s]
Table Predict: 100%|██████████| 16/16 [00:04<00:00,  3.91it/s]

Processing pages:   0%|          | 0/50 [00:00<?, ?it/s]
Processing pages:   6%|▌         | 3/50 [00:00<00:02, 17.92it/s]
Processing pages:  10%|█         | 5/50 [00:00<00:02, 16.63it/s]
Processing pages:  14%|█▍        | 7/50 [00:00<00:02, 17.45it/s]
Processing pages:  18%|█▊        | 9/50 [00:00<00:02, 16.38it/s]
Processing pages:  22%|██▏       | 11/50 [00:00<00:02, 16.06it/s]
Processing pages:  26%|██▌       | 13/50 [00:00<00:02, 17.14it/s]
Processing pages:  30%|███       | 15/50 [00:00<00:02, 16.85it/s]
Processing pages:  34%|███▍      | 17/50 [00:01<00:02, 16.13it/s]
Processing pages:  38%|███▊      | 19/50 [00:01<00:01, 16.41it/s]
Processing pages:  42%|████▏     | 21/50 [00:01<00:01, 15.55it/s]
Processing pages:  46%|████▌     | 23/50 [00:01<00:01, 15.23it/s]
Processing pages:  50%|█████     | 25/50 [00:01<00:01, 15.03it/s]
Processing pages:  54%|█████▍    | 27/50 [00:01<00:01, 14.81it/s]
Processing pages:  58%|█████▊    | 29/50 [00:01<00:01, 14.77it/s]
Processing pages:  62%|██████▏   | 31/50 [00:01<00:01, 14.44it/s]2025-08-06 19:24:43.549 | WARNING  | mineru.utils.block_sort:sort_lines_by_model:115 - bottom > page_h, left: 30, right: 840, top: 402.66666666666674, bottom: 540.0000000000001, page_w: 960, page_h: 540

Processing pages:  66%|██████▌   | 33/50 [00:02<00:01, 15.08it/s]2025-08-06 19:24:43.690 | WARNING  | mineru.utils.block_sort:sort_lines_by_model:115 - bottom > page_h, left: 5, right: 819, top: 398.66666666666674, bottom: 540.0000000000001, page_w: 960, page_h: 540

Processing pages:  70%|███████   | 35/50 [00:02<00:01, 14.79it/s]
Processing pages:  74%|███████▍  | 37/50 [00:02<00:00, 14.56it/s]
Processing pages:  78%|███████▊  | 39/50 [00:02<00:00, 14.25it/s]
Processing pages:  82%|████████▏ | 41/50 [00:02<00:00, 15.47it/s]
Processing pages:  86%|████████▌ | 43/50 [00:02<00:00, 15.43it/s]
Processing pages:  90%|█████████ | 45/50 [00:02<00:00, 15.69it/s]
Processing pages:  94%|█████████▍| 47/50 [00:03<00:00, 15.67it/s]
Processing pages:  98%|█████████▊| 49/50 [00:03<00:00, 16.59it/s]
Processing pages: 100%|██████████| 50/50 [00:03<00:00, 15.82it/s]

OCR-rec Predict:   0%|          | 0/224 [00:00<?, ?it/s]
OCR-rec Predict:  36%|███▌      | 80/224 [00:00<00:00, 749.84it/s]
OCR-rec Predict:  71%|███████▏  | 160/224 [00:00<00:00, 746.51it/s]
OCR-rec Predict: 100%|██████████| 224/224 [00:00<00:00, 706.79it/s]
2025-08-06 19:24:45.320 | INFO     | mineru.cli.common:_process_output:156 - local output dir is E:\政企知识问答检索项目\PDF-OUTPUT/94425ff2-7743-4972-81a2-6a500e4c1ed9/1、中国移动OnePark智慧园区解决方案Ver24.06/auto
INFO:     111.4.10.85:10927 - "POST /file_parse HTTP/1.1" 200 OK
INFO:     111.4.10.85:13327 - "GET /docs HTTP/1.1" 200 OK
2025-08-06 20:14:36.662 | WARNING  | mineru.cli.common:convert_pdf_bytes_to_bytes_by_pypdfium2:52 - end_page_id is out of range, use pdf_docs length
Cannot set gray non-stroke color because /'P1204' is an invalid float value
Cannot set gray non-stroke color because /'P1206' is an invalid float value
Cannot set gray non-stroke color because /'P1208' is an invalid float value
Cannot set gray non-stroke color because /'P1210' is an invalid float value
Cannot set gray non-stroke color because /'P1214' is an invalid float value
Cannot set gray non-stroke color because /'P1216' is an invalid float value
Cannot set gray non-stroke color because /'P1220' is an invalid float value
Cannot set gray non-stroke color because /'P1222' is an invalid float value
Cannot set gray non-stroke color because /'P1283' is an invalid float value
Cannot set gray non-stroke color because /'P1157' is an invalid float value
Cannot set gray non-stroke color because /'P1162' is an invalid float value
Cannot set gray non-stroke color because /'P1167' is an invalid float value
Cannot set gray non-stroke color because /'P1182' is an invalid float value
Cannot set gray non-stroke color because /'P1204' is an invalid float value
Cannot set gray non-stroke color because /'P1206' is an invalid float value
Cannot set gray non-stroke color because /'P1208' is an invalid float value
Cannot set gray non-stroke color because /'P1210' is an invalid float value
Cannot set gray non-stroke color because /'P1214' is an invalid float value
Cannot set gray non-stroke color because /'P1216' is an invalid float value
Cannot set gray non-stroke color because /'P1220' is an invalid float value
Cannot set gray non-stroke color because /'P1222' is an invalid float value
Cannot set gray non-stroke color because /'P1283' is an invalid float value
Cannot set gray non-stroke color because /'P1157' is an invalid float value
Cannot set gray non-stroke color because /'P1162' is an invalid float value
Cannot set gray non-stroke color because /'P1167' is an invalid float value
Cannot set gray non-stroke color because /'P1182' is an invalid float value
2025-08-06 20:14:56.130 | INFO     | mineru.backend.pipeline.pipeline_analyze:doc_analyze:124 - Batch 1/1: 50 pages/50 pages
2025-08-06 20:14:56.131 | INFO     | mineru.backend.pipeline.pipeline_analyze:batch_image_analyze:187 - gpu_memory: 40 GB, batch_ratio: 16

Layout Predict:   0%|          | 0/50 [00:00<?, ?it/s]
Layout Predict:  16%|█▌        | 8/50 [00:00<00:02, 20.59it/s]
Layout Predict:  32%|███▏      | 16/50 [00:00<00:01, 23.18it/s]
Layout Predict:  48%|████▊     | 24/50 [00:01<00:01, 24.04it/s]
Layout Predict:  64%|██████▍   | 32/50 [00:01<00:00, 23.90it/s]
Layout Predict:  80%|████████  | 40/50 [00:01<00:00, 24.31it/s]
Layout Predict:  96%|█████████▌| 48/50 [00:01<00:00, 25.11it/s]
Layout Predict: 100%|██████████| 50/50 [00:02<00:00, 24.29it/s]

MFD Predict:   0%|          | 0/50 [00:00<?, ?it/s]
MFD Predict:   4%|▍         | 2/50 [00:00<00:02, 18.53it/s]
MFD Predict:   8%|▊         | 4/50 [00:00<00:02, 18.47it/s]
MFD Predict:  12%|█▏        | 6/50 [00:00<00:02, 18.55it/s]
MFD Predict:  16%|█▌        | 8/50 [00:00<00:02, 18.61it/s]
MFD Predict:  20%|██        | 10/50 [00:00<00:02, 18.59it/s]
MFD Predict:  24%|██▍       | 12/50 [00:00<00:02, 18.59it/s]
MFD Predict:  28%|██▊       | 14/50 [00:00<00:01, 18.59it/s]
MFD Predict:  32%|███▏      | 16/50 [00:00<00:01, 18.62it/s]
MFD Predict:  36%|███▌      | 18/50 [00:00<00:01, 18.16it/s]
MFD Predict:  40%|████      | 20/50 [00:01<00:01, 18.29it/s]
MFD Predict:  44%|████▍     | 22/50 [00:01<00:01, 18.40it/s]
MFD Predict:  48%|████▊     | 24/50 [00:01<00:01, 18.48it/s]
MFD Predict:  52%|█████▏    | 26/50 [00:01<00:01, 18.55it/s]
MFD Predict:  56%|█████▌    | 28/50 [00:01<00:01, 18.55it/s]
MFD Predict:  60%|██████    | 30/50 [00:01<00:01, 18.57it/s]
MFD Predict:  64%|██████▍   | 32/50 [00:01<00:00, 18.63it/s]
MFD Predict:  68%|██████▊   | 34/50 [00:01<00:00, 18.58it/s]
MFD Predict:  72%|███████▏  | 36/50 [00:01<00:00, 18.51it/s]
MFD Predict:  76%|███████▌  | 38/50 [00:02<00:00, 18.11it/s]
MFD Predict:  80%|████████  | 40/50 [00:02<00:00, 17.78it/s]
MFD Predict:  84%|████████▍ | 42/50 [00:02<00:00, 17.41it/s]
MFD Predict:  88%|████████▊ | 44/50 [00:02<00:00, 17.20it/s]
MFD Predict:  92%|█████████▏| 46/50 [00:02<00:00, 17.21it/s]
MFD Predict:  96%|█████████▌| 48/50 [00:02<00:00, 17.20it/s]
MFD Predict: 100%|██████████| 50/50 [00:02<00:00, 17.22it/s]
MFD Predict: 100%|██████████| 50/50 [00:02<00:00, 18.07it/s]

MFR Predict:   0%|          | 0/57 [00:00<?, ?it/s]
MFR Predict:  56%|█████▌    | 32/57 [00:00<00:00, 170.60it/s]
MFR Predict: 100%|██████████| 57/57 [00:00<00:00, 77.53it/s] 
MFR Predict: 100%|██████████| 57/57 [00:00<00:00, 85.33it/s]

OCR-det ch:   0%|          | 0/242 [00:00<?, ?it/s]
OCR-det ch:   0%|          | 1/242 [00:00<00:50,  4.75it/s]
OCR-det ch:   2%|▏         | 5/242 [00:00<00:13, 17.95it/s]
OCR-det ch:   3%|▎         | 8/242 [00:00<00:14, 16.47it/s]
OCR-det ch:   5%|▍         | 12/242 [00:00<00:10, 22.15it/s]
OCR-det ch:   6%|▌         | 15/242 [00:00<00:09, 23.66it/s]
OCR-det ch:   8%|▊         | 19/242 [00:00<00:08, 25.99it/s]
OCR-det ch:   9%|▉         | 22/242 [00:00<00:08, 27.05it/s]
OCR-det ch:  11%|█         | 27/242 [00:01<00:06, 33.19it/s]
OCR-det ch:  13%|█▎        | 31/242 [00:01<00:06, 31.45it/s]
OCR-det ch:  14%|█▍        | 35/242 [00:01<00:07, 28.72it/s]
OCR-det ch:  16%|█▌        | 39/242 [00:01<00:08, 22.91it/s]
OCR-det ch:  17%|█▋        | 42/242 [00:01<00:08, 22.27it/s]
OCR-det ch:  19%|█▊        | 45/242 [00:01<00:08, 23.05it/s]
OCR-det ch:  21%|██        | 50/242 [00:02<00:06, 28.23it/s]
OCR-det ch:  23%|██▎       | 55/242 [00:02<00:06, 31.11it/s]
OCR-det ch:  25%|██▍       | 60/242 [00:02<00:05, 34.43it/s]
OCR-det ch:  26%|██▋       | 64/242 [00:02<00:05, 31.06it/s]
OCR-det ch:  29%|██▊       | 69/242 [00:02<00:05, 34.26it/s]
OCR-det ch:  30%|███       | 73/242 [00:02<00:05, 31.73it/s]
OCR-det ch:  32%|███▏      | 77/242 [00:02<00:05, 31.61it/s]
OCR-det ch:  33%|███▎      | 81/242 [00:02<00:05, 28.67it/s]
OCR-det ch:  35%|███▌      | 85/242 [00:03<00:05, 30.31it/s]
OCR-det ch:  37%|███▋      | 89/242 [00:03<00:05, 27.49it/s]
OCR-det ch:  38%|███▊      | 92/242 [00:03<00:05, 27.79it/s]
OCR-det ch:  39%|███▉      | 95/242 [00:03<00:05, 27.55it/s]
OCR-det ch:  41%|████      | 99/242 [00:03<00:06, 22.84it/s]
OCR-det ch:  43%|████▎     | 103/242 [00:03<00:05, 25.58it/s]
OCR-det ch:  44%|████▍     | 107/242 [00:03<00:04, 27.69it/s]
OCR-det ch:  45%|████▌     | 110/242 [00:04<00:04, 26.63it/s]
OCR-det ch:  47%|████▋     | 113/242 [00:04<00:06, 20.31it/s]
OCR-det ch:  48%|████▊     | 116/242 [00:04<00:07, 17.29it/s]
OCR-det ch:  49%|████▉     | 118/242 [00:04<00:07, 17.33it/s]
OCR-det ch:  50%|████▉     | 120/242 [00:04<00:09, 13.40it/s]
OCR-det ch:  51%|█████     | 124/242 [00:05<00:06, 17.73it/s]
OCR-det ch:  53%|█████▎    | 129/242 [00:05<00:04, 23.07it/s]
OCR-det ch:  55%|█████▍    | 132/242 [00:05<00:04, 22.13it/s]
OCR-det ch:  56%|█████▌    | 136/242 [00:05<00:04, 24.29it/s]
OCR-det ch:  58%|█████▊    | 140/242 [00:05<00:03, 26.79it/s]
OCR-det ch:  59%|█████▉    | 143/242 [00:05<00:03, 27.25it/s]
OCR-det ch:  60%|██████    | 146/242 [00:05<00:03, 26.43it/s]
OCR-det ch:  62%|██████▏   | 149/242 [00:06<00:04, 21.28it/s]
OCR-det ch:  63%|██████▎   | 153/242 [00:06<00:03, 24.96it/s]
OCR-det ch:  64%|██████▍   | 156/242 [00:06<00:03, 24.41it/s]
OCR-det ch:  66%|██████▌   | 160/242 [00:06<00:02, 27.50it/s]
OCR-det ch:  68%|██████▊   | 165/242 [00:06<00:02, 32.90it/s]
OCR-det ch:  70%|██████▉   | 169/242 [00:06<00:02, 33.27it/s]
OCR-det ch:  72%|███████▏  | 174/242 [00:06<00:01, 36.64it/s]
OCR-det ch:  74%|███████▍  | 179/242 [00:06<00:01, 37.89it/s]
OCR-det ch:  76%|███████▌  | 184/242 [00:06<00:01, 39.22it/s]
OCR-det ch:  78%|███████▊  | 188/242 [00:07<00:01, 37.14it/s]
OCR-det ch:  79%|███████▉  | 192/242 [00:07<00:01, 37.07it/s]
OCR-det ch:  81%|████████  | 196/242 [00:07<00:01, 36.84it/s]
OCR-det ch:  83%|████████▎ | 200/242 [00:07<00:01, 36.13it/s]
OCR-det ch:  84%|████████▍ | 204/242 [00:07<00:01, 36.21it/s]
OCR-det ch:  86%|████████▌ | 208/242 [00:07<00:00, 36.55it/s]
OCR-det ch:  88%|████████▊ | 212/242 [00:07<00:00, 35.46it/s]
OCR-det ch:  90%|████████▉ | 217/242 [00:07<00:00, 35.42it/s]
OCR-det ch:  91%|█████████▏| 221/242 [00:08<00:00, 34.79it/s]
OCR-det ch:  93%|█████████▎| 225/242 [00:08<00:00, 33.17it/s]
OCR-det ch:  95%|█████████▍| 229/242 [00:08<00:00, 34.25it/s]
OCR-det ch:  97%|█████████▋| 234/242 [00:08<00:00, 36.90it/s]
OCR-det ch:  98%|█████████▊| 238/242 [00:08<00:00, 36.02it/s]
OCR-det ch: 100%|██████████| 242/242 [00:08<00:00, 28.18it/s]

Table Predict:   0%|          | 0/16 [00:00<?, ?it/s]
Table Predict:   6%|▋         | 1/16 [00:00<00:06,  2.41it/s]
Table Predict:  12%|█▎        | 2/16 [00:01<00:10,  1.29it/s]
Table Predict:  19%|█▉        | 3/16 [00:01<00:06,  2.06it/s]
Table Predict:  25%|██▌       | 4/16 [00:01<00:04,  2.87it/s]
Table Predict:  31%|███▏      | 5/16 [00:01<00:03,  3.37it/s]
Table Predict:  38%|███▊      | 6/16 [00:02<00:02,  3.46it/s]
Table Predict:  44%|████▍     | 7/16 [00:02<00:02,  4.15it/s]
Table Predict:  50%|█████     | 8/16 [00:02<00:01,  4.20it/s]
Table Predict:  56%|█████▋    | 9/16 [00:02<00:01,  4.88it/s]
Table Predict:  62%|██████▎   | 10/16 [00:02<00:01,  4.83it/s]
Table Predict:  69%|██████▉   | 11/16 [00:03<00:00,  5.38it/s]
Table Predict:  75%|███████▌  | 12/16 [00:03<00:00,  5.82it/s]
Table Predict:  81%|████████▏ | 13/16 [00:03<00:00,  6.15it/s]
Table Predict:  88%|████████▊ | 14/16 [00:03<00:00,  4.73it/s]
Table Predict:  94%|█████████▍| 15/16 [00:03<00:00,  5.02it/s]
Table Predict: 100%|██████████| 16/16 [00:04<00:00,  4.42it/s]
Table Predict: 100%|██████████| 16/16 [00:04<00:00,  3.88it/s]

Processing pages:   0%|          | 0/50 [00:00<?, ?it/s]
Processing pages:   6%|▌         | 3/50 [00:00<00:02, 17.69it/s]
Processing pages:  10%|█         | 5/50 [00:00<00:02, 16.61it/s]
Processing pages:  14%|█▍        | 7/50 [00:00<00:02, 17.49it/s]
Processing pages:  18%|█▊        | 9/50 [00:00<00:02, 16.37it/s]
Processing pages:  22%|██▏       | 11/50 [00:00<00:02, 15.95it/s]
Processing pages:  26%|██▌       | 13/50 [00:00<00:02, 17.05it/s]
Processing pages:  30%|███       | 15/50 [00:00<00:02, 16.81it/s]
Processing pages:  34%|███▍      | 17/50 [00:01<00:02, 16.17it/s]
Processing pages:  38%|███▊      | 19/50 [00:01<00:01, 16.46it/s]
Processing pages:  42%|████▏     | 21/50 [00:01<00:01, 15.63it/s]
Processing pages:  46%|████▌     | 23/50 [00:01<00:01, 15.26it/s]
Processing pages:  50%|█████     | 25/50 [00:01<00:01, 15.06it/s]
Processing pages:  54%|█████▍    | 27/50 [00:01<00:01, 14.83it/s]
Processing pages:  58%|█████▊    | 29/50 [00:01<00:01, 14.75it/s]
Processing pages:  62%|██████▏   | 31/50 [00:01<00:01, 14.41it/s]2025-08-06 20:15:17.278 | WARNING  | mineru.utils.block_sort:sort_lines_by_model:115 - bottom > page_h, left: 30, right: 840, top: 402.66666666666674, bottom: 540.0000000000001, page_w: 960, page_h: 540

Processing pages:  66%|██████▌   | 33/50 [00:02<00:01, 15.08it/s]2025-08-06 20:15:17.417 | WARNING  | mineru.utils.block_sort:sort_lines_by_model:115 - bottom > page_h, left: 5, right: 819, top: 398.66666666666674, bottom: 540.0000000000001, page_w: 960, page_h: 540

Processing pages:  70%|███████   | 35/50 [00:02<00:01, 14.85it/s]
Processing pages:  74%|███████▍  | 37/50 [00:02<00:00, 14.72it/s]
Processing pages:  78%|███████▊  | 39/50 [00:02<00:00, 14.37it/s]
Processing pages:  82%|████████▏ | 41/50 [00:02<00:00, 15.59it/s]
Processing pages:  86%|████████▌ | 43/50 [00:02<00:00, 15.50it/s]
Processing pages:  90%|█████████ | 45/50 [00:02<00:00, 15.72it/s]
Processing pages:  94%|█████████▍| 47/50 [00:03<00:00, 15.67it/s]
Processing pages:  98%|█████████▊| 49/50 [00:03<00:00, 16.61it/s]
Processing pages: 100%|██████████| 50/50 [00:03<00:00, 15.84it/s]

OCR-rec Predict:   0%|          | 0/224 [00:00<?, ?it/s]
OCR-rec Predict:  36%|███▌      | 80/224 [00:00<00:00, 750.68it/s]
OCR-rec Predict:  71%|███████▏  | 160/224 [00:00<00:00, 750.97it/s]
OCR-rec Predict: 100%|██████████| 224/224 [00:00<00:00, 699.90it/s]
2025-08-06 20:15:19.046 | INFO     | mineru.cli.common:_process_output:156 - local output dir is E:\政企知识问答检索项目\PDF-OUTPUT/2596509b-60e1-419d-8274-27d988b56b50/1、中国移动OnePark智慧园区解决方案Ver24.06/auto
INFO:     111.4.10.85:13330 - "POST /file_parse HTTP/1.1" 200 OK
INFO:     111.4.10.85:14124 - "GET /docs HTTP/1.1" 200 OK
2025-08-06 20:40:04.354 | WARNING  | mineru.cli.common:convert_pdf_bytes_to_bytes_by_pypdfium2:52 - end_page_id is out of range, use pdf_docs length
Cannot set gray non-stroke color because /'P1311' is an invalid float value
Cannot set gray non-stroke color because /'P1204' is an invalid float value
Cannot set gray non-stroke color because /'P1206' is an invalid float value
Cannot set gray non-stroke color because /'P1208' is an invalid float value
Cannot set gray non-stroke color because /'P1210' is an invalid float value
Cannot set gray non-stroke color because /'P1214' is an invalid float value
Cannot set gray non-stroke color because /'P1216' is an invalid float value
Cannot set gray non-stroke color because /'P1220' is an invalid float value
Cannot set gray non-stroke color because /'P1222' is an invalid float value
Cannot set gray non-stroke color because /'P1113' is an invalid float value
Cannot set gray non-stroke color because /'P1245' is an invalid float value
Cannot set gray non-stroke color because /'P1259' is an invalid float value
Cannot set gray non-stroke color because /'P1273' is an invalid float value
Cannot set gray non-stroke color because /'P1311' is an invalid float value
Cannot set gray non-stroke color because /'P1204' is an invalid float value
Cannot set gray non-stroke color because /'P1206' is an invalid float value
Cannot set gray non-stroke color because /'P1208' is an invalid float value
Cannot set gray non-stroke color because /'P1210' is an invalid float value
Cannot set gray non-stroke color because /'P1214' is an invalid float value
Cannot set gray non-stroke color because /'P1216' is an invalid float value
Cannot set gray non-stroke color because /'P1220' is an invalid float value
Cannot set gray non-stroke color because /'P1222' is an invalid float value
Cannot set gray non-stroke color because /'P1113' is an invalid float value
Cannot set gray non-stroke color because /'P1245' is an invalid float value
Cannot set gray non-stroke color because /'P1259' is an invalid float value
Cannot set gray non-stroke color because /'P1273' is an invalid float value
2025-08-06 20:40:23.674 | INFO     | mineru.backend.pipeline.pipeline_analyze:doc_analyze:124 - Batch 1/1: 50 pages/50 pages
2025-08-06 20:40:23.675 | INFO     | mineru.backend.pipeline.pipeline_analyze:batch_image_analyze:187 - gpu_memory: 40 GB, batch_ratio: 16

Layout Predict:   0%|          | 0/50 [00:00<?, ?it/s]
Layout Predict:  16%|█▌        | 8/50 [00:00<00:01, 25.21it/s]
Layout Predict:  32%|███▏      | 16/50 [00:00<00:01, 25.36it/s]
Layout Predict:  48%|████▊     | 24/50 [00:00<00:01, 25.53it/s]
Layout Predict:  64%|██████▍   | 32/50 [00:01<00:00, 25.35it/s]
Layout Predict:  80%|████████  | 40/50 [00:01<00:00, 25.76it/s]
Layout Predict:  96%|█████████▌| 48/50 [00:01<00:00, 25.58it/s]
Layout Predict: 100%|██████████| 50/50 [00:01<00:00, 25.45it/s]

MFD Predict:   0%|          | 0/50 [00:00<?, ?it/s]
MFD Predict:   4%|▍         | 2/50 [00:00<00:02, 18.28it/s]
MFD Predict:   8%|▊         | 4/50 [00:00<00:02, 18.25it/s]
MFD Predict:  12%|█▏        | 6/50 [00:00<00:02, 18.38it/s]
MFD Predict:  16%|█▌        | 8/50 [00:00<00:02, 18.41it/s]
MFD Predict:  20%|██        | 10/50 [00:00<00:02, 17.75it/s]
MFD Predict:  24%|██▍       | 12/50 [00:00<00:02, 17.52it/s]
MFD Predict:  28%|██▊       | 14/50 [00:00<00:02, 17.37it/s]
MFD Predict:  32%|███▏      | 16/50 [00:00<00:01, 17.30it/s]
MFD Predict:  36%|███▌      | 18/50 [00:01<00:01, 17.20it/s]
MFD Predict:  40%|████      | 20/50 [00:01<00:01, 17.20it/s]
MFD Predict:  44%|████▍     | 22/50 [00:01<00:01, 17.21it/s]
MFD Predict:  48%|████▊     | 24/50 [00:01<00:01, 17.24it/s]
MFD Predict:  52%|█████▏    | 26/50 [00:01<00:01, 17.24it/s]
MFD Predict:  56%|█████▌    | 28/50 [00:01<00:01, 17.18it/s]
MFD Predict:  60%|██████    | 30/50 [00:01<00:01, 17.16it/s]
MFD Predict:  64%|██████▍   | 32/50 [00:01<00:01, 17.08it/s]
MFD Predict:  68%|██████▊   | 34/50 [00:01<00:00, 16.96it/s]
MFD Predict:  72%|███████▏  | 36/50 [00:02<00:00, 16.88it/s]
MFD Predict:  76%|███████▌  | 38/50 [00:02<00:00, 16.72it/s]
MFD Predict:  80%|████████  | 40/50 [00:02<00:00, 16.72it/s]
MFD Predict:  84%|████████▍ | 42/50 [00:02<00:00, 16.64it/s]
MFD Predict:  88%|████████▊ | 44/50 [00:02<00:00, 16.57it/s]
MFD Predict:  92%|█████████▏| 46/50 [00:02<00:00, 16.53it/s]
MFD Predict:  96%|█████████▌| 48/50 [00:02<00:00, 16.51it/s]
MFD Predict: 100%|██████████| 50/50 [00:02<00:00, 16.52it/s]
MFD Predict: 100%|██████████| 50/50 [00:02<00:00, 17.07it/s]

MFR Predict:   0%|          | 0/57 [00:00<?, ?it/s]
MFR Predict:  56%|█████▌    | 32/57 [00:00<00:00, 162.05it/s]
MFR Predict: 100%|██████████| 57/57 [00:00<00:00, 76.70it/s] 
MFR Predict: 100%|██████████| 57/57 [00:00<00:00, 84.13it/s]

OCR-det ch:   0%|          | 0/242 [00:00<?, ?it/s]
OCR-det ch:   0%|          | 1/242 [00:00<00:53,  4.47it/s]
OCR-det ch:   2%|▏         | 5/242 [00:00<00:13, 17.40it/s]
OCR-det ch:   3%|▎         | 8/242 [00:00<00:14, 16.32it/s]
OCR-det ch:   5%|▍         | 12/242 [00:00<00:10, 22.09it/s]
OCR-det ch:   6%|▌         | 15/242 [00:00<00:09, 23.61it/s]
OCR-det ch:   8%|▊         | 19/242 [00:00<00:08, 26.11it/s]
OCR-det ch:  10%|▉         | 23/242 [00:01<00:07, 28.38it/s]
OCR-det ch:  12%|█▏        | 28/242 [00:01<00:06, 33.88it/s]
OCR-det ch:  13%|█▎        | 32/242 [00:01<00:07, 29.97it/s]
OCR-det ch:  15%|█▍        | 36/242 [00:01<00:06, 29.78it/s]
OCR-det ch:  17%|█▋        | 40/242 [00:01<00:08, 23.54it/s]
OCR-det ch:  18%|█▊        | 43/242 [00:01<00:08, 22.78it/s]
OCR-det ch:  19%|█▉        | 46/242 [00:01<00:08, 23.48it/s]
OCR-det ch:  21%|██        | 51/242 [00:02<00:06, 28.57it/s]
OCR-det ch:  23%|██▎       | 56/242 [00:02<00:05, 31.92it/s]
OCR-det ch:  25%|██▌       | 61/242 [00:02<00:05, 35.52it/s]
OCR-det ch:  27%|██▋       | 65/242 [00:02<00:05, 31.58it/s]
OCR-det ch:  29%|██▉       | 70/242 [00:02<00:05, 33.14it/s]
OCR-det ch:  31%|███       | 74/242 [00:02<00:05, 32.93it/s]
OCR-det ch:  32%|███▏      | 78/242 [00:02<00:05, 31.51it/s]
OCR-det ch:  34%|███▍      | 82/242 [00:03<00:05, 28.27it/s]
OCR-det ch:  36%|███▌      | 86/242 [00:03<00:05, 28.41it/s]
OCR-det ch:  37%|███▋      | 89/242 [00:03<00:05, 28.41it/s]
OCR-det ch:  38%|███▊      | 92/242 [00:03<00:05, 28.52it/s]
OCR-det ch:  39%|███▉      | 95/242 [00:03<00:05, 28.08it/s]
OCR-det ch:  41%|████      | 99/242 [00:03<00:06, 23.06it/s]
OCR-det ch:  43%|████▎     | 103/242 [00:03<00:05, 25.85it/s]
OCR-det ch:  44%|████▍     | 107/242 [00:03<00:04, 28.06it/s]
OCR-det ch:  45%|████▌     | 110/242 [00:04<00:04, 26.94it/s]
OCR-det ch:  47%|████▋     | 113/242 [00:04<00:06, 20.40it/s]
OCR-det ch:  48%|████▊     | 116/242 [00:04<00:07, 17.32it/s]
OCR-det ch:  49%|████▉     | 119/242 [00:04<00:08, 14.54it/s]
OCR-det ch:  50%|█████     | 121/242 [00:04<00:08, 14.55it/s]
OCR-det ch:  52%|█████▏    | 126/242 [00:05<00:05, 20.72it/s]
OCR-det ch:  54%|█████▎    | 130/242 [00:05<00:05, 21.05it/s]
OCR-det ch:  55%|█████▌    | 134/242 [00:05<00:04, 24.38it/s]
OCR-det ch:  57%|█████▋    | 138/242 [00:05<00:03, 26.11it/s]
OCR-det ch:  59%|█████▊    | 142/242 [00:05<00:03, 28.20it/s]
OCR-det ch:  60%|██████    | 146/242 [00:05<00:03, 26.89it/s]
OCR-det ch:  62%|██████▏   | 149/242 [00:05<00:04, 22.07it/s]
OCR-det ch:  63%|██████▎   | 153/242 [00:06<00:03, 25.34it/s]
OCR-det ch:  64%|██████▍   | 156/242 [00:06<00:03, 24.70it/s]
OCR-det ch:  66%|██████▌   | 160/242 [00:06<00:02, 27.60it/s]
OCR-det ch:  68%|██████▊   | 165/242 [00:06<00:02, 32.90it/s]
OCR-det ch:  70%|██████▉   | 169/242 [00:06<00:02, 33.44it/s]
OCR-det ch:  72%|███████▏  | 174/242 [00:06<00:01, 36.83it/s]
OCR-det ch:  74%|███████▍  | 179/242 [00:06<00:01, 38.15it/s]
OCR-det ch:  76%|███████▌  | 184/242 [00:06<00:01, 39.45it/s]
OCR-det ch:  78%|███████▊  | 189/242 [00:07<00:01, 35.61it/s]
OCR-det ch:  80%|████████  | 194/242 [00:07<00:01, 36.85it/s]
OCR-det ch:  82%|████████▏ | 198/242 [00:07<00:01, 35.98it/s]
OCR-det ch:  84%|████████▍ | 203/242 [00:07<00:01, 37.16it/s]
OCR-det ch:  86%|████████▌ | 207/242 [00:07<00:00, 37.49it/s]
OCR-det ch:  87%|████████▋ | 211/242 [00:07<00:00, 36.00it/s]
OCR-det ch:  89%|████████▉ | 216/242 [00:07<00:00, 38.12it/s]
OCR-det ch:  91%|█████████ | 220/242 [00:07<00:00, 34.78it/s]
OCR-det ch:  93%|█████████▎| 224/242 [00:08<00:00, 32.84it/s]
OCR-det ch:  95%|█████████▍| 229/242 [00:08<00:00, 34.81it/s]
OCR-det ch:  97%|█████████▋| 234/242 [00:08<00:00, 37.26it/s]
OCR-det ch:  98%|█████████▊| 238/242 [00:08<00:00, 36.41it/s]
OCR-det ch: 100%|██████████| 242/242 [00:08<00:00, 28.38it/s]

Table Predict:   0%|          | 0/16 [00:00<?, ?it/s]
Table Predict:   6%|▋         | 1/16 [00:00<00:06,  2.41it/s]
Table Predict:  12%|█▎        | 2/16 [00:01<00:10,  1.29it/s]
Table Predict:  19%|█▉        | 3/16 [00:01<00:06,  2.06it/s]
Table Predict:  25%|██▌       | 4/16 [00:01<00:04,  2.88it/s]
Table Predict:  31%|███▏      | 5/16 [00:01<00:03,  3.39it/s]
Table Predict:  38%|███▊      | 6/16 [00:02<00:02,  3.48it/s]
Table Predict:  44%|████▍     | 7/16 [00:02<00:02,  4.17it/s]
Table Predict:  50%|█████     | 8/16 [00:02<00:01,  4.35it/s]
Table Predict:  56%|█████▋    | 9/16 [00:02<00:01,  5.01it/s]
Table Predict:  62%|██████▎   | 10/16 [00:02<00:01,  4.95it/s]
Table Predict:  69%|██████▉   | 11/16 [00:03<00:00,  5.49it/s]
Table Predict:  75%|███████▌  | 12/16 [00:03<00:00,  5.91it/s]
Table Predict:  81%|████████▏ | 13/16 [00:03<00:00,  6.23it/s]
Table Predict:  88%|████████▊ | 14/16 [00:03<00:00,  4.76it/s]
Table Predict:  94%|█████████▍| 15/16 [00:03<00:00,  5.04it/s]
Table Predict: 100%|██████████| 16/16 [00:04<00:00,  4.44it/s]
Table Predict: 100%|██████████| 16/16 [00:04<00:00,  3.92it/s]

Processing pages:   0%|          | 0/50 [00:00<?, ?it/s]
Processing pages:   6%|▌         | 3/50 [00:00<00:02, 17.87it/s]
Processing pages:  10%|█         | 5/50 [00:00<00:02, 16.28it/s]
Processing pages:  14%|█▍        | 7/50 [00:00<00:02, 17.14it/s]
Processing pages:  18%|█▊        | 9/50 [00:00<00:02, 16.22it/s]
Processing pages:  22%|██▏       | 11/50 [00:00<00:02, 16.00it/s]
Processing pages:  26%|██▌       | 13/50 [00:00<00:02, 17.11it/s]
Processing pages:  30%|███       | 15/50 [00:00<00:02, 16.85it/s]
Processing pages:  34%|███▍      | 17/50 [00:01<00:02, 16.20it/s]
Processing pages:  38%|███▊      | 19/50 [00:01<00:01, 16.47it/s]
Processing pages:  42%|████▏     | 21/50 [00:01<00:01, 15.63it/s]
Processing pages:  46%|████▌     | 23/50 [00:01<00:01, 15.31it/s]
Processing pages:  50%|█████     | 25/50 [00:01<00:01, 15.12it/s]
Processing pages:  54%|█████▍    | 27/50 [00:01<00:01, 14.90it/s]
Processing pages:  58%|█████▊    | 29/50 [00:01<00:01, 14.82it/s]
Processing pages:  62%|██████▏   | 31/50 [00:01<00:01, 14.52it/s]2025-08-06 20:40:44.885 | WARNING  | mineru.utils.block_sort:sort_lines_by_model:115 - bottom > page_h, left: 30, right: 840, top: 402.66666666666674, bottom: 540.0000000000001, page_w: 960, page_h: 540

Processing pages:  66%|██████▌   | 33/50 [00:02<00:01, 15.19it/s]2025-08-06 20:40:45.023 | WARNING  | mineru.utils.block_sort:sort_lines_by_model:115 - bottom > page_h, left: 5, right: 819, top: 398.66666666666674, bottom: 540.0000000000001, page_w: 960, page_h: 540

Processing pages:  70%|███████   | 35/50 [00:02<00:01, 14.94it/s]
Processing pages:  74%|███████▍  | 37/50 [00:02<00:00, 14.79it/s]
Processing pages:  78%|███████▊  | 39/50 [00:02<00:00, 14.45it/s]
Processing pages:  82%|████████▏ | 41/50 [00:02<00:00, 15.70it/s]
Processing pages:  86%|████████▌ | 43/50 [00:02<00:00, 15.63it/s]
Processing pages:  90%|█████████ | 45/50 [00:02<00:00, 15.85it/s]
Processing pages:  94%|█████████▍| 47/50 [00:03<00:00, 15.77it/s]
Processing pages:  98%|█████████▊| 49/50 [00:03<00:00, 16.69it/s]
Processing pages: 100%|██████████| 50/50 [00:03<00:00, 15.89it/s]

OCR-rec Predict:   0%|          | 0/224 [00:00<?, ?it/s]
OCR-rec Predict:  36%|███▌      | 80/224 [00:00<00:00, 729.76it/s]
OCR-rec Predict:  71%|███████▏  | 160/224 [00:00<00:00, 741.23it/s]
OCR-rec Predict: 100%|██████████| 224/224 [00:00<00:00, 691.03it/s]
2025-08-06 20:40:46.649 | INFO     | mineru.cli.common:_process_output:156 - local output dir is E:\政企知识问答检索项目\PDF-OUTPUT/bf3913d1-5da0-4b70-a026-5f90e5bebab5/1、中国移动OnePark智慧园区解决方案Ver24.06/auto
INFO:     111.4.10.85:14143 - "POST /file_parse HTTP/1.1" 200 OK
