#!/usr/bin/env python3
"""
MinerU API 调用示例集合
服务器地址: http://************:18010
"""

import requests
import json
import base64

# API配置
API_BASE_URL = "http://************:18010"

def example_1_simple_pdf_parse():
    """示例1: 简单PDF解析"""
    print("📄 示例1: 简单PDF解析")
    
    # 准备文件
    file_path = "document.pdf"  # 替换为您的PDF文件路径
    
    try:
        with open(file_path, 'rb') as f:
            files = {'files': ('document.pdf', f, 'application/pdf')}
            data = {
                'output_dir': './output',
                'lang_list': ['ch'],  # 中文
                'backend': 'pipeline',
                'return_md': True
            }
            
            response = requests.post(
                f"{API_BASE_URL}/file_parse",
                files=files,
                data=data,
                timeout=300
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 解析成功!")
                print("📋 Markdown内容:")
                for filename, content in result['results'].items():
                    print(f"文件: {filename}")
                    print(content.get('md_content', ''))
            else:
                print(f"❌ 解析失败: {response.status_code}")
                
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
    except Exception as e:
        print(f"❌ 错误: {e}")

def example_2_advanced_pdf_parse():
    """示例2: 高级PDF解析（包含表格和公式）"""
    print("\n📊 示例2: 高级PDF解析")
    
    file_path = "complex_document.pdf"  # 替换为您的PDF文件路径
    
    try:
        with open(file_path, 'rb') as f:
            files = {'files': ('complex_document.pdf', f, 'application/pdf')}
            data = {
                'output_dir': './output',
                'lang_list': ['ch', 'en'],  # 中英文混合
                'backend': 'pipeline',
                'parse_method': 'auto',
                'formula_enable': True,     # 启用公式识别
                'table_enable': True,       # 启用表格识别
                'return_md': True,
                'return_middle_json': True, # 返回中间JSON
                'return_content_list': True # 返回内容列表
            }
            
            response = requests.post(
                f"{API_BASE_URL}/file_parse",
                files=files,
                data=data,
                timeout=300
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 高级解析成功!")
                
                for filename, content in result['results'].items():
                    print(f"\n📄 文件: {filename}")
                    
                    if 'md_content' in content:
                        print("📝 Markdown内容:")
                        print(content['md_content'][:500] + "...")
                    
                    if 'middle_json' in content:
                        print("🔧 中间处理数据可用")
                    
                    if 'content_list' in content:
                        print("📋 内容列表可用")
            else:
                print(f"❌ 解析失败: {response.status_code}")
                
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
    except Exception as e:
        print(f"❌ 错误: {e}")

def example_3_image_ocr():
    """示例3: 图片OCR识别"""
    print("\n🖼️ 示例3: 图片OCR识别")
    
    image_path = "document_image.png"  # 替换为您的图片文件路径
    
    try:
        with open(image_path, 'rb') as f:
            files = {'files': ('document_image.png', f, 'image/png')}
            data = {
                'output_dir': './output',
                'lang_list': ['ch'],
                'backend': 'pipeline',
                'parse_method': 'ocr',  # 强制使用OCR
                'return_md': True
            }
            
            response = requests.post(
                f"{API_BASE_URL}/file_parse",
                files=files,
                data=data,
                timeout=300
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 图片OCR成功!")
                
                for filename, content in result['results'].items():
                    print(f"📄 文件: {filename}")
                    print("📝 识别文本:")
                    print(content.get('md_content', ''))
            else:
                print(f"❌ OCR失败: {response.status_code}")
                
    except FileNotFoundError:
        print(f"❌ 文件不存在: {image_path}")
    except Exception as e:
        print(f"❌ 错误: {e}")

def example_4_batch_processing():
    """示例4: 批量处理多个文件"""
    print("\n📚 示例4: 批量处理")
    
    file_paths = ["doc1.pdf", "doc2.pdf", "image1.png"]  # 替换为您的文件列表
    
    for file_path in file_paths:
        try:
            print(f"\n处理文件: {file_path}")
            
            # 检测文件类型
            if file_path.lower().endswith('.pdf'):
                content_type = 'application/pdf'
            elif file_path.lower().endswith(('.png', '.jpg', '.jpeg')):
                content_type = f'image/{file_path.split(".")[-1]}'
            else:
                print(f"❌ 不支持的文件类型: {file_path}")
                continue
            
            with open(file_path, 'rb') as f:
                files = {'files': (file_path, f, content_type)}
                data = {
                    'output_dir': f'./output/{file_path.split(".")[0]}',
                    'lang_list': ['ch'],
                    'backend': 'pipeline',
                    'return_md': True
                }
                
                response = requests.post(
                    f"{API_BASE_URL}/file_parse",
                    files=files,
                    data=data,
                    timeout=300
                )
                
                if response.status_code == 200:
                    print(f"✅ {file_path} 处理成功!")
                else:
                    print(f"❌ {file_path} 处理失败: {response.status_code}")
                    
        except FileNotFoundError:
            print(f"❌ 文件不存在: {file_path}")
        except Exception as e:
            print(f"❌ 处理 {file_path} 时出错: {e}")

def example_5_curl_commands():
    """示例5: 等效的curl命令"""
    print("\n🔧 示例5: 等效的curl命令")
    
    print("基本PDF解析:")
    print(f"""curl -X POST "{API_BASE_URL}/file_parse" \\
  -F "files=@document.pdf" \\
  -F "output_dir=./output" \\
  -F "lang_list=ch" \\
  -F "backend=pipeline" \\
  -F "return_md=true"
""")
    
    print("高级解析（包含表格和公式）:")
    print(f"""curl -X POST "{API_BASE_URL}/file_parse" \\
  -F "files=@document.pdf" \\
  -F "output_dir=./output" \\
  -F "lang_list=ch" \\
  -F "lang_list=en" \\
  -F "backend=pipeline" \\
  -F "formula_enable=true" \\
  -F "table_enable=true" \\
  -F "return_md=true" \\
  -F "return_middle_json=true"
""")
    
    print("图片OCR:")
    print(f"""curl -X POST "{API_BASE_URL}/file_parse" \\
  -F "files=@image.png" \\
  -F "output_dir=./output" \\
  -F "lang_list=ch" \\
  -F "parse_method=ocr" \\
  -F "return_md=true"
""")

def main():
    """主函数"""
    print("🚀 MinerU API 调用示例集合")
    print(f"📍 API地址: {API_BASE_URL}")
    print(f"📖 API文档: {API_BASE_URL}/docs")
    print("=" * 60)
    
    # 运行示例（注释掉不需要的示例）
    # example_1_simple_pdf_parse()
    # example_2_advanced_pdf_parse()
    # example_3_image_ocr()
    # example_4_batch_processing()
    example_5_curl_commands()
    
    print("\n" + "=" * 60)
    print("🎯 示例展示完成!")
    print("💡 取消注释相应的示例函数来运行实际测试")

if __name__ == "__main__":
    main()
