/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package connector

import (
	"context"

	"github.com/coze-dev/coze-studio/backend/domain/connector/entity"
	connector "github.com/coze-dev/coze-studio/backend/domain/connector/service"
	"github.com/coze-dev/coze-studio/backend/infra/contract/storage"
)

type ConnectorApplicationService struct {
	DomainSVC connector.Connector
}

var ConnectorApplicationSVC *ConnectorApplicationService

func New(domainSVC connector.Connector, tosClient storage.Storage) *ConnectorApplicationService {
	return &ConnectorApplicationService{
		DomainSVC: domainSVC,
	}
}

func (c *ConnectorApplicationService) List(ctx context.Context) ([]*entity.Connector, error) {
	return c.DomainSVC.List(ctx)
}
