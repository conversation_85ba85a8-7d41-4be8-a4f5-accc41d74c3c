// Code generated by hertz generator. DO NOT EDIT.

package coze

import (
	"github.com/cloudwego/hertz/pkg/app/server"
	coze "github.com/coze-dev/coze-studio/backend/api/handler/coze"
)

/*
 This file will register all the routes of the services in the master idl.
 And it will update automatically when you use the "update" command for the idl.
 So don't modify the contents of the file, or your code will be deleted when it is updated.
*/

// Register register routes based on the IDL 'api.${HTTP Method}' annotation.
func Register(r *server.Hertz) {

	root := r.Group("/", rootMw()...)
	{
		_api := root.Group("/api", _apiMw()...)
		{
			_bot := _api.Group("/bot", _botMw()...)
			_bot.POST("/get_type_list", append(_gettypelistMw(), coze.GetTypeList)...)
			_bot.POST("/upload_file", append(_uploadfileMw(), coze.UploadFile)...)
		}
		{
			_common := _api.Group("/common", _commonMw()...)
			{
				_upload := _common.Group("/upload", _uploadMw()...)
				_upload.GET("/apply_upload_action", append(_applyuploadactionMw(), coze.ApplyUploadAction)...)
				_upload.POST("/apply_upload_action", append(_applyuploadaction0Mw(), coze.ApplyUploadAction)...)
				_upload.POST("/*tos_uri", append(_commonuploadMw(), coze.CommonUpload)...)
			}
		}
		{
			_conversation := _api.Group("/conversation", _conversationMw()...)
			_conversation.POST("/break_message", append(_breakmessageMw(), coze.BreakMessage)...)
			_conversation.POST("/chat", append(_agentrunMw(), coze.AgentRun)...)
			_conversation.POST("/clear_message", append(_clearconversationhistoryMw(), coze.ClearConversationHistory)...)
			_conversation.POST("/create_section", append(_clearconversationctxMw(), coze.ClearConversationCtx)...)
			_conversation.POST("/delete_message", append(_deletemessageMw(), coze.DeleteMessage)...)
			_conversation.POST("/get_message_list", append(_getmessagelistMw(), coze.GetMessageList)...)
		}
		{
			_developer := _api.Group("/developer", _developerMw()...)
			_developer.POST("/get_icon", append(_geticonMw(), coze.GetIcon)...)
		}
		{
			_draftbot := _api.Group("/draftbot", _draftbotMw()...)
			_draftbot.POST("/commit_check", append(_checkdraftbotcommitMw(), coze.CheckDraftBotCommit)...)
			_draftbot.POST("/create", append(_draftbotcreateMw(), coze.DraftBotCreate)...)
			_draftbot.POST("/delete", append(_deletedraftbotMw(), coze.DeleteDraftBot)...)
			_draftbot.POST("/duplicate", append(_duplicatedraftbotMw(), coze.DuplicateDraftBot)...)
			_draftbot.POST("/get_display_info", append(_getdraftbotdisplayinfoMw(), coze.GetDraftBotDisplayInfo)...)
			_draftbot.POST("/list_draft_history", append(_listdraftbothistoryMw(), coze.ListDraftBotHistory)...)
			_draftbot.POST("/publish", append(_publishdraftbotMw(), coze.PublishDraftBot)...)
			_draftbot.POST("/update_display_info", append(_updatedraftbotdisplayinfoMw(), coze.UpdateDraftBotDisplayInfo)...)
			{
				_publish := _draftbot.Group("/publish", _publishMw()...)
				{
					_connector := _publish.Group("/connector", _connectorMw()...)
					_connector.POST("/list", append(_publishconnectorlistMw(), coze.PublishConnectorList)...)
				}
			}
		}
		{
			_intelligence_api := _api.Group("/intelligence_api", _intelligence_apiMw()...)
			{
				_draft_project := _intelligence_api.Group("/draft_project", _draft_projectMw()...)
				_draft_project.POST("/copy", append(_draftprojectcopyMw(), coze.DraftProjectCopy)...)
				_draft_project.POST("/create", append(_draftprojectcreateMw(), coze.DraftProjectCreate)...)
				_draft_project.POST("/delete", append(_draftprojectdeleteMw(), coze.DraftProjectDelete)...)
				_draft_project.POST("/inner_task_list", append(_draftprojectinnertasklistMw(), coze.DraftProjectInnerTaskList)...)
				_draft_project.POST("/update", append(_draftprojectupdateMw(), coze.DraftProjectUpdate)...)
			}
			{
				_publish0 := _intelligence_api.Group("/publish", _publish0Mw()...)
				_publish0.POST("/check_version_number", append(_checkprojectversionnumberMw(), coze.CheckProjectVersionNumber)...)
				_publish0.POST("/connector_list", append(_projectpublishconnectorlistMw(), coze.ProjectPublishConnectorList)...)
				_publish0.POST("/get_published_connector", append(_getprojectpublishedconnectorMw(), coze.GetProjectPublishedConnector)...)
				_publish0.POST("/publish_project", append(_publishprojectMw(), coze.PublishProject)...)
				_publish0.POST("/publish_record_detail", append(_getpublishrecorddetailMw(), coze.GetPublishRecordDetail)...)
				_publish0.POST("/publish_record_list", append(_getpublishrecordlistMw(), coze.GetPublishRecordList)...)
			}
			{
				_search := _intelligence_api.Group("/search", _searchMw()...)
				_search.POST("/get_draft_intelligence_info", append(_getdraftintelligenceinfoMw(), coze.GetDraftIntelligenceInfo)...)
				_search.POST("/get_draft_intelligence_list", append(_getdraftintelligencelistMw(), coze.GetDraftIntelligenceList)...)
				_search.POST("/get_recently_edit_intelligence", append(_getuserrecentlyeditintelligenceMw(), coze.GetUserRecentlyEditIntelligence)...)
			}
		}
		{
			_knowledge := _api.Group("/knowledge", _knowledgeMw()...)
			_knowledge.POST("/create", append(_createdatasetMw(), coze.CreateDataset)...)
			_knowledge.POST("/delete", append(_deletedatasetMw(), coze.DeleteDataset)...)
			_knowledge.POST("/detail", append(_datasetdetailMw(), coze.DatasetDetail)...)
			_knowledge.POST("/list", append(_listdatasetMw(), coze.ListDataset)...)
			_knowledge.POST("/update", append(_updatedatasetMw(), coze.UpdateDataset)...)
			{
				_document := _knowledge.Group("/document", _documentMw()...)
				_document.POST("/create", append(_createdocumentMw(), coze.CreateDocument)...)
				_document.POST("/delete", append(_deletedocumentMw(), coze.DeleteDocument)...)
				_document.POST("/list", append(_listdocumentMw(), coze.ListDocument)...)
				_document.POST("/resegment", append(_resegmentMw(), coze.Resegment)...)
				_document.POST("/update", append(_updatedocumentMw(), coze.UpdateDocument)...)
				{
					_progress := _document.Group("/progress", _progressMw()...)
					_progress.POST("/get", append(_getdocumentprogressMw(), coze.GetDocumentProgress)...)
				}
			}
			{
				_icon := _knowledge.Group("/icon", _iconMw()...)
				_icon.POST("/get", append(_geticonfordatasetMw(), coze.GetIconForDataset)...)
			}
			{
				_photo := _knowledge.Group("/photo", _photoMw()...)
				_photo.POST("/caption", append(_updatephotocaptionMw(), coze.UpdatePhotoCaption)...)
				_photo.POST("/detail", append(_photodetailMw(), coze.PhotoDetail)...)
				_photo.POST("/extract_caption", append(_extractphotocaptionMw(), coze.ExtractPhotoCaption)...)
				_photo.POST("/list", append(_listphotoMw(), coze.ListPhoto)...)
			}
			{
				_review := _knowledge.Group("/review", _reviewMw()...)
				_review.POST("/create", append(_createdocumentreviewMw(), coze.CreateDocumentReview)...)
				_review.POST("/mget", append(_mgetdocumentreviewMw(), coze.MGetDocumentReview)...)
				_review.POST("/save", append(_savedocumentreviewMw(), coze.SaveDocumentReview)...)
			}
			{
				_slice := _knowledge.Group("/slice", _sliceMw()...)
				_slice.POST("/create", append(_createsliceMw(), coze.CreateSlice)...)
				_slice.POST("/delete", append(_deletesliceMw(), coze.DeleteSlice)...)
				_slice.POST("/list", append(_listsliceMw(), coze.ListSlice)...)
				_slice.POST("/update", append(_updatesliceMw(), coze.UpdateSlice)...)
			}
			{
				_table_schema := _knowledge.Group("/table_schema", _table_schemaMw()...)
				_table_schema.POST("/get", append(_gettableschemaMw(), coze.GetTableSchema)...)
				_table_schema.POST("/validate", append(_validatetableschemaMw(), coze.ValidateTableSchema)...)
			}
		}
		{
			_marketplace := _api.Group("/marketplace", _marketplaceMw()...)
			{
				_product := _marketplace.Group("/product", _productMw()...)
				_product.GET("/detail", append(_publicgetproductdetailMw(), coze.PublicGetProductDetail)...)
				_product.POST("/duplicate", append(_publicduplicateproductMw(), coze.PublicDuplicateProduct)...)
				_product.POST("/favorite", append(_publicfavoriteproductMw(), coze.PublicFavoriteProduct)...)
				_favorite := _product.Group("/favorite", _favoriteMw()...)
				_favorite.GET("/list.v2", append(_publicgetuserfavoritelistv2Mw(), coze.PublicGetUserFavoriteListV2)...)
				_product.GET("/list", append(_publicgetproductlistMw(), coze.PublicGetProductList)...)
			}
		}
		{
			_memory := _api.Group("/memory", _memoryMw()...)
			_memory.GET("/doc_table_info", append(_getdocumenttableinfoMw(), coze.GetDocumentTableInfo)...)
			_memory.GET("/sys_variable_conf", append(_getsysvariableconfMw(), coze.GetSysVariableConf)...)
			_memory.GET("/table_mode_config", append(_getmodeconfigMw(), coze.GetModeConfig)...)
			{
				_database := _memory.Group("/database", _databaseMw()...)
				_database.POST("/add", append(_adddatabaseMw(), coze.AddDatabase)...)
				_database.POST("/bind_to_bot", append(_binddatabaseMw(), coze.BindDatabase)...)
				_database.POST("/delete", append(_deletedatabaseMw(), coze.DeleteDatabase)...)
				_database.POST("/get_by_id", append(_getdatabasebyidMw(), coze.GetDatabaseByID)...)
				_database.POST("/get_connector_name", append(_getconnectornameMw(), coze.GetConnectorName)...)
				_database.POST("/get_online_database_id", append(_getonlinedatabaseidMw(), coze.GetOnlineDatabaseId)...)
				_database.POST("/get_template", append(_getdatabasetemplateMw(), coze.GetDatabaseTemplate)...)
				_database.POST("/list", append(_listdatabaseMw(), coze.ListDatabase)...)
				_database.POST("/list_records", append(_listdatabaserecordsMw(), coze.ListDatabaseRecords)...)
				_database.POST("/unbind_to_bot", append(_unbinddatabaseMw(), coze.UnBindDatabase)...)
				_database.POST("/update", append(_updatedatabaseMw(), coze.UpdateDatabase)...)
				_database.POST("/update_bot_switch", append(_updatedatabasebotswitchMw(), coze.UpdateDatabaseBotSwitch)...)
				_database.POST("/update_records", append(_updatedatabaserecordsMw(), coze.UpdateDatabaseRecords)...)
				{
					_table := _database.Group("/table", _tableMw()...)
					_table.POST("/list_new", append(_getbotdatabaseMw(), coze.GetBotDatabase)...)
					_table.POST("/reset", append(_resetbottableMw(), coze.ResetBotTable)...)
				}
			}
			{
				_project := _memory.Group("/project", _projectMw()...)
				{
					_variable := _project.Group("/variable", _variableMw()...)
					_variable.GET("/meta_list", append(_getprojectvariablelistMw(), coze.GetProjectVariableList)...)
					_variable.POST("/meta_update", append(_updateprojectvariableMw(), coze.UpdateProjectVariable)...)
				}
			}
			{
				_table_file := _memory.Group("/table_file", _table_fileMw()...)
				_table_file.POST("/get_progress", append(_databasefileprogressdataMw(), coze.DatabaseFileProgressData)...)
				_table_file.POST("/submit", append(_submitdatabaseinserttaskMw(), coze.SubmitDatabaseInsertTask)...)
			}
			{
				_table_schema0 := _memory.Group("/table_schema", _table_schema0Mw()...)
				_table_schema0.POST("/get", append(_getdatabasetableschemaMw(), coze.GetDatabaseTableSchema)...)
				_table_schema0.POST("/validate", append(_validatedatabasetableschemaMw(), coze.ValidateDatabaseTableSchema)...)
			}
			{
				_variable0 := _memory.Group("/variable", _variable0Mw()...)
				_variable0.POST("/delete", append(_delprofilememoryMw(), coze.DelProfileMemory)...)
				_variable0.POST("/get", append(_getplaygroundmemoryMw(), coze.GetPlayGroundMemory)...)
				_variable0.POST("/get_meta", append(_getmemoryvariablemetaMw(), coze.GetMemoryVariableMeta)...)
				_variable0.POST("/upsert", append(_setkvmemoryMw(), coze.SetKvMemory)...)
			}
		}
		{
			_oauth := _api.Group("/oauth", _oauthMw()...)
			_oauth.GET("/authorization_code", append(_oauthauthorizationcodeMw(), coze.OauthAuthorizationCode)...)
		}
		{
			_passport := _api.Group("/passport", _passportMw()...)
			{
				_account := _passport.Group("/account", _accountMw()...)
				{
					_info := _account.Group("/info", _infoMw()...)
					{
						_v2 := _info.Group("/v2", _v2Mw()...)
						_v2.POST("/", append(_passportaccountinfov2Mw(), coze.PassportAccountInfoV2)...)
					}
				}
			}
			{
				_web := _passport.Group("/web", _webMw()...)
				{
					_email := _web.Group("/email", _emailMw()...)
					{
						_login := _email.Group("/login", _loginMw()...)
						_login.POST("/", append(_passportwebemailloginpostMw(), coze.PassportWebEmailLoginPost)...)
					}
					{
						_password := _email.Group("/password", _passwordMw()...)
						{
							_reset := _password.Group("/reset", _resetMw()...)
							_reset.GET("/", append(_passportwebemailpasswordresetgetMw(), coze.PassportWebEmailPasswordResetGet)...)
						}
					}
					{
						_register := _email.Group("/register", _registerMw()...)
						{
							_v20 := _register.Group("/v2", _v20Mw()...)
							_v20.POST("/", append(_passportwebemailregisterv2postMw(), coze.PassportWebEmailRegisterV2Post)...)
						}
					}
				}
				{
					_logout := _web.Group("/logout", _logoutMw()...)
					_logout.GET("/", append(_passportweblogoutgetMw(), coze.PassportWebLogoutGet)...)
				}
			}
		}
		{
			_permission_api := _api.Group("/permission_api", _permission_apiMw()...)
			{
				_pat := _permission_api.Group("/pat", _patMw()...)
				_pat.POST("/create_personal_access_token_and_permission", append(_createpersonalaccesstokenandpermissionMw(), coze.CreatePersonalAccessTokenAndPermission)...)
				_pat.POST("/delete_personal_access_token_and_permission", append(_deletepersonalaccesstokenandpermissionMw(), coze.DeletePersonalAccessTokenAndPermission)...)
				_pat.GET("/get_personal_access_token_and_permission", append(_getpersonalaccesstokenandpermissionMw(), coze.GetPersonalAccessTokenAndPermission)...)
				_pat.GET("/list_personal_access_tokens", append(_listpersonalaccesstokensMw(), coze.ListPersonalAccessTokens)...)
				_pat.POST("/update_personal_access_token_and_permission", append(_updatepersonalaccesstokenandpermissionMw(), coze.UpdatePersonalAccessTokenAndPermission)...)
			}
		}
		{
			_playground := _api.Group("/playground", _playgroundMw()...)
			_playground.POST("/get_onboarding", append(_getonboardingMw(), coze.GetOnboarding)...)
			{
				_upload0 := _playground.Group("/upload", _upload0Mw()...)
				_upload0.POST("/auth_token", append(_getuploadauthtokenMw(), coze.GetUploadAuthToken)...)
			}
		}
		{
			_playground_api := _api.Group("/playground_api", _playground_apiMw()...)
			_playground_api.POST("/create_update_shortcut_command", append(_createupdateshortcutcommandMw(), coze.CreateUpdateShortcutCommand)...)
			_playground_api.POST("/delete_prompt_resource", append(_deletepromptresourceMw(), coze.DeletePromptResource)...)
			_playground_api.POST("/get_file_list", append(_getfileurlsMw(), coze.GetFileUrls)...)
			_playground_api.POST("/get_imagex_url", append(_getimagexshorturlMw(), coze.GetImagexShortUrl)...)
			_playground_api.POST("/get_official_prompt_list", append(_getofficialpromptresourcelistMw(), coze.GetOfficialPromptResourceList)...)
			_playground_api.GET("/get_prompt_resource_info", append(_getpromptresourceinfoMw(), coze.GetPromptResourceInfo)...)
			_playground_api.POST("/mget_user_info", append(_mgetuserbasicinfoMw(), coze.MGetUserBasicInfo)...)
			_playground_api.POST("/report_user_behavior", append(_reportuserbehaviorMw(), coze.ReportUserBehavior)...)
			_playground_api.POST("/upsert_prompt_resource", append(_upsertpromptresourceMw(), coze.UpsertPromptResource)...)
			{
				_draftbot0 := _playground_api.Group("/draftbot", _draftbot0Mw()...)
				_draftbot0.POST("/get_draft_bot_info", append(_getdraftbotinfoagwMw(), coze.GetDraftBotInfoAgw)...)
				_draftbot0.POST("/update_draft_bot_info", append(_updatedraftbotinfoagwMw(), coze.UpdateDraftBotInfoAgw)...)
			}
			{
				_operate := _playground_api.Group("/operate", _operateMw()...)
				_operate.POST("/get_bot_popup_info", append(_getbotpopupinfoMw(), coze.GetBotPopupInfo)...)
				_operate.POST("/update_bot_popup_info", append(_updatebotpopupinfoMw(), coze.UpdateBotPopupInfo)...)
			}
			{
				_space := _playground_api.Group("/space", _spaceMw()...)
				_space.POST("/list", append(_getspacelistv2Mw(), coze.GetSpaceListV2)...)
			}
		}
		{
			_plugin := _api.Group("/plugin", _pluginMw()...)
			_plugin.POST("/get_oauth_schema", append(_getoauthschemaMw(), coze.GetOAuthSchema)...)
		}
		{
			_plugin_api := _api.Group("/plugin_api", _plugin_apiMw()...)
			_plugin_api.POST("/batch_create_api", append(_batchcreateapiMw(), coze.BatchCreateAPI)...)
			_plugin_api.POST("/check_and_lock_plugin_edit", append(_checkandlockplugineditMw(), coze.CheckAndLockPluginEdit)...)
			_plugin_api.POST("/convert_to_openapi", append(_convert2openapiMw(), coze.Convert2OpenAPI)...)
			_plugin_api.POST("/create_api", append(_createapiMw(), coze.CreateAPI)...)
			_plugin_api.POST("/debug_api", append(_debugapiMw(), coze.DebugAPI)...)
			_plugin_api.POST("/del_plugin", append(_delpluginMw(), coze.DelPlugin)...)
			_plugin_api.POST("/delete_api", append(_deleteapiMw(), coze.DeleteAPI)...)
			_plugin_api.POST("/get_bot_default_params", append(_getbotdefaultparamsMw(), coze.GetBotDefaultParams)...)
			_plugin_api.POST("/get_dev_plugin_list", append(_getdevpluginlistMw(), coze.GetDevPluginList)...)
			_plugin_api.POST("/get_oauth_schema", append(_getoauthschemaapiMw(), coze.GetOAuthSchemaAPI)...)
			_plugin_api.POST("/get_oauth_status", append(_getoauthstatusMw(), coze.GetOAuthStatus)...)
			_plugin_api.POST("/get_playground_plugin_list", append(_getplaygroundpluginlistMw(), coze.GetPlaygroundPluginList)...)
			_plugin_api.POST("/get_plugin_apis", append(_getpluginapisMw(), coze.GetPluginAPIs)...)
			_plugin_api.POST("/get_plugin_info", append(_getplugininfoMw(), coze.GetPluginInfo)...)
			_plugin_api.POST("/get_plugin_next_version", append(_getpluginnextversionMw(), coze.GetPluginNextVersion)...)
			_plugin_api.POST("/get_queried_oauth_plugins", append(_getqueriedoauthpluginlistMw(), coze.GetQueriedOAuthPluginList)...)
			_plugin_api.POST("/get_updated_apis", append(_getupdatedapisMw(), coze.GetUpdatedAPIs)...)
			_plugin_api.POST("/get_user_authority", append(_getuserauthorityMw(), coze.GetUserAuthority)...)
			_plugin_api.POST("/library_resource_list", append(_libraryresourcelistMw(), coze.LibraryResourceList)...)
			_plugin_api.POST("/project_resource_list", append(_projectresourcelistMw(), coze.ProjectResourceList)...)
			_plugin_api.POST("/publish_plugin", append(_publishpluginMw(), coze.PublishPlugin)...)
			_plugin_api.POST("/register", append(_registerpluginMw(), coze.RegisterPlugin)...)
			_plugin_api.POST("/register_plugin_meta", append(_registerpluginmetaMw(), coze.RegisterPluginMeta)...)
			_plugin_api.POST("/resource_copy_cancel", append(_resourcecopycancelMw(), coze.ResourceCopyCancel)...)
			_plugin_api.POST("/resource_copy_detail", append(_resourcecopydetailMw(), coze.ResourceCopyDetail)...)
			_plugin_api.POST("/resource_copy_dispatch", append(_resourcecopydispatchMw(), coze.ResourceCopyDispatch)...)
			_plugin_api.POST("/resource_copy_retry", append(_resourcecopyretryMw(), coze.ResourceCopyRetry)...)
			_plugin_api.POST("/revoke_auth_token", append(_revokeauthtokenMw(), coze.RevokeAuthToken)...)
			_plugin_api.POST("/unlock_plugin_edit", append(_unlockplugineditMw(), coze.UnlockPluginEdit)...)
			_plugin_api.POST("/update", append(_updatepluginMw(), coze.UpdatePlugin)...)
			_plugin_api.POST("/update_api", append(_updateapiMw(), coze.UpdateAPI)...)
			_plugin_api.POST("/update_bot_default_params", append(_updatebotdefaultparamsMw(), coze.UpdateBotDefaultParams)...)
			_plugin_api.POST("/update_plugin_meta", append(_updatepluginmetaMw(), coze.UpdatePluginMeta)...)
		}
		{
			_user := _api.Group("/user", _userMw()...)
			_user.POST("/update_profile", append(_userupdateprofileMw(), coze.UserUpdateProfile)...)
			_user.POST("/update_profile_check", append(_updateuserprofilecheckMw(), coze.UpdateUserProfileCheck)...)
		}
		{
			_web0 := _api.Group("/web", _web0Mw()...)
			{
				_user0 := _web0.Group("/user", _user0Mw()...)
				{
					_update := _user0.Group("/update", _updateMw()...)
					{
						_upload_avatar := _update.Group("/upload_avatar", _upload_avatarMw()...)
						_upload_avatar.POST("/", append(_userupdateavatarMw(), coze.UserUpdateAvatar)...)
					}
				}
			}
		}
		{
			_workflow_api := _api.Group("/workflow_api", _workflow_apiMw()...)
			_workflow_api.GET("/apiDetail", append(_getapidetailMw(), coze.GetApiDetail)...)
			_workflow_api.POST("/batch_delete", append(_batchdeleteworkflowMw(), coze.BatchDeleteWorkflow)...)
			_workflow_api.POST("/cancel", append(_cancelworkflowMw(), coze.CancelWorkFlow)...)
			_workflow_api.POST("/canvas", append(_getcanvasinfoMw(), coze.GetCanvasInfo)...)
			_workflow_api.POST("/copy", append(_copyworkflowMw(), coze.CopyWorkflow)...)
			_workflow_api.POST("/copy_wk_template", append(_copywktemplateapiMw(), coze.CopyWkTemplateApi)...)
			_workflow_api.POST("/create", append(_createworkflowMw(), coze.CreateWorkflow)...)
			_workflow_api.POST("/delete", append(_deleteworkflowMw(), coze.DeleteWorkflow)...)
			_workflow_api.POST("/delete_strategy", append(_getdeletestrategyMw(), coze.GetDeleteStrategy)...)
			_workflow_api.POST("/example_workflow_list", append(_getexampleworkflowlistMw(), coze.GetExampleWorkFlowList)...)
			_workflow_api.GET("/get_node_execute_history", append(_getnodeexecutehistoryMw(), coze.GetNodeExecuteHistory)...)
			_workflow_api.GET("/get_process", append(_getworkflowprocessMw(), coze.GetWorkFlowProcess)...)
			_workflow_api.POST("/get_trace", append(_gettracesdkMw(), coze.GetTraceSDK)...)
			_workflow_api.POST("/history_schema", append(_gethistoryschemaMw(), coze.GetHistorySchema)...)
			_workflow_api.POST("/list_publish_workflow", append(_listpublishworkflowMw(), coze.ListPublishWorkflow)...)
			_workflow_api.POST("/list_spans", append(_listrootspansMw(), coze.ListRootSpans)...)
			_workflow_api.POST("/llm_fc_setting_detail", append(_getllmnodefcsettingdetailMw(), coze.GetLLMNodeFCSettingDetail)...)
			_workflow_api.POST("/llm_fc_setting_merged", append(_getllmnodefcsettingsmergedMw(), coze.GetLLMNodeFCSettingsMerged)...)
			_workflow_api.POST("/nodeDebug", append(_workflownodedebugv2Mw(), coze.WorkflowNodeDebugV2)...)
			_workflow_api.POST("/node_panel_search", append(_nodepanelsearchMw(), coze.NodePanelSearch)...)
			_workflow_api.POST("/node_template_list", append(_nodetemplatelistMw(), coze.NodeTemplateList)...)
			_workflow_api.POST("/node_type", append(_queryworkflownodetypesMw(), coze.QueryWorkflowNodeTypes)...)
			_workflow_api.POST("/publish", append(_publishworkflowMw(), coze.PublishWorkflow)...)
			_workflow_api.POST("/released_workflows", append(_getreleasedworkflowsMw(), coze.GetReleasedWorkflows)...)
			_workflow_api.POST("/save", append(_saveworkflowMw(), coze.SaveWorkflow)...)
			_workflow_api.POST("/sign_image_url", append(_signimageurlMw(), coze.SignImageURL)...)
			_workflow_api.POST("/test_resume", append(_workflowtestresumeMw(), coze.WorkFlowTestResume)...)
			_workflow_api.POST("/test_run", append(_workflowtestrunMw(), coze.WorkFlowTestRun)...)
			_workflow_api.POST("/update_meta", append(_updateworkflowmetaMw(), coze.UpdateWorkflowMeta)...)
			_workflow_api.POST("/validate_tree", append(_validatetreeMw(), coze.ValidateTree)...)
			_workflow_api.POST("/workflow_detail", append(_getworkflowdetailMw(), coze.GetWorkflowDetail)...)
			_workflow_api.POST("/workflow_detail_info", append(_getworkflowdetailinfoMw(), coze.GetWorkflowDetailInfo)...)
			_workflow_api.POST("/workflow_list", append(_getworkflowlistMw(), coze.GetWorkFlowList)...)
			_workflow_api.POST("/workflow_references", append(_getworkflowreferencesMw(), coze.GetWorkflowReferences)...)
			{
				_chat_flow_role := _workflow_api.Group("/chat_flow_role", _chat_flow_roleMw()...)
				_chat_flow_role.POST("/create", append(_createchatflowroleMw(), coze.CreateChatFlowRole)...)
				_chat_flow_role.POST("/delete", append(_deletechatflowroleMw(), coze.DeleteChatFlowRole)...)
				_chat_flow_role.GET("/get", append(_getchatflowroleMw(), coze.GetChatFlowRole)...)
			}
			{
				_project_conversation := _workflow_api.Group("/project_conversation", _project_conversationMw()...)
				_project_conversation.POST("/create", append(_createprojectconversationdefMw(), coze.CreateProjectConversationDef)...)
				_project_conversation.POST("/delete", append(_deleteprojectconversationdefMw(), coze.DeleteProjectConversationDef)...)
				_project_conversation.GET("/list", append(_listprojectconversationdefMw(), coze.ListProjectConversationDef)...)
				_project_conversation.POST("/update", append(_updateprojectconversationdefMw(), coze.UpdateProjectConversationDef)...)
			}
			{
				_upload1 := _workflow_api.Group("/upload", _upload1Mw()...)
				_upload1.POST("/auth_token", append(_getworkflowuploadauthtokenMw(), coze.GetWorkflowUploadAuthToken)...)
			}
		}
	}
	{
		_v1 := root.Group("/v1", _v1Mw()...)
		_v1.GET("/conversations", append(_listconversationsapiMw(), coze.ListConversationsApi)...)
		{
			_bot0 := _v1.Group("/bot", _bot0Mw()...)
			_bot0.GET("/get_online_info", append(_getbotonlineinfoMw(), coze.GetBotOnlineInfo)...)
		}
		{
			_conversation0 := _v1.Group("/conversation", _conversation0Mw()...)
			_conversation0.POST("/create", append(_createconversationMw(), coze.CreateConversation)...)
			{
				_message := _conversation0.Group("/message", _messageMw()...)
				_message.POST("/list", append(_getapimessagelistMw(), coze.GetApiMessageList)...)
			}
		}
		{
			_conversations := _v1.Group("/conversations", _conversationsMw()...)
			{
				_conversation_id := _conversations.Group("/:conversation_id", _conversation_idMw()...)
				_conversation_id.POST("/clear", append(_clearconversationapiMw(), coze.ClearConversationApi)...)
			}
		}
		{
			_files := _v1.Group("/files", _filesMw()...)
			_files.POST("/upload", append(_uploadfileopenMw(), coze.UploadFileOpen)...)
		}
		{
			_workflow := _v1.Group("/workflow", _workflowMw()...)
			_workflow.GET("/get_run_history", append(_openapigetworkflowrunhistoryMw(), coze.OpenAPIGetWorkflowRunHistory)...)
			_workflow.POST("/run", append(_openapirunflowMw(), coze.OpenAPIRunFlow)...)
			_workflow.POST("/stream_resume", append(_openapistreamresumeflowMw(), coze.OpenAPIStreamResumeFlow)...)
			_workflow.POST("/stream_run", append(_openapistreamrunflowMw(), coze.OpenAPIStreamRunFlow)...)
		}
		{
			_workflows := _v1.Group("/workflows", _workflowsMw()...)
			_workflows.POST("/chat", append(_openapichatflowrunMw(), coze.OpenAPIChatFlowRun)...)
			_workflows.GET("/:workflow_id", append(_openapigetworkflowinfoMw(), coze.OpenAPIGetWorkflowInfo)...)
		}
	}
	{
		_v3 := root.Group("/v3", _v3Mw()...)
		_v3.POST("/chat", append(_chatv3Mw(), coze.ChatV3)...)
	}
}
