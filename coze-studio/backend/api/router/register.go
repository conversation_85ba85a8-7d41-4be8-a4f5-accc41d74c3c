/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator. DO NOT EDIT.

package router

import (
	"context"
	"os"
	"path"
	"strings"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/app/server"

	coze "github.com/coze-dev/coze-studio/backend/api/router/coze"
	"github.com/coze-dev/coze-studio/backend/pkg/logs"
)

// GeneratedRegister registers routers generated by IDL.
func GeneratedRegister(r *server.Hertz) {
	// INSERT_POINT: DO NOT DELETE THIS LINE!
	coze.Register(r)
	staticFileRegister(r)
}

// staticFileRegister registers web page router.
func staticFileRegister(r *server.Hertz) {
	cwd, err := os.Getwd()
	if err != nil {
		logs.Warnf("[staticFileRegister] Failed to get current working directory: %v", err)
		cwd = os.Getenv("PWD")
	}

	staticFile := path.Join(cwd, "resources/static/index.html")

	r.Static("/static", path.Join(cwd, "/resources/static"))
	r.StaticFile("/favicon.png", "./resources/static/favicon.png")
	r.StaticFile("/", staticFile)
	r.StaticFile("/sign", staticFile)

	type data struct {
		Code int32  `json:"code"`
		Msg  string `json:"msg"`
	}

	r.NoRoute(func(c context.Context, ctx *app.RequestContext) {
		path := string(ctx.GetRequest().URI().Path())
		if strings.HasPrefix(path, "/api/") ||
			strings.HasPrefix(path, "/v1/") ||
			strings.HasPrefix(path, "/v3/") {
			ctx.JSON(404, data{
				Code: 404,
				Msg:  "not found",
			})
			return
		}

		// index page will show 404 error
		ctx.File(staticFile)
	})

}
