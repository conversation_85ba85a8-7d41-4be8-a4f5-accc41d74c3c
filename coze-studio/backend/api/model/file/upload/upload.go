// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package upload

import (
	"context"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

type CommonUploadRequest struct {
	ByteData   []byte  `thrift:"ByteData,1,optional" json:"ByteData,omitempty" raw_body:""`
	UploadID   *string `thrift:"uploadID,2,optional" json:"uploadID,omitempty" query:"uploadID"`
	PartNumber *string `thrift:"partNumber,3,optional" json:"partNumber,omitempty" query:"partNumber"`
}

func NewCommonUploadRequest() *CommonUploadRequest {
	return &CommonUploadRequest{}
}

func (p *CommonUploadRequest) InitDefault() {
}

var CommonUploadRequest_ByteData_DEFAULT []byte

func (p *CommonUploadRequest) GetByteData() (v []byte) {
	if !p.IsSetByteData() {
		return CommonUploadRequest_ByteData_DEFAULT
	}
	return p.ByteData
}

var CommonUploadRequest_UploadID_DEFAULT string

func (p *CommonUploadRequest) GetUploadID() (v string) {
	if !p.IsSetUploadID() {
		return CommonUploadRequest_UploadID_DEFAULT
	}
	return *p.UploadID
}

var CommonUploadRequest_PartNumber_DEFAULT string

func (p *CommonUploadRequest) GetPartNumber() (v string) {
	if !p.IsSetPartNumber() {
		return CommonUploadRequest_PartNumber_DEFAULT
	}
	return *p.PartNumber
}

var fieldIDToName_CommonUploadRequest = map[int16]string{
	1: "ByteData",
	2: "uploadID",
	3: "partNumber",
}

func (p *CommonUploadRequest) IsSetByteData() bool {
	return p.ByteData != nil
}

func (p *CommonUploadRequest) IsSetUploadID() bool {
	return p.UploadID != nil
}

func (p *CommonUploadRequest) IsSetPartNumber() bool {
	return p.PartNumber != nil
}

func (p *CommonUploadRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CommonUploadRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CommonUploadRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field []byte
	if v, err := iprot.ReadBinary(); err != nil {
		return err
	} else {
		_field = []byte(v)
	}
	p.ByteData = _field
	return nil
}
func (p *CommonUploadRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UploadID = _field
	return nil
}
func (p *CommonUploadRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PartNumber = _field
	return nil
}

func (p *CommonUploadRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CommonUploadRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CommonUploadRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetByteData() {
		if err = oprot.WriteFieldBegin("ByteData", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBinary([]byte(p.ByteData)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CommonUploadRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetUploadID() {
		if err = oprot.WriteFieldBegin("uploadID", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UploadID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CommonUploadRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPartNumber() {
		if err = oprot.WriteFieldBegin("partNumber", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.PartNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CommonUploadRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonUploadRequest(%+v)", *p)

}

type Error struct {
	Code      int32  `thrift:"code,1" form:"code" json:"code" query:"code"`
	Error     string `thrift:"error,2" form:"error" json:"error" query:"error"`
	ErrorCode int32  `thrift:"error_code,3" form:"error_code" json:"error_code" query:"error_code"`
	Message   string `thrift:"message,4" form:"message" json:"message" query:"message"`
}

func NewError() *Error {
	return &Error{}
}

func (p *Error) InitDefault() {
}

func (p *Error) GetCode() (v int32) {
	return p.Code
}

func (p *Error) GetError() (v string) {
	return p.Error
}

func (p *Error) GetErrorCode() (v int32) {
	return p.ErrorCode
}

func (p *Error) GetMessage() (v string) {
	return p.Message
}

var fieldIDToName_Error = map[int16]string{
	1: "code",
	2: "error",
	3: "error_code",
	4: "message",
}

func (p *Error) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Error[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Error) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *Error) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Error = _field
	return nil
}
func (p *Error) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrorCode = _field
	return nil
}
func (p *Error) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}

func (p *Error) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Error"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Error) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Error) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("error", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Error); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Error) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("error_code", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ErrorCode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Error) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("message", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Error) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Error(%+v)", *p)

}

type Payload struct {
	Hash     string `thrift:"hash,1" form:"hash" json:"hash" query:"hash"`
	Key      string `thrift:"key,2" form:"key" json:"key" query:"key"`
	UploadID string `thrift:"uploadID,3" form:"uploadID" json:"uploadID" query:"uploadID"`
}

func NewPayload() *Payload {
	return &Payload{}
}

func (p *Payload) InitDefault() {
}

func (p *Payload) GetHash() (v string) {
	return p.Hash
}

func (p *Payload) GetKey() (v string) {
	return p.Key
}

func (p *Payload) GetUploadID() (v string) {
	return p.UploadID
}

var fieldIDToName_Payload = map[int16]string{
	1: "hash",
	2: "key",
	3: "uploadID",
}

func (p *Payload) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Payload[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Payload) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Hash = _field
	return nil
}
func (p *Payload) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Key = _field
	return nil
}
func (p *Payload) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UploadID = _field
	return nil
}

func (p *Payload) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Payload"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Payload) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("hash", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Hash); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Payload) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("key", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Key); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Payload) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("uploadID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UploadID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Payload) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Payload(%+v)", *p)

}

type CommonUploadResponse struct {
	Version string   `thrift:"Version,1" form:"Version" json:"Version" query:"Version"`
	Success int32    `thrift:"success,2" form:"success" json:"success" query:"success"`
	Error   *Error   `thrift:"error,3" form:"error" json:"error" query:"error"`
	Payload *Payload `thrift:"payload,4" form:"payload" json:"payload" query:"payload"`
}

func NewCommonUploadResponse() *CommonUploadResponse {
	return &CommonUploadResponse{}
}

func (p *CommonUploadResponse) InitDefault() {
}

func (p *CommonUploadResponse) GetVersion() (v string) {
	return p.Version
}

func (p *CommonUploadResponse) GetSuccess() (v int32) {
	return p.Success
}

var CommonUploadResponse_Error_DEFAULT *Error

func (p *CommonUploadResponse) GetError() (v *Error) {
	if !p.IsSetError() {
		return CommonUploadResponse_Error_DEFAULT
	}
	return p.Error
}

var CommonUploadResponse_Payload_DEFAULT *Payload

func (p *CommonUploadResponse) GetPayload() (v *Payload) {
	if !p.IsSetPayload() {
		return CommonUploadResponse_Payload_DEFAULT
	}
	return p.Payload
}

var fieldIDToName_CommonUploadResponse = map[int16]string{
	1: "Version",
	2: "success",
	3: "error",
	4: "payload",
}

func (p *CommonUploadResponse) IsSetError() bool {
	return p.Error != nil
}

func (p *CommonUploadResponse) IsSetPayload() bool {
	return p.Payload != nil
}

func (p *CommonUploadResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CommonUploadResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CommonUploadResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Version = _field
	return nil
}
func (p *CommonUploadResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}
func (p *CommonUploadResponse) ReadField3(iprot thrift.TProtocol) error {
	_field := NewError()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Error = _field
	return nil
}
func (p *CommonUploadResponse) ReadField4(iprot thrift.TProtocol) error {
	_field := NewPayload()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Payload = _field
	return nil
}

func (p *CommonUploadResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CommonUploadResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CommonUploadResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Version", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Version); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CommonUploadResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("success", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CommonUploadResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("error", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Error.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CommonUploadResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("payload", thrift.STRUCT, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Payload.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CommonUploadResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonUploadResponse(%+v)", *p)

}

type ApplyUploadActionRequest struct {
	Action        *string `thrift:"Action,1,optional" json:"Action,omitempty" query:"Action"`
	Version       *string `thrift:"Version,2,optional" json:"Version,omitempty" query:"Version"`
	ServiceId     *string `thrift:"ServiceId,3,optional" json:"ServiceId,omitempty" query:"ServiceId"`
	FileExtension *string `thrift:"FileExtension,4,optional" json:"FileExtension,omitempty" query:"FileExtension"`
	FileSize      *string `thrift:"FileSize,5,optional" json:"FileSize,omitempty" query:"FileSize"`
	S             *string `thrift:"s,6,optional" json:"s,omitempty" query:"s"`
	ByteData      []byte  `thrift:"ByteData,7,optional" json:"ByteData,omitempty" raw_body:""`
}

func NewApplyUploadActionRequest() *ApplyUploadActionRequest {
	return &ApplyUploadActionRequest{}
}

func (p *ApplyUploadActionRequest) InitDefault() {
}

var ApplyUploadActionRequest_Action_DEFAULT string

func (p *ApplyUploadActionRequest) GetAction() (v string) {
	if !p.IsSetAction() {
		return ApplyUploadActionRequest_Action_DEFAULT
	}
	return *p.Action
}

var ApplyUploadActionRequest_Version_DEFAULT string

func (p *ApplyUploadActionRequest) GetVersion() (v string) {
	if !p.IsSetVersion() {
		return ApplyUploadActionRequest_Version_DEFAULT
	}
	return *p.Version
}

var ApplyUploadActionRequest_ServiceId_DEFAULT string

func (p *ApplyUploadActionRequest) GetServiceId() (v string) {
	if !p.IsSetServiceId() {
		return ApplyUploadActionRequest_ServiceId_DEFAULT
	}
	return *p.ServiceId
}

var ApplyUploadActionRequest_FileExtension_DEFAULT string

func (p *ApplyUploadActionRequest) GetFileExtension() (v string) {
	if !p.IsSetFileExtension() {
		return ApplyUploadActionRequest_FileExtension_DEFAULT
	}
	return *p.FileExtension
}

var ApplyUploadActionRequest_FileSize_DEFAULT string

func (p *ApplyUploadActionRequest) GetFileSize() (v string) {
	if !p.IsSetFileSize() {
		return ApplyUploadActionRequest_FileSize_DEFAULT
	}
	return *p.FileSize
}

var ApplyUploadActionRequest_S_DEFAULT string

func (p *ApplyUploadActionRequest) GetS() (v string) {
	if !p.IsSetS() {
		return ApplyUploadActionRequest_S_DEFAULT
	}
	return *p.S
}

var ApplyUploadActionRequest_ByteData_DEFAULT []byte

func (p *ApplyUploadActionRequest) GetByteData() (v []byte) {
	if !p.IsSetByteData() {
		return ApplyUploadActionRequest_ByteData_DEFAULT
	}
	return p.ByteData
}

var fieldIDToName_ApplyUploadActionRequest = map[int16]string{
	1: "Action",
	2: "Version",
	3: "ServiceId",
	4: "FileExtension",
	5: "FileSize",
	6: "s",
	7: "ByteData",
}

func (p *ApplyUploadActionRequest) IsSetAction() bool {
	return p.Action != nil
}

func (p *ApplyUploadActionRequest) IsSetVersion() bool {
	return p.Version != nil
}

func (p *ApplyUploadActionRequest) IsSetServiceId() bool {
	return p.ServiceId != nil
}

func (p *ApplyUploadActionRequest) IsSetFileExtension() bool {
	return p.FileExtension != nil
}

func (p *ApplyUploadActionRequest) IsSetFileSize() bool {
	return p.FileSize != nil
}

func (p *ApplyUploadActionRequest) IsSetS() bool {
	return p.S != nil
}

func (p *ApplyUploadActionRequest) IsSetByteData() bool {
	return p.ByteData != nil
}

func (p *ApplyUploadActionRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ApplyUploadActionRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ApplyUploadActionRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Action = _field
	return nil
}
func (p *ApplyUploadActionRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Version = _field
	return nil
}
func (p *ApplyUploadActionRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ServiceId = _field
	return nil
}
func (p *ApplyUploadActionRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FileExtension = _field
	return nil
}
func (p *ApplyUploadActionRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FileSize = _field
	return nil
}
func (p *ApplyUploadActionRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.S = _field
	return nil
}
func (p *ApplyUploadActionRequest) ReadField7(iprot thrift.TProtocol) error {

	var _field []byte
	if v, err := iprot.ReadBinary(); err != nil {
		return err
	} else {
		_field = []byte(v)
	}
	p.ByteData = _field
	return nil
}

func (p *ApplyUploadActionRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ApplyUploadActionRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ApplyUploadActionRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAction() {
		if err = oprot.WriteFieldBegin("Action", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Action); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ApplyUploadActionRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetVersion() {
		if err = oprot.WriteFieldBegin("Version", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Version); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ApplyUploadActionRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetServiceId() {
		if err = oprot.WriteFieldBegin("ServiceId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ServiceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ApplyUploadActionRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileExtension() {
		if err = oprot.WriteFieldBegin("FileExtension", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FileExtension); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ApplyUploadActionRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileSize() {
		if err = oprot.WriteFieldBegin("FileSize", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FileSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ApplyUploadActionRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetS() {
		if err = oprot.WriteFieldBegin("s", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.S); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ApplyUploadActionRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetByteData() {
		if err = oprot.WriteFieldBegin("ByteData", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBinary([]byte(p.ByteData)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ApplyUploadActionRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ApplyUploadActionRequest(%+v)", *p)

}

type ResponseMetadata struct {
	RequestId string `thrift:"RequestId,1,required" form:"RequestId,required" json:"RequestId,required" query:"RequestId,required"`
	Action    string `thrift:"Action,2,required" form:"Action,required" json:"Action,required" query:"Action,required"`
	Version   string `thrift:"Version,3,required" form:"Version,required" json:"Version,required" query:"Version,required"`
	Service   string `thrift:"Service,4,required" form:"Service,required" json:"Service,required" query:"Service,required"`
	Region    string `thrift:"Region,5,required" form:"Region,required" json:"Region,required" query:"Region,required"`
}

func NewResponseMetadata() *ResponseMetadata {
	return &ResponseMetadata{}
}

func (p *ResponseMetadata) InitDefault() {
}

func (p *ResponseMetadata) GetRequestId() (v string) {
	return p.RequestId
}

func (p *ResponseMetadata) GetAction() (v string) {
	return p.Action
}

func (p *ResponseMetadata) GetVersion() (v string) {
	return p.Version
}

func (p *ResponseMetadata) GetService() (v string) {
	return p.Service
}

func (p *ResponseMetadata) GetRegion() (v string) {
	return p.Region
}

var fieldIDToName_ResponseMetadata = map[int16]string{
	1: "RequestId",
	2: "Action",
	3: "Version",
	4: "Service",
	5: "Region",
}

func (p *ResponseMetadata) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRequestId bool = false
	var issetAction bool = false
	var issetVersion bool = false
	var issetService bool = false
	var issetRegion bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRequestId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAction = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetService = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRequestId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAction {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetVersion {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetService {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetRegion {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResponseMetadata[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ResponseMetadata[fieldId]))
}

func (p *ResponseMetadata) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RequestId = _field
	return nil
}
func (p *ResponseMetadata) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Action = _field
	return nil
}
func (p *ResponseMetadata) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Version = _field
	return nil
}
func (p *ResponseMetadata) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Service = _field
	return nil
}
func (p *ResponseMetadata) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Region = _field
	return nil
}

func (p *ResponseMetadata) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResponseMetadata"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResponseMetadata) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RequestId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RequestId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResponseMetadata) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Action", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Action); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ResponseMetadata) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Version", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Version); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ResponseMetadata) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Service", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Service); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ResponseMetadata) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Region", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Region); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ResponseMetadata) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResponseMetadata(%+v)", *p)

}

type StoreInfo struct {
	StoreUri string `thrift:"StoreUri,1,required" form:"StoreUri,required" json:"StoreUri,required" query:"StoreUri,required"`
	Auth     string `thrift:"Auth,2,required" form:"Auth,required" json:"Auth,required" query:"Auth,required"`
	UploadID string `thrift:"UploadID,3,required" form:"UploadID,required" json:"UploadID,required" query:"UploadID,required"`
}

func NewStoreInfo() *StoreInfo {
	return &StoreInfo{}
}

func (p *StoreInfo) InitDefault() {
}

func (p *StoreInfo) GetStoreUri() (v string) {
	return p.StoreUri
}

func (p *StoreInfo) GetAuth() (v string) {
	return p.Auth
}

func (p *StoreInfo) GetUploadID() (v string) {
	return p.UploadID
}

var fieldIDToName_StoreInfo = map[int16]string{
	1: "StoreUri",
	2: "Auth",
	3: "UploadID",
}

func (p *StoreInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStoreUri bool = false
	var issetAuth bool = false
	var issetUploadID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetStoreUri = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAuth = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUploadID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStoreUri {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAuth {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUploadID {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StoreInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_StoreInfo[fieldId]))
}

func (p *StoreInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StoreUri = _field
	return nil
}
func (p *StoreInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Auth = _field
	return nil
}
func (p *StoreInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UploadID = _field
	return nil
}

func (p *StoreInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("StoreInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StoreInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StoreUri", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StoreUri); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *StoreInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Auth", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Auth); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *StoreInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UploadID", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UploadID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *StoreInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StoreInfo(%+v)", *p)

}

type UploadAddress struct {
	StoreInfos   []*StoreInfo      `thrift:"StoreInfos,1,required" form:"StoreInfos,required" json:"StoreInfos,required" query:"StoreInfos,required"`
	UploadHosts  []string          `thrift:"UploadHosts,2,required" form:"UploadHosts,required" json:"UploadHosts,required" query:"UploadHosts,required"`
	UploadHeader map[string]string `thrift:"UploadHeader,3,optional" form:"UploadHeader" json:"UploadHeader,omitempty" query:"UploadHeader"`
	SessionKey   string            `thrift:"SessionKey,4,required" form:"SessionKey,required" json:"SessionKey,required" query:"SessionKey,required"`
	Cloud        string            `thrift:"Cloud,5,required" form:"Cloud,required" json:"Cloud,required" query:"Cloud,required"`
}

func NewUploadAddress() *UploadAddress {
	return &UploadAddress{}
}

func (p *UploadAddress) InitDefault() {
}

func (p *UploadAddress) GetStoreInfos() (v []*StoreInfo) {
	return p.StoreInfos
}

func (p *UploadAddress) GetUploadHosts() (v []string) {
	return p.UploadHosts
}

var UploadAddress_UploadHeader_DEFAULT map[string]string

func (p *UploadAddress) GetUploadHeader() (v map[string]string) {
	if !p.IsSetUploadHeader() {
		return UploadAddress_UploadHeader_DEFAULT
	}
	return p.UploadHeader
}

func (p *UploadAddress) GetSessionKey() (v string) {
	return p.SessionKey
}

func (p *UploadAddress) GetCloud() (v string) {
	return p.Cloud
}

var fieldIDToName_UploadAddress = map[int16]string{
	1: "StoreInfos",
	2: "UploadHosts",
	3: "UploadHeader",
	4: "SessionKey",
	5: "Cloud",
}

func (p *UploadAddress) IsSetUploadHeader() bool {
	return p.UploadHeader != nil
}

func (p *UploadAddress) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStoreInfos bool = false
	var issetUploadHosts bool = false
	var issetSessionKey bool = false
	var issetCloud bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetStoreInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUploadHosts = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionKey = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetCloud = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStoreInfos {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUploadHosts {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSessionKey {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetCloud {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UploadAddress[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UploadAddress[fieldId]))
}

func (p *UploadAddress) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*StoreInfo, 0, size)
	values := make([]StoreInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.StoreInfos = _field
	return nil
}
func (p *UploadAddress) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.UploadHosts = _field
	return nil
}
func (p *UploadAddress) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.UploadHeader = _field
	return nil
}
func (p *UploadAddress) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionKey = _field
	return nil
}
func (p *UploadAddress) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Cloud = _field
	return nil
}

func (p *UploadAddress) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UploadAddress"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UploadAddress) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StoreInfos", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.StoreInfos)); err != nil {
		return err
	}
	for _, v := range p.StoreInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UploadAddress) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UploadHosts", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.UploadHosts)); err != nil {
		return err
	}
	for _, v := range p.UploadHosts {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UploadAddress) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUploadHeader() {
		if err = oprot.WriteFieldBegin("UploadHeader", thrift.MAP, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.UploadHeader)); err != nil {
			return err
		}
		for k, v := range p.UploadHeader {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UploadAddress) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionKey", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionKey); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UploadAddress) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Cloud", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Cloud); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *UploadAddress) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadAddress(%+v)", *p)

}

type UploadNode struct {
	StoreInfos   []*StoreInfo      `thrift:"StoreInfos,1,required" form:"StoreInfos,required" json:"StoreInfos,required" query:"StoreInfos,required"`
	UploadHost   string            `thrift:"UploadHost,2,required" form:"UploadHost,required" json:"UploadHost,required" query:"UploadHost,required"`
	UploadHeader map[string]string `thrift:"UploadHeader,3,optional" form:"UploadHeader" json:"UploadHeader,omitempty" query:"UploadHeader"`
	SessionKey   string            `thrift:"SessionKey,4,required" form:"SessionKey,required" json:"SessionKey,required" query:"SessionKey,required"`
}

func NewUploadNode() *UploadNode {
	return &UploadNode{}
}

func (p *UploadNode) InitDefault() {
}

func (p *UploadNode) GetStoreInfos() (v []*StoreInfo) {
	return p.StoreInfos
}

func (p *UploadNode) GetUploadHost() (v string) {
	return p.UploadHost
}

var UploadNode_UploadHeader_DEFAULT map[string]string

func (p *UploadNode) GetUploadHeader() (v map[string]string) {
	if !p.IsSetUploadHeader() {
		return UploadNode_UploadHeader_DEFAULT
	}
	return p.UploadHeader
}

func (p *UploadNode) GetSessionKey() (v string) {
	return p.SessionKey
}

var fieldIDToName_UploadNode = map[int16]string{
	1: "StoreInfos",
	2: "UploadHost",
	3: "UploadHeader",
	4: "SessionKey",
}

func (p *UploadNode) IsSetUploadHeader() bool {
	return p.UploadHeader != nil
}

func (p *UploadNode) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStoreInfos bool = false
	var issetUploadHost bool = false
	var issetSessionKey bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetStoreInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUploadHost = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetSessionKey = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStoreInfos {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUploadHost {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSessionKey {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UploadNode[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UploadNode[fieldId]))
}

func (p *UploadNode) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*StoreInfo, 0, size)
	values := make([]StoreInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.StoreInfos = _field
	return nil
}
func (p *UploadNode) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UploadHost = _field
	return nil
}
func (p *UploadNode) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.UploadHeader = _field
	return nil
}
func (p *UploadNode) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SessionKey = _field
	return nil
}

func (p *UploadNode) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UploadNode"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UploadNode) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StoreInfos", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.StoreInfos)); err != nil {
		return err
	}
	for _, v := range p.StoreInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UploadNode) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UploadHost", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UploadHost); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UploadNode) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUploadHeader() {
		if err = oprot.WriteFieldBegin("UploadHeader", thrift.MAP, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.UploadHeader)); err != nil {
			return err
		}
		for k, v := range p.UploadHeader {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UploadNode) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SessionKey", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SessionKey); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *UploadNode) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadNode(%+v)", *p)

}

type InnerUploadAddress struct {
	UploadNodes []*UploadNode `thrift:"UploadNodes,1,required" form:"UploadNodes,required" json:"UploadNodes,required" query:"UploadNodes,required"`
}

func NewInnerUploadAddress() *InnerUploadAddress {
	return &InnerUploadAddress{}
}

func (p *InnerUploadAddress) InitDefault() {
}

func (p *InnerUploadAddress) GetUploadNodes() (v []*UploadNode) {
	return p.UploadNodes
}

var fieldIDToName_InnerUploadAddress = map[int16]string{
	1: "UploadNodes",
}

func (p *InnerUploadAddress) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetUploadNodes bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetUploadNodes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetUploadNodes {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InnerUploadAddress[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InnerUploadAddress[fieldId]))
}

func (p *InnerUploadAddress) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*UploadNode, 0, size)
	values := make([]UploadNode, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.UploadNodes = _field
	return nil
}

func (p *InnerUploadAddress) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("InnerUploadAddress"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InnerUploadAddress) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UploadNodes", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.UploadNodes)); err != nil {
		return err
	}
	for _, v := range p.UploadNodes {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InnerUploadAddress) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InnerUploadAddress(%+v)", *p)

}

type UploadResult struct {
	Uri       string `thrift:"Uri,1,required" form:"Uri,required" json:"Uri,required" query:"Uri,required"`
	UriStatus int32  `thrift:"UriStatus,2,required" form:"UriStatus,required" json:"UriStatus,required" query:"UriStatus,required"`
}

func NewUploadResult() *UploadResult {
	return &UploadResult{}
}

func (p *UploadResult) InitDefault() {
}

func (p *UploadResult) GetUri() (v string) {
	return p.Uri
}

func (p *UploadResult) GetUriStatus() (v int32) {
	return p.UriStatus
}

var fieldIDToName_UploadResult = map[int16]string{
	1: "Uri",
	2: "UriStatus",
}

func (p *UploadResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetUri bool = false
	var issetUriStatus bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetUri = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUriStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetUri {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUriStatus {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UploadResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UploadResult[fieldId]))
}

func (p *UploadResult) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Uri = _field
	return nil
}
func (p *UploadResult) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UriStatus = _field
	return nil
}

func (p *UploadResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UploadResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UploadResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Uri", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Uri); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UploadResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UriStatus", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.UriStatus); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UploadResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadResult(%+v)", *p)

}

type PluginResult struct {
	FileName    string `thrift:"FileName,1,required" form:"FileName,required" json:"FileName,required" query:"FileName,required"`
	SourceUri   string `thrift:"SourceUri,2,required" form:"SourceUri,required" json:"SourceUri,required" query:"SourceUri,required"`
	ImageUri    string `thrift:"ImageUri,3,required" form:"ImageUri,required" json:"ImageUri,required" query:"ImageUri,required"`
	ImageWidth  int32  `thrift:"ImageWidth,4,required" form:"ImageWidth,required" json:"ImageWidth,required" query:"ImageWidth,required"`
	ImageHeight int32  `thrift:"ImageHeight,5,required" form:"ImageHeight,required" json:"ImageHeight,required" query:"ImageHeight,required"`
	ImageMd5    string `thrift:"ImageMd5,6,required" form:"ImageMd5,required" json:"ImageMd5,required" query:"ImageMd5,required"`
	ImageFormat string `thrift:"ImageFormat,7,required" form:"ImageFormat,required" json:"ImageFormat,required" query:"ImageFormat,required"`
	ImageSize   int32  `thrift:"ImageSize,8,required" form:"ImageSize,required" json:"ImageSize,required" query:"ImageSize,required"`
	FrameCnt    int32  `thrift:"FrameCnt,9,required" form:"FrameCnt,required" json:"FrameCnt,required" query:"FrameCnt,required"`
}

func NewPluginResult() *PluginResult {
	return &PluginResult{}
}

func (p *PluginResult) InitDefault() {
}

func (p *PluginResult) GetFileName() (v string) {
	return p.FileName
}

func (p *PluginResult) GetSourceUri() (v string) {
	return p.SourceUri
}

func (p *PluginResult) GetImageUri() (v string) {
	return p.ImageUri
}

func (p *PluginResult) GetImageWidth() (v int32) {
	return p.ImageWidth
}

func (p *PluginResult) GetImageHeight() (v int32) {
	return p.ImageHeight
}

func (p *PluginResult) GetImageMd5() (v string) {
	return p.ImageMd5
}

func (p *PluginResult) GetImageFormat() (v string) {
	return p.ImageFormat
}

func (p *PluginResult) GetImageSize() (v int32) {
	return p.ImageSize
}

func (p *PluginResult) GetFrameCnt() (v int32) {
	return p.FrameCnt
}

var fieldIDToName_PluginResult = map[int16]string{
	1: "FileName",
	2: "SourceUri",
	3: "ImageUri",
	4: "ImageWidth",
	5: "ImageHeight",
	6: "ImageMd5",
	7: "ImageFormat",
	8: "ImageSize",
	9: "FrameCnt",
}

func (p *PluginResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFileName bool = false
	var issetSourceUri bool = false
	var issetImageUri bool = false
	var issetImageWidth bool = false
	var issetImageHeight bool = false
	var issetImageMd5 bool = false
	var issetImageFormat bool = false
	var issetImageSize bool = false
	var issetFrameCnt bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFileName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSourceUri = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetImageUri = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetImageWidth = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetImageHeight = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetImageMd5 = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetImageFormat = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetImageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetFrameCnt = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFileName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSourceUri {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetImageUri {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetImageWidth {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetImageHeight {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetImageMd5 {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetImageFormat {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetImageSize {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetFrameCnt {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PluginResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PluginResult[fieldId]))
}

func (p *PluginResult) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FileName = _field
	return nil
}
func (p *PluginResult) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SourceUri = _field
	return nil
}
func (p *PluginResult) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ImageUri = _field
	return nil
}
func (p *PluginResult) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ImageWidth = _field
	return nil
}
func (p *PluginResult) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ImageHeight = _field
	return nil
}
func (p *PluginResult) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ImageMd5 = _field
	return nil
}
func (p *PluginResult) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ImageFormat = _field
	return nil
}
func (p *PluginResult) ReadField8(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ImageSize = _field
	return nil
}
func (p *PluginResult) ReadField9(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FrameCnt = _field
	return nil
}

func (p *PluginResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PluginResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PluginResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FileName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FileName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PluginResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SourceUri", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SourceUri); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PluginResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ImageUri", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ImageUri); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PluginResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ImageWidth", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ImageWidth); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PluginResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ImageHeight", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ImageHeight); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *PluginResult) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ImageMd5", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ImageMd5); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *PluginResult) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ImageFormat", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ImageFormat); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *PluginResult) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ImageSize", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ImageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *PluginResult) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FrameCnt", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.FrameCnt); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *PluginResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PluginResult(%+v)", *p)

}

type ApplyUploadActionResult struct {
	UploadAddress         *UploadAddress      `thrift:"UploadAddress,1,optional" form:"UploadAddress" json:"UploadAddress,omitempty" query:"UploadAddress"`
	FallbackUploadAddress *UploadAddress      `thrift:"FallbackUploadAddress,2,optional" form:"FallbackUploadAddress" json:"FallbackUploadAddress,omitempty" query:"FallbackUploadAddress"`
	InnerUploadAddress    *InnerUploadAddress `thrift:"InnerUploadAddress,3,optional" form:"InnerUploadAddress" json:"InnerUploadAddress,omitempty" query:"InnerUploadAddress"`
	RequestId             *string             `thrift:"RequestId,4,optional" form:"RequestId" json:"RequestId,omitempty" query:"RequestId"`
	SDKParam              *string             `thrift:"SDKParam,5,optional" form:"SDKParam" json:"SDKParam,omitempty" query:"SDKParam"`
	Results               []*UploadResult     `thrift:"Results,6,optional" form:"Results" json:"Results,omitempty" query:"Results"`
	PluginResult          []*PluginResult     `thrift:"PluginResult,7,optional" form:"PluginResult" json:"PluginResult,omitempty" query:"PluginResult"`
}

func NewApplyUploadActionResult() *ApplyUploadActionResult {
	return &ApplyUploadActionResult{}
}

func (p *ApplyUploadActionResult) InitDefault() {
}

var ApplyUploadActionResult_UploadAddress_DEFAULT *UploadAddress

func (p *ApplyUploadActionResult) GetUploadAddress() (v *UploadAddress) {
	if !p.IsSetUploadAddress() {
		return ApplyUploadActionResult_UploadAddress_DEFAULT
	}
	return p.UploadAddress
}

var ApplyUploadActionResult_FallbackUploadAddress_DEFAULT *UploadAddress

func (p *ApplyUploadActionResult) GetFallbackUploadAddress() (v *UploadAddress) {
	if !p.IsSetFallbackUploadAddress() {
		return ApplyUploadActionResult_FallbackUploadAddress_DEFAULT
	}
	return p.FallbackUploadAddress
}

var ApplyUploadActionResult_InnerUploadAddress_DEFAULT *InnerUploadAddress

func (p *ApplyUploadActionResult) GetInnerUploadAddress() (v *InnerUploadAddress) {
	if !p.IsSetInnerUploadAddress() {
		return ApplyUploadActionResult_InnerUploadAddress_DEFAULT
	}
	return p.InnerUploadAddress
}

var ApplyUploadActionResult_RequestId_DEFAULT string

func (p *ApplyUploadActionResult) GetRequestId() (v string) {
	if !p.IsSetRequestId() {
		return ApplyUploadActionResult_RequestId_DEFAULT
	}
	return *p.RequestId
}

var ApplyUploadActionResult_SDKParam_DEFAULT string

func (p *ApplyUploadActionResult) GetSDKParam() (v string) {
	if !p.IsSetSDKParam() {
		return ApplyUploadActionResult_SDKParam_DEFAULT
	}
	return *p.SDKParam
}

var ApplyUploadActionResult_Results_DEFAULT []*UploadResult

func (p *ApplyUploadActionResult) GetResults() (v []*UploadResult) {
	if !p.IsSetResults() {
		return ApplyUploadActionResult_Results_DEFAULT
	}
	return p.Results
}

var ApplyUploadActionResult_PluginResult_DEFAULT []*PluginResult

func (p *ApplyUploadActionResult) GetPluginResult() (v []*PluginResult) {
	if !p.IsSetPluginResult() {
		return ApplyUploadActionResult_PluginResult_DEFAULT
	}
	return p.PluginResult
}

var fieldIDToName_ApplyUploadActionResult = map[int16]string{
	1: "UploadAddress",
	2: "FallbackUploadAddress",
	3: "InnerUploadAddress",
	4: "RequestId",
	5: "SDKParam",
	6: "Results",
	7: "PluginResult",
}

func (p *ApplyUploadActionResult) IsSetUploadAddress() bool {
	return p.UploadAddress != nil
}

func (p *ApplyUploadActionResult) IsSetFallbackUploadAddress() bool {
	return p.FallbackUploadAddress != nil
}

func (p *ApplyUploadActionResult) IsSetInnerUploadAddress() bool {
	return p.InnerUploadAddress != nil
}

func (p *ApplyUploadActionResult) IsSetRequestId() bool {
	return p.RequestId != nil
}

func (p *ApplyUploadActionResult) IsSetSDKParam() bool {
	return p.SDKParam != nil
}

func (p *ApplyUploadActionResult) IsSetResults() bool {
	return p.Results != nil
}

func (p *ApplyUploadActionResult) IsSetPluginResult() bool {
	return p.PluginResult != nil
}

func (p *ApplyUploadActionResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ApplyUploadActionResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ApplyUploadActionResult) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUploadAddress()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.UploadAddress = _field
	return nil
}
func (p *ApplyUploadActionResult) ReadField2(iprot thrift.TProtocol) error {
	_field := NewUploadAddress()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.FallbackUploadAddress = _field
	return nil
}
func (p *ApplyUploadActionResult) ReadField3(iprot thrift.TProtocol) error {
	_field := NewInnerUploadAddress()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InnerUploadAddress = _field
	return nil
}
func (p *ApplyUploadActionResult) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RequestId = _field
	return nil
}
func (p *ApplyUploadActionResult) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SDKParam = _field
	return nil
}
func (p *ApplyUploadActionResult) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*UploadResult, 0, size)
	values := make([]UploadResult, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Results = _field
	return nil
}
func (p *ApplyUploadActionResult) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PluginResult, 0, size)
	values := make([]PluginResult, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PluginResult = _field
	return nil
}

func (p *ApplyUploadActionResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ApplyUploadActionResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ApplyUploadActionResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetUploadAddress() {
		if err = oprot.WriteFieldBegin("UploadAddress", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.UploadAddress.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ApplyUploadActionResult) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetFallbackUploadAddress() {
		if err = oprot.WriteFieldBegin("FallbackUploadAddress", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.FallbackUploadAddress.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ApplyUploadActionResult) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInnerUploadAddress() {
		if err = oprot.WriteFieldBegin("InnerUploadAddress", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.InnerUploadAddress.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ApplyUploadActionResult) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRequestId() {
		if err = oprot.WriteFieldBegin("RequestId", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RequestId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ApplyUploadActionResult) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSDKParam() {
		if err = oprot.WriteFieldBegin("SDKParam", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SDKParam); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ApplyUploadActionResult) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetResults() {
		if err = oprot.WriteFieldBegin("Results", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Results)); err != nil {
			return err
		}
		for _, v := range p.Results {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ApplyUploadActionResult) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPluginResult() {
		if err = oprot.WriteFieldBegin("PluginResult", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PluginResult)); err != nil {
			return err
		}
		for _, v := range p.PluginResult {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ApplyUploadActionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ApplyUploadActionResult(%+v)", *p)

}

type ApplyUploadActionResponse struct {
	ResponseMetadata *ResponseMetadata        `thrift:"ResponseMetadata,1,required" form:"ResponseMetadata,required" json:"ResponseMetadata,required" query:"ResponseMetadata,required"`
	Result           *ApplyUploadActionResult `thrift:"Result,2,required" form:"Result,required" json:"Result,required" query:"Result,required"`
}

func NewApplyUploadActionResponse() *ApplyUploadActionResponse {
	return &ApplyUploadActionResponse{}
}

func (p *ApplyUploadActionResponse) InitDefault() {
}

var ApplyUploadActionResponse_ResponseMetadata_DEFAULT *ResponseMetadata

func (p *ApplyUploadActionResponse) GetResponseMetadata() (v *ResponseMetadata) {
	if !p.IsSetResponseMetadata() {
		return ApplyUploadActionResponse_ResponseMetadata_DEFAULT
	}
	return p.ResponseMetadata
}

var ApplyUploadActionResponse_Result_DEFAULT *ApplyUploadActionResult

func (p *ApplyUploadActionResponse) GetResult() (v *ApplyUploadActionResult) {
	if !p.IsSetResult() {
		return ApplyUploadActionResponse_Result_DEFAULT
	}
	return p.Result
}

var fieldIDToName_ApplyUploadActionResponse = map[int16]string{
	1: "ResponseMetadata",
	2: "Result",
}

func (p *ApplyUploadActionResponse) IsSetResponseMetadata() bool {
	return p.ResponseMetadata != nil
}

func (p *ApplyUploadActionResponse) IsSetResult() bool {
	return p.Result != nil
}

func (p *ApplyUploadActionResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetResponseMetadata bool = false
	var issetResult bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetResponseMetadata = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetResult = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetResponseMetadata {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetResult {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ApplyUploadActionResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ApplyUploadActionResponse[fieldId]))
}

func (p *ApplyUploadActionResponse) ReadField1(iprot thrift.TProtocol) error {
	_field := NewResponseMetadata()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ResponseMetadata = _field
	return nil
}
func (p *ApplyUploadActionResponse) ReadField2(iprot thrift.TProtocol) error {
	_field := NewApplyUploadActionResult()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Result = _field
	return nil
}

func (p *ApplyUploadActionResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ApplyUploadActionResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ApplyUploadActionResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ResponseMetadata", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ResponseMetadata.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ApplyUploadActionResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Result", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Result.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ApplyUploadActionResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ApplyUploadActionResponse(%+v)", *p)

}

type UploadService interface {
	CommonUpload(ctx context.Context, request *CommonUploadRequest) (r *CommonUploadResponse, err error)

	ApplyUploadAction(ctx context.Context, request *ApplyUploadActionRequest) (r *ApplyUploadActionResponse, err error)
}

type UploadServiceClient struct {
	c thrift.TClient
}

func NewUploadServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *UploadServiceClient {
	return &UploadServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewUploadServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *UploadServiceClient {
	return &UploadServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewUploadServiceClient(c thrift.TClient) *UploadServiceClient {
	return &UploadServiceClient{
		c: c,
	}
}

func (p *UploadServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *UploadServiceClient) CommonUpload(ctx context.Context, request *CommonUploadRequest) (r *CommonUploadResponse, err error) {
	var _args UploadServiceCommonUploadArgs
	_args.Request = request
	var _result UploadServiceCommonUploadResult
	if err = p.Client_().Call(ctx, "CommonUpload", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *UploadServiceClient) ApplyUploadAction(ctx context.Context, request *ApplyUploadActionRequest) (r *ApplyUploadActionResponse, err error) {
	var _args UploadServiceApplyUploadActionArgs
	_args.Request = request
	var _result UploadServiceApplyUploadActionResult
	if err = p.Client_().Call(ctx, "ApplyUploadAction", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

type UploadServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      UploadService
}

func (p *UploadServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *UploadServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *UploadServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewUploadServiceProcessor(handler UploadService) *UploadServiceProcessor {
	self := &UploadServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self.AddToProcessorMap("CommonUpload", &uploadServiceProcessorCommonUpload{handler: handler})
	self.AddToProcessorMap("ApplyUploadAction", &uploadServiceProcessorApplyUploadAction{handler: handler})
	return self
}
func (p *UploadServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush(ctx)
	return false, x
}

type uploadServiceProcessorCommonUpload struct {
	handler UploadService
}

func (p *uploadServiceProcessorCommonUpload) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := UploadServiceCommonUploadArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("CommonUpload", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := UploadServiceCommonUploadResult{}
	var retval *CommonUploadResponse
	if retval, err2 = p.handler.CommonUpload(ctx, args.Request); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CommonUpload: "+err2.Error())
		oprot.WriteMessageBegin("CommonUpload", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("CommonUpload", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type uploadServiceProcessorApplyUploadAction struct {
	handler UploadService
}

func (p *uploadServiceProcessorApplyUploadAction) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := UploadServiceApplyUploadActionArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ApplyUploadAction", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := UploadServiceApplyUploadActionResult{}
	var retval *ApplyUploadActionResponse
	if retval, err2 = p.handler.ApplyUploadAction(ctx, args.Request); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ApplyUploadAction: "+err2.Error())
		oprot.WriteMessageBegin("ApplyUploadAction", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ApplyUploadAction", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type UploadServiceCommonUploadArgs struct {
	Request *CommonUploadRequest `thrift:"request,1"`
}

func NewUploadServiceCommonUploadArgs() *UploadServiceCommonUploadArgs {
	return &UploadServiceCommonUploadArgs{}
}

func (p *UploadServiceCommonUploadArgs) InitDefault() {
}

var UploadServiceCommonUploadArgs_Request_DEFAULT *CommonUploadRequest

func (p *UploadServiceCommonUploadArgs) GetRequest() (v *CommonUploadRequest) {
	if !p.IsSetRequest() {
		return UploadServiceCommonUploadArgs_Request_DEFAULT
	}
	return p.Request
}

var fieldIDToName_UploadServiceCommonUploadArgs = map[int16]string{
	1: "request",
}

func (p *UploadServiceCommonUploadArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *UploadServiceCommonUploadArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UploadServiceCommonUploadArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UploadServiceCommonUploadArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCommonUploadRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Request = _field
	return nil
}

func (p *UploadServiceCommonUploadArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CommonUpload_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UploadServiceCommonUploadArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("request", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Request.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UploadServiceCommonUploadArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadServiceCommonUploadArgs(%+v)", *p)

}

type UploadServiceCommonUploadResult struct {
	Success *CommonUploadResponse `thrift:"success,0,optional"`
}

func NewUploadServiceCommonUploadResult() *UploadServiceCommonUploadResult {
	return &UploadServiceCommonUploadResult{}
}

func (p *UploadServiceCommonUploadResult) InitDefault() {
}

var UploadServiceCommonUploadResult_Success_DEFAULT *CommonUploadResponse

func (p *UploadServiceCommonUploadResult) GetSuccess() (v *CommonUploadResponse) {
	if !p.IsSetSuccess() {
		return UploadServiceCommonUploadResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_UploadServiceCommonUploadResult = map[int16]string{
	0: "success",
}

func (p *UploadServiceCommonUploadResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UploadServiceCommonUploadResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UploadServiceCommonUploadResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UploadServiceCommonUploadResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCommonUploadResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *UploadServiceCommonUploadResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CommonUpload_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UploadServiceCommonUploadResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *UploadServiceCommonUploadResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadServiceCommonUploadResult(%+v)", *p)

}

type UploadServiceApplyUploadActionArgs struct {
	Request *ApplyUploadActionRequest `thrift:"request,1"`
}

func NewUploadServiceApplyUploadActionArgs() *UploadServiceApplyUploadActionArgs {
	return &UploadServiceApplyUploadActionArgs{}
}

func (p *UploadServiceApplyUploadActionArgs) InitDefault() {
}

var UploadServiceApplyUploadActionArgs_Request_DEFAULT *ApplyUploadActionRequest

func (p *UploadServiceApplyUploadActionArgs) GetRequest() (v *ApplyUploadActionRequest) {
	if !p.IsSetRequest() {
		return UploadServiceApplyUploadActionArgs_Request_DEFAULT
	}
	return p.Request
}

var fieldIDToName_UploadServiceApplyUploadActionArgs = map[int16]string{
	1: "request",
}

func (p *UploadServiceApplyUploadActionArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *UploadServiceApplyUploadActionArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UploadServiceApplyUploadActionArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UploadServiceApplyUploadActionArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewApplyUploadActionRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Request = _field
	return nil
}

func (p *UploadServiceApplyUploadActionArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ApplyUploadAction_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UploadServiceApplyUploadActionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("request", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Request.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UploadServiceApplyUploadActionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadServiceApplyUploadActionArgs(%+v)", *p)

}

type UploadServiceApplyUploadActionResult struct {
	Success *ApplyUploadActionResponse `thrift:"success,0,optional"`
}

func NewUploadServiceApplyUploadActionResult() *UploadServiceApplyUploadActionResult {
	return &UploadServiceApplyUploadActionResult{}
}

func (p *UploadServiceApplyUploadActionResult) InitDefault() {
}

var UploadServiceApplyUploadActionResult_Success_DEFAULT *ApplyUploadActionResponse

func (p *UploadServiceApplyUploadActionResult) GetSuccess() (v *ApplyUploadActionResponse) {
	if !p.IsSetSuccess() {
		return UploadServiceApplyUploadActionResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_UploadServiceApplyUploadActionResult = map[int16]string{
	0: "success",
}

func (p *UploadServiceApplyUploadActionResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UploadServiceApplyUploadActionResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UploadServiceApplyUploadActionResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UploadServiceApplyUploadActionResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewApplyUploadActionResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *UploadServiceApplyUploadActionResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ApplyUploadAction_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UploadServiceApplyUploadActionResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *UploadServiceApplyUploadActionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadServiceApplyUploadActionResult(%+v)", *p)

}
