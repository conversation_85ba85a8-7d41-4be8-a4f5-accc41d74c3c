// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package common

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
)

type Scene int64

const (
	Scene_Default  Scene = 0
	Scene_Explore  Scene = 1
	Scene_BotStore Scene = 2
	Scene_CozeHome Scene = 3
	//调试
	Scene_Playground Scene = 4
	// 评测平台
	Scene_Evaluation Scene = 5
	Scene_AgentAPP   Scene = 6
	//prompt优化
	Scene_PromptOptimize Scene = 7
	// createbot的nl2bot功能
	Scene_GenerateAgentInfo Scene = 8
	//openapi
	Scene_SceneOpenApi Scene = 9
)

func (p Scene) String() string {
	switch p {
	case Scene_Default:
		return "Default"
	case Scene_Explore:
		return "Explore"
	case Scene_BotStore:
		return "BotStore"
	case Scene_CozeHome:
		return "CozeHome"
	case Scene_Playground:
		return "Playground"
	case Scene_Evaluation:
		return "Evaluation"
	case Scene_AgentAPP:
		return "AgentAPP"
	case Scene_PromptOptimize:
		return "PromptOptimize"
	case Scene_GenerateAgentInfo:
		return "GenerateAgentInfo"
	case Scene_SceneOpenApi:
		return "SceneOpenApi"
	}
	return "<UNSET>"
}

func SceneFromString(s string) (Scene, error) {
	switch s {
	case "Default":
		return Scene_Default, nil
	case "Explore":
		return Scene_Explore, nil
	case "BotStore":
		return Scene_BotStore, nil
	case "CozeHome":
		return Scene_CozeHome, nil
	case "Playground":
		return Scene_Playground, nil
	case "Evaluation":
		return Scene_Evaluation, nil
	case "AgentAPP":
		return Scene_AgentAPP, nil
	case "PromptOptimize":
		return Scene_PromptOptimize, nil
	case "GenerateAgentInfo":
		return Scene_GenerateAgentInfo, nil
	case "SceneOpenApi":
		return Scene_SceneOpenApi, nil
	}
	return Scene(0), fmt.Errorf("not a valid Scene string")
}

func ScenePtr(v Scene) *Scene { return &v }
func (p *Scene) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = Scene(result.Int64)
	return
}

func (p *Scene) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}
