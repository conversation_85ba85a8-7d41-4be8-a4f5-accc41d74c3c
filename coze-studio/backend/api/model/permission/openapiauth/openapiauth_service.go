// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package openapiauth

import (
	"context"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

type OpenAPIAuthService interface {
	GetPersonalAccessTokenAndPermission(ctx context.Context, req *GetPersonalAccessTokenAndPermissionRequest) (r *GetPersonalAccessTokenAndPermissionResponse, err error)

	DeletePersonalAccessTokenAndPermission(ctx context.Context, req *DeletePersonalAccessTokenAndPermissionRequest) (r *DeletePersonalAccessTokenAndPermissionResponse, err error)

	ListPersonalAccessTokens(ctx context.Context, req *ListPersonalAccessTokensRequest) (r *ListPersonalAccessTokensResponse, err error)

	CreatePersonalAccessTokenAndPermission(ctx context.Context, req *CreatePersonalAccessTokenAndPermissionRequest) (r *CreatePersonalAccessTokenAndPermissionResponse, err error)

	UpdatePersonalAccessTokenAndPermission(ctx context.Context, req *UpdatePersonalAccessTokenAndPermissionRequest) (r *UpdatePersonalAccessTokenAndPermissionResponse, err error)
}

type OpenAPIAuthServiceClient struct {
	c thrift.TClient
}

func NewOpenAPIAuthServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *OpenAPIAuthServiceClient {
	return &OpenAPIAuthServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewOpenAPIAuthServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *OpenAPIAuthServiceClient {
	return &OpenAPIAuthServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewOpenAPIAuthServiceClient(c thrift.TClient) *OpenAPIAuthServiceClient {
	return &OpenAPIAuthServiceClient{
		c: c,
	}
}

func (p *OpenAPIAuthServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *OpenAPIAuthServiceClient) GetPersonalAccessTokenAndPermission(ctx context.Context, req *GetPersonalAccessTokenAndPermissionRequest) (r *GetPersonalAccessTokenAndPermissionResponse, err error) {
	var _args OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs
	_args.Req = req
	var _result OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult
	if err = p.Client_().Call(ctx, "GetPersonalAccessTokenAndPermission", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *OpenAPIAuthServiceClient) DeletePersonalAccessTokenAndPermission(ctx context.Context, req *DeletePersonalAccessTokenAndPermissionRequest) (r *DeletePersonalAccessTokenAndPermissionResponse, err error) {
	var _args OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs
	_args.Req = req
	var _result OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult
	if err = p.Client_().Call(ctx, "DeletePersonalAccessTokenAndPermission", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *OpenAPIAuthServiceClient) ListPersonalAccessTokens(ctx context.Context, req *ListPersonalAccessTokensRequest) (r *ListPersonalAccessTokensResponse, err error) {
	var _args OpenAPIAuthServiceListPersonalAccessTokensArgs
	_args.Req = req
	var _result OpenAPIAuthServiceListPersonalAccessTokensResult
	if err = p.Client_().Call(ctx, "ListPersonalAccessTokens", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *OpenAPIAuthServiceClient) CreatePersonalAccessTokenAndPermission(ctx context.Context, req *CreatePersonalAccessTokenAndPermissionRequest) (r *CreatePersonalAccessTokenAndPermissionResponse, err error) {
	var _args OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs
	_args.Req = req
	var _result OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult
	if err = p.Client_().Call(ctx, "CreatePersonalAccessTokenAndPermission", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *OpenAPIAuthServiceClient) UpdatePersonalAccessTokenAndPermission(ctx context.Context, req *UpdatePersonalAccessTokenAndPermissionRequest) (r *UpdatePersonalAccessTokenAndPermissionResponse, err error) {
	var _args OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs
	_args.Req = req
	var _result OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult
	if err = p.Client_().Call(ctx, "UpdatePersonalAccessTokenAndPermission", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

type OpenAPIAuthServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      OpenAPIAuthService
}

func (p *OpenAPIAuthServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *OpenAPIAuthServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *OpenAPIAuthServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewOpenAPIAuthServiceProcessor(handler OpenAPIAuthService) *OpenAPIAuthServiceProcessor {
	self := &OpenAPIAuthServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self.AddToProcessorMap("GetPersonalAccessTokenAndPermission", &openAPIAuthServiceProcessorGetPersonalAccessTokenAndPermission{handler: handler})
	self.AddToProcessorMap("DeletePersonalAccessTokenAndPermission", &openAPIAuthServiceProcessorDeletePersonalAccessTokenAndPermission{handler: handler})
	self.AddToProcessorMap("ListPersonalAccessTokens", &openAPIAuthServiceProcessorListPersonalAccessTokens{handler: handler})
	self.AddToProcessorMap("CreatePersonalAccessTokenAndPermission", &openAPIAuthServiceProcessorCreatePersonalAccessTokenAndPermission{handler: handler})
	self.AddToProcessorMap("UpdatePersonalAccessTokenAndPermission", &openAPIAuthServiceProcessorUpdatePersonalAccessTokenAndPermission{handler: handler})
	return self
}
func (p *OpenAPIAuthServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush(ctx)
	return false, x
}

type openAPIAuthServiceProcessorGetPersonalAccessTokenAndPermission struct {
	handler OpenAPIAuthService
}

func (p *openAPIAuthServiceProcessorGetPersonalAccessTokenAndPermission) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetPersonalAccessTokenAndPermission", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult{}
	var retval *GetPersonalAccessTokenAndPermissionResponse
	if retval, err2 = p.handler.GetPersonalAccessTokenAndPermission(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetPersonalAccessTokenAndPermission: "+err2.Error())
		oprot.WriteMessageBegin("GetPersonalAccessTokenAndPermission", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetPersonalAccessTokenAndPermission", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type openAPIAuthServiceProcessorDeletePersonalAccessTokenAndPermission struct {
	handler OpenAPIAuthService
}

func (p *openAPIAuthServiceProcessorDeletePersonalAccessTokenAndPermission) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("DeletePersonalAccessTokenAndPermission", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult{}
	var retval *DeletePersonalAccessTokenAndPermissionResponse
	if retval, err2 = p.handler.DeletePersonalAccessTokenAndPermission(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DeletePersonalAccessTokenAndPermission: "+err2.Error())
		oprot.WriteMessageBegin("DeletePersonalAccessTokenAndPermission", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("DeletePersonalAccessTokenAndPermission", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type openAPIAuthServiceProcessorListPersonalAccessTokens struct {
	handler OpenAPIAuthService
}

func (p *openAPIAuthServiceProcessorListPersonalAccessTokens) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := OpenAPIAuthServiceListPersonalAccessTokensArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ListPersonalAccessTokens", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := OpenAPIAuthServiceListPersonalAccessTokensResult{}
	var retval *ListPersonalAccessTokensResponse
	if retval, err2 = p.handler.ListPersonalAccessTokens(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ListPersonalAccessTokens: "+err2.Error())
		oprot.WriteMessageBegin("ListPersonalAccessTokens", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ListPersonalAccessTokens", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type openAPIAuthServiceProcessorCreatePersonalAccessTokenAndPermission struct {
	handler OpenAPIAuthService
}

func (p *openAPIAuthServiceProcessorCreatePersonalAccessTokenAndPermission) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("CreatePersonalAccessTokenAndPermission", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult{}
	var retval *CreatePersonalAccessTokenAndPermissionResponse
	if retval, err2 = p.handler.CreatePersonalAccessTokenAndPermission(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CreatePersonalAccessTokenAndPermission: "+err2.Error())
		oprot.WriteMessageBegin("CreatePersonalAccessTokenAndPermission", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("CreatePersonalAccessTokenAndPermission", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type openAPIAuthServiceProcessorUpdatePersonalAccessTokenAndPermission struct {
	handler OpenAPIAuthService
}

func (p *openAPIAuthServiceProcessorUpdatePersonalAccessTokenAndPermission) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UpdatePersonalAccessTokenAndPermission", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult{}
	var retval *UpdatePersonalAccessTokenAndPermissionResponse
	if retval, err2 = p.handler.UpdatePersonalAccessTokenAndPermission(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UpdatePersonalAccessTokenAndPermission: "+err2.Error())
		oprot.WriteMessageBegin("UpdatePersonalAccessTokenAndPermission", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UpdatePersonalAccessTokenAndPermission", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs struct {
	Req *GetPersonalAccessTokenAndPermissionRequest `thrift:"req,1"`
}

func NewOpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs() *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs {
	return &OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs{}
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs) InitDefault() {
}

var OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs_Req_DEFAULT *GetPersonalAccessTokenAndPermissionRequest

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs) GetReq() (v *GetPersonalAccessTokenAndPermissionRequest) {
	if !p.IsSetReq() {
		return OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs = map[int16]string{
	1: "req",
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetPersonalAccessTokenAndPermissionRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPersonalAccessTokenAndPermission_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionArgs(%+v)", *p)

}

type OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult struct {
	Success *GetPersonalAccessTokenAndPermissionResponse `thrift:"success,0,optional"`
}

func NewOpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult() *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult {
	return &OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult{}
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult) InitDefault() {
}

var OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult_Success_DEFAULT *GetPersonalAccessTokenAndPermissionResponse

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult) GetSuccess() (v *GetPersonalAccessTokenAndPermissionResponse) {
	if !p.IsSetSuccess() {
		return OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult = map[int16]string{
	0: "success",
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetPersonalAccessTokenAndPermissionResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPersonalAccessTokenAndPermission_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPIAuthServiceGetPersonalAccessTokenAndPermissionResult(%+v)", *p)

}

type OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs struct {
	Req *DeletePersonalAccessTokenAndPermissionRequest `thrift:"req,1"`
}

func NewOpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs() *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs {
	return &OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs{}
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs) InitDefault() {
}

var OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs_Req_DEFAULT *DeletePersonalAccessTokenAndPermissionRequest

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs) GetReq() (v *DeletePersonalAccessTokenAndPermissionRequest) {
	if !p.IsSetReq() {
		return OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs = map[int16]string{
	1: "req",
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDeletePersonalAccessTokenAndPermissionRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeletePersonalAccessTokenAndPermission_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionArgs(%+v)", *p)

}

type OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult struct {
	Success *DeletePersonalAccessTokenAndPermissionResponse `thrift:"success,0,optional"`
}

func NewOpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult() *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult {
	return &OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult{}
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult) InitDefault() {
}

var OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult_Success_DEFAULT *DeletePersonalAccessTokenAndPermissionResponse

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult) GetSuccess() (v *DeletePersonalAccessTokenAndPermissionResponse) {
	if !p.IsSetSuccess() {
		return OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult = map[int16]string{
	0: "success",
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDeletePersonalAccessTokenAndPermissionResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeletePersonalAccessTokenAndPermission_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPIAuthServiceDeletePersonalAccessTokenAndPermissionResult(%+v)", *p)

}

type OpenAPIAuthServiceListPersonalAccessTokensArgs struct {
	Req *ListPersonalAccessTokensRequest `thrift:"req,1"`
}

func NewOpenAPIAuthServiceListPersonalAccessTokensArgs() *OpenAPIAuthServiceListPersonalAccessTokensArgs {
	return &OpenAPIAuthServiceListPersonalAccessTokensArgs{}
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensArgs) InitDefault() {
}

var OpenAPIAuthServiceListPersonalAccessTokensArgs_Req_DEFAULT *ListPersonalAccessTokensRequest

func (p *OpenAPIAuthServiceListPersonalAccessTokensArgs) GetReq() (v *ListPersonalAccessTokensRequest) {
	if !p.IsSetReq() {
		return OpenAPIAuthServiceListPersonalAccessTokensArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_OpenAPIAuthServiceListPersonalAccessTokensArgs = map[int16]string{
	1: "req",
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpenAPIAuthServiceListPersonalAccessTokensArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewListPersonalAccessTokensRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListPersonalAccessTokens_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPIAuthServiceListPersonalAccessTokensArgs(%+v)", *p)

}

type OpenAPIAuthServiceListPersonalAccessTokensResult struct {
	Success *ListPersonalAccessTokensResponse `thrift:"success,0,optional"`
}

func NewOpenAPIAuthServiceListPersonalAccessTokensResult() *OpenAPIAuthServiceListPersonalAccessTokensResult {
	return &OpenAPIAuthServiceListPersonalAccessTokensResult{}
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensResult) InitDefault() {
}

var OpenAPIAuthServiceListPersonalAccessTokensResult_Success_DEFAULT *ListPersonalAccessTokensResponse

func (p *OpenAPIAuthServiceListPersonalAccessTokensResult) GetSuccess() (v *ListPersonalAccessTokensResponse) {
	if !p.IsSetSuccess() {
		return OpenAPIAuthServiceListPersonalAccessTokensResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_OpenAPIAuthServiceListPersonalAccessTokensResult = map[int16]string{
	0: "success",
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpenAPIAuthServiceListPersonalAccessTokensResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewListPersonalAccessTokensResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListPersonalAccessTokens_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *OpenAPIAuthServiceListPersonalAccessTokensResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPIAuthServiceListPersonalAccessTokensResult(%+v)", *p)

}

type OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs struct {
	Req *CreatePersonalAccessTokenAndPermissionRequest `thrift:"req,1"`
}

func NewOpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs() *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs {
	return &OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs{}
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs) InitDefault() {
}

var OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs_Req_DEFAULT *CreatePersonalAccessTokenAndPermissionRequest

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs) GetReq() (v *CreatePersonalAccessTokenAndPermissionRequest) {
	if !p.IsSetReq() {
		return OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs = map[int16]string{
	1: "req",
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreatePersonalAccessTokenAndPermissionRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreatePersonalAccessTokenAndPermission_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionArgs(%+v)", *p)

}

type OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult struct {
	Success *CreatePersonalAccessTokenAndPermissionResponse `thrift:"success,0,optional"`
}

func NewOpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult() *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult {
	return &OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult{}
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult) InitDefault() {
}

var OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult_Success_DEFAULT *CreatePersonalAccessTokenAndPermissionResponse

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult) GetSuccess() (v *CreatePersonalAccessTokenAndPermissionResponse) {
	if !p.IsSetSuccess() {
		return OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult = map[int16]string{
	0: "success",
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCreatePersonalAccessTokenAndPermissionResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreatePersonalAccessTokenAndPermission_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPIAuthServiceCreatePersonalAccessTokenAndPermissionResult(%+v)", *p)

}

type OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs struct {
	Req *UpdatePersonalAccessTokenAndPermissionRequest `thrift:"req,1"`
}

func NewOpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs() *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs {
	return &OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs{}
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs) InitDefault() {
}

var OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs_Req_DEFAULT *UpdatePersonalAccessTokenAndPermissionRequest

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs) GetReq() (v *UpdatePersonalAccessTokenAndPermissionRequest) {
	if !p.IsSetReq() {
		return OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs = map[int16]string{
	1: "req",
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUpdatePersonalAccessTokenAndPermissionRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdatePersonalAccessTokenAndPermission_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionArgs(%+v)", *p)

}

type OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult struct {
	Success *UpdatePersonalAccessTokenAndPermissionResponse `thrift:"success,0,optional"`
}

func NewOpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult() *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult {
	return &OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult{}
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult) InitDefault() {
}

var OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult_Success_DEFAULT *UpdatePersonalAccessTokenAndPermissionResponse

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult) GetSuccess() (v *UpdatePersonalAccessTokenAndPermissionResponse) {
	if !p.IsSetSuccess() {
		return OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult = map[int16]string{
	0: "success",
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUpdatePersonalAccessTokenAndPermissionResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdatePersonalAccessTokenAndPermission_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OpenAPIAuthServiceUpdatePersonalAccessTokenAndPermissionResult(%+v)", *p)

}
