// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package plugin_develop_common

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

type OnlineStatus int64

const (
	OnlineStatus_OFFLINE OnlineStatus = 0
	OnlineStatus_ONLINE  OnlineStatus = 1
)

func (p OnlineStatus) String() string {
	switch p {
	case OnlineStatus_OFFLINE:
		return "OFFLINE"
	case OnlineStatus_ONLINE:
		return "ONLINE"
	}
	return "<UNSET>"
}

func OnlineStatusFromString(s string) (OnlineStatus, error) {
	switch s {
	case "OFFLINE":
		return OnlineStatus_OFFLINE, nil
	case "ONLINE":
		return OnlineStatus_ONLINE, nil
	}
	return OnlineStatus(0), fmt.Errorf("not a valid OnlineStatus string")
}

func OnlineStatusPtr(v OnlineStatus) *OnlineStatus { return &v }
func (p *OnlineStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = OnlineStatus(result.Int64)
	return
}

func (p *OnlineStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type DebugExampleStatus int64

const (
	DebugExampleStatus_Default DebugExampleStatus = 0
	DebugExampleStatus_Enable  DebugExampleStatus = 1
	DebugExampleStatus_Disable DebugExampleStatus = 2
)

func (p DebugExampleStatus) String() string {
	switch p {
	case DebugExampleStatus_Default:
		return "Default"
	case DebugExampleStatus_Enable:
		return "Enable"
	case DebugExampleStatus_Disable:
		return "Disable"
	}
	return "<UNSET>"
}

func DebugExampleStatusFromString(s string) (DebugExampleStatus, error) {
	switch s {
	case "Default":
		return DebugExampleStatus_Default, nil
	case "Enable":
		return DebugExampleStatus_Enable, nil
	case "Disable":
		return DebugExampleStatus_Disable, nil
	}
	return DebugExampleStatus(0), fmt.Errorf("not a valid DebugExampleStatus string")
}

func DebugExampleStatusPtr(v DebugExampleStatus) *DebugExampleStatus { return &v }
func (p *DebugExampleStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DebugExampleStatus(result.Int64)
	return
}

func (p *DebugExampleStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ParameterLocation int64

const (
	ParameterLocation_Path   ParameterLocation = 1
	ParameterLocation_Query  ParameterLocation = 2
	ParameterLocation_Body   ParameterLocation = 3
	ParameterLocation_Header ParameterLocation = 4
)

func (p ParameterLocation) String() string {
	switch p {
	case ParameterLocation_Path:
		return "Path"
	case ParameterLocation_Query:
		return "Query"
	case ParameterLocation_Body:
		return "Body"
	case ParameterLocation_Header:
		return "Header"
	}
	return "<UNSET>"
}

func ParameterLocationFromString(s string) (ParameterLocation, error) {
	switch s {
	case "Path":
		return ParameterLocation_Path, nil
	case "Query":
		return ParameterLocation_Query, nil
	case "Body":
		return ParameterLocation_Body, nil
	case "Header":
		return ParameterLocation_Header, nil
	}
	return ParameterLocation(0), fmt.Errorf("not a valid ParameterLocation string")
}

func ParameterLocationPtr(v ParameterLocation) *ParameterLocation { return &v }
func (p *ParameterLocation) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ParameterLocation(result.Int64)
	return
}

func (p *ParameterLocation) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// plugin枚举值
type PluginParamTypeFormat int64

const (
	PluginParamTypeFormat_FileUrl  PluginParamTypeFormat = 0
	PluginParamTypeFormat_ImageUrl PluginParamTypeFormat = 1
	PluginParamTypeFormat_DocUrl   PluginParamTypeFormat = 2
	PluginParamTypeFormat_CodeUrl  PluginParamTypeFormat = 3
	PluginParamTypeFormat_PptUrl   PluginParamTypeFormat = 4
	PluginParamTypeFormat_TxtUrl   PluginParamTypeFormat = 5
	PluginParamTypeFormat_ExcelUrl PluginParamTypeFormat = 6
	PluginParamTypeFormat_AudioUrl PluginParamTypeFormat = 7
	PluginParamTypeFormat_ZipUrl   PluginParamTypeFormat = 8
	PluginParamTypeFormat_VideoUrl PluginParamTypeFormat = 9
)

func (p PluginParamTypeFormat) String() string {
	switch p {
	case PluginParamTypeFormat_FileUrl:
		return "FileUrl"
	case PluginParamTypeFormat_ImageUrl:
		return "ImageUrl"
	case PluginParamTypeFormat_DocUrl:
		return "DocUrl"
	case PluginParamTypeFormat_CodeUrl:
		return "CodeUrl"
	case PluginParamTypeFormat_PptUrl:
		return "PptUrl"
	case PluginParamTypeFormat_TxtUrl:
		return "TxtUrl"
	case PluginParamTypeFormat_ExcelUrl:
		return "ExcelUrl"
	case PluginParamTypeFormat_AudioUrl:
		return "AudioUrl"
	case PluginParamTypeFormat_ZipUrl:
		return "ZipUrl"
	case PluginParamTypeFormat_VideoUrl:
		return "VideoUrl"
	}
	return "<UNSET>"
}

func PluginParamTypeFormatFromString(s string) (PluginParamTypeFormat, error) {
	switch s {
	case "FileUrl":
		return PluginParamTypeFormat_FileUrl, nil
	case "ImageUrl":
		return PluginParamTypeFormat_ImageUrl, nil
	case "DocUrl":
		return PluginParamTypeFormat_DocUrl, nil
	case "CodeUrl":
		return PluginParamTypeFormat_CodeUrl, nil
	case "PptUrl":
		return PluginParamTypeFormat_PptUrl, nil
	case "TxtUrl":
		return PluginParamTypeFormat_TxtUrl, nil
	case "ExcelUrl":
		return PluginParamTypeFormat_ExcelUrl, nil
	case "AudioUrl":
		return PluginParamTypeFormat_AudioUrl, nil
	case "ZipUrl":
		return PluginParamTypeFormat_ZipUrl, nil
	case "VideoUrl":
		return PluginParamTypeFormat_VideoUrl, nil
	}
	return PluginParamTypeFormat(0), fmt.Errorf("not a valid PluginParamTypeFormat string")
}

func PluginParamTypeFormatPtr(v PluginParamTypeFormat) *PluginParamTypeFormat { return &v }
func (p *PluginParamTypeFormat) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PluginParamTypeFormat(result.Int64)
	return
}

func (p *PluginParamTypeFormat) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type APIMethod int64

const (
	APIMethod_GET    APIMethod = 1
	APIMethod_POST   APIMethod = 2
	APIMethod_PUT    APIMethod = 3
	APIMethod_DELETE APIMethod = 4
	APIMethod_PATCH  APIMethod = 5
)

func (p APIMethod) String() string {
	switch p {
	case APIMethod_GET:
		return "GET"
	case APIMethod_POST:
		return "POST"
	case APIMethod_PUT:
		return "PUT"
	case APIMethod_DELETE:
		return "DELETE"
	case APIMethod_PATCH:
		return "PATCH"
	}
	return "<UNSET>"
}

func APIMethodFromString(s string) (APIMethod, error) {
	switch s {
	case "GET":
		return APIMethod_GET, nil
	case "POST":
		return APIMethod_POST, nil
	case "PUT":
		return APIMethod_PUT, nil
	case "DELETE":
		return APIMethod_DELETE, nil
	case "PATCH":
		return APIMethod_PATCH, nil
	}
	return APIMethod(0), fmt.Errorf("not a valid APIMethod string")
}

func APIMethodPtr(v APIMethod) *APIMethod { return &v }
func (p *APIMethod) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = APIMethod(result.Int64)
	return
}

func (p *APIMethod) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type APIDebugStatus int64

const (
	APIDebugStatus_DebugWaiting APIDebugStatus = 0
	APIDebugStatus_DebugPassed  APIDebugStatus = 1
)

func (p APIDebugStatus) String() string {
	switch p {
	case APIDebugStatus_DebugWaiting:
		return "DebugWaiting"
	case APIDebugStatus_DebugPassed:
		return "DebugPassed"
	}
	return "<UNSET>"
}

func APIDebugStatusFromString(s string) (APIDebugStatus, error) {
	switch s {
	case "DebugWaiting":
		return APIDebugStatus_DebugWaiting, nil
	case "DebugPassed":
		return APIDebugStatus_DebugPassed, nil
	}
	return APIDebugStatus(0), fmt.Errorf("not a valid APIDebugStatus string")
}

func APIDebugStatusPtr(v APIDebugStatus) *APIDebugStatus { return &v }
func (p *APIDebugStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = APIDebugStatus(result.Int64)
	return
}

func (p *APIDebugStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ParameterType int64

const (
	ParameterType_String  ParameterType = 1
	ParameterType_Integer ParameterType = 2
	ParameterType_Number  ParameterType = 3
	ParameterType_Object  ParameterType = 4
	ParameterType_Array   ParameterType = 5
	ParameterType_Bool    ParameterType = 6
)

func (p ParameterType) String() string {
	switch p {
	case ParameterType_String:
		return "String"
	case ParameterType_Integer:
		return "Integer"
	case ParameterType_Number:
		return "Number"
	case ParameterType_Object:
		return "Object"
	case ParameterType_Array:
		return "Array"
	case ParameterType_Bool:
		return "Bool"
	}
	return "<UNSET>"
}

func ParameterTypeFromString(s string) (ParameterType, error) {
	switch s {
	case "String":
		return ParameterType_String, nil
	case "Integer":
		return ParameterType_Integer, nil
	case "Number":
		return ParameterType_Number, nil
	case "Object":
		return ParameterType_Object, nil
	case "Array":
		return ParameterType_Array, nil
	case "Bool":
		return ParameterType_Bool, nil
	}
	return ParameterType(0), fmt.Errorf("not a valid ParameterType string")
}

func ParameterTypePtr(v ParameterType) *ParameterType { return &v }
func (p *ParameterType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ParameterType(result.Int64)
	return
}

func (p *ParameterType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// 默认入参的设置来源
type DefaultParamSource int64

const (
	// 默认用户输入
	DefaultParamSource_Input DefaultParamSource = 0
	// 引用变量
	DefaultParamSource_Variable DefaultParamSource = 1
)

func (p DefaultParamSource) String() string {
	switch p {
	case DefaultParamSource_Input:
		return "Input"
	case DefaultParamSource_Variable:
		return "Variable"
	}
	return "<UNSET>"
}

func DefaultParamSourceFromString(s string) (DefaultParamSource, error) {
	switch s {
	case "Input":
		return DefaultParamSource_Input, nil
	case "Variable":
		return DefaultParamSource_Variable, nil
	}
	return DefaultParamSource(0), fmt.Errorf("not a valid DefaultParamSource string")
}

func DefaultParamSourcePtr(v DefaultParamSource) *DefaultParamSource { return &v }
func (p *DefaultParamSource) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DefaultParamSource(result.Int64)
	return
}

func (p *DefaultParamSource) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// 针对File类型参数的细分类型
type AssistParameterType int64

const (
	AssistParameterType_DEFAULT AssistParameterType = 1
	AssistParameterType_IMAGE   AssistParameterType = 2
	AssistParameterType_DOC     AssistParameterType = 3
	AssistParameterType_CODE    AssistParameterType = 4
	AssistParameterType_PPT     AssistParameterType = 5
	AssistParameterType_TXT     AssistParameterType = 6
	AssistParameterType_EXCEL   AssistParameterType = 7
	AssistParameterType_AUDIO   AssistParameterType = 8
	AssistParameterType_ZIP     AssistParameterType = 9
	AssistParameterType_VIDEO   AssistParameterType = 10
	// 语音
	AssistParameterType_VOICE AssistParameterType = 12
)

func (p AssistParameterType) String() string {
	switch p {
	case AssistParameterType_DEFAULT:
		return "DEFAULT"
	case AssistParameterType_IMAGE:
		return "IMAGE"
	case AssistParameterType_DOC:
		return "DOC"
	case AssistParameterType_CODE:
		return "CODE"
	case AssistParameterType_PPT:
		return "PPT"
	case AssistParameterType_TXT:
		return "TXT"
	case AssistParameterType_EXCEL:
		return "EXCEL"
	case AssistParameterType_AUDIO:
		return "AUDIO"
	case AssistParameterType_ZIP:
		return "ZIP"
	case AssistParameterType_VIDEO:
		return "VIDEO"
	case AssistParameterType_VOICE:
		return "VOICE"
	}
	return "<UNSET>"
}

func AssistParameterTypeFromString(s string) (AssistParameterType, error) {
	switch s {
	case "DEFAULT":
		return AssistParameterType_DEFAULT, nil
	case "IMAGE":
		return AssistParameterType_IMAGE, nil
	case "DOC":
		return AssistParameterType_DOC, nil
	case "CODE":
		return AssistParameterType_CODE, nil
	case "PPT":
		return AssistParameterType_PPT, nil
	case "TXT":
		return AssistParameterType_TXT, nil
	case "EXCEL":
		return AssistParameterType_EXCEL, nil
	case "AUDIO":
		return AssistParameterType_AUDIO, nil
	case "ZIP":
		return AssistParameterType_ZIP, nil
	case "VIDEO":
		return AssistParameterType_VIDEO, nil
	case "VOICE":
		return AssistParameterType_VOICE, nil
	}
	return AssistParameterType(0), fmt.Errorf("not a valid AssistParameterType string")
}

func AssistParameterTypePtr(v AssistParameterType) *AssistParameterType { return &v }
func (p *AssistParameterType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = AssistParameterType(result.Int64)
	return
}

func (p *AssistParameterType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PluginToolAuthType int64

const (
	// 强授权
	PluginToolAuthType_Required PluginToolAuthType = 0
	// 半匿名授权
	PluginToolAuthType_Supported PluginToolAuthType = 1
	// 不授权
	PluginToolAuthType_Disable PluginToolAuthType = 2
)

func (p PluginToolAuthType) String() string {
	switch p {
	case PluginToolAuthType_Required:
		return "Required"
	case PluginToolAuthType_Supported:
		return "Supported"
	case PluginToolAuthType_Disable:
		return "Disable"
	}
	return "<UNSET>"
}

func PluginToolAuthTypeFromString(s string) (PluginToolAuthType, error) {
	switch s {
	case "Required":
		return PluginToolAuthType_Required, nil
	case "Supported":
		return PluginToolAuthType_Supported, nil
	case "Disable":
		return PluginToolAuthType_Disable, nil
	}
	return PluginToolAuthType(0), fmt.Errorf("not a valid PluginToolAuthType string")
}

func PluginToolAuthTypePtr(v PluginToolAuthType) *PluginToolAuthType { return &v }
func (p *PluginToolAuthType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PluginToolAuthType(result.Int64)
	return
}

func (p *PluginToolAuthType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PluginCardStatus int64

const (
	PluginCardStatus_Latest PluginCardStatus = 1
	// 主卡片版本有升级
	PluginCardStatus_NeedUpdate PluginCardStatus = 2
	// 插件工具出参不匹配
	PluginCardStatus_ParamMisMatch PluginCardStatus = 3
)

func (p PluginCardStatus) String() string {
	switch p {
	case PluginCardStatus_Latest:
		return "Latest"
	case PluginCardStatus_NeedUpdate:
		return "NeedUpdate"
	case PluginCardStatus_ParamMisMatch:
		return "ParamMisMatch"
	}
	return "<UNSET>"
}

func PluginCardStatusFromString(s string) (PluginCardStatus, error) {
	switch s {
	case "Latest":
		return PluginCardStatus_Latest, nil
	case "NeedUpdate":
		return PluginCardStatus_NeedUpdate, nil
	case "ParamMisMatch":
		return PluginCardStatus_ParamMisMatch, nil
	}
	return PluginCardStatus(0), fmt.Errorf("not a valid PluginCardStatus string")
}

func PluginCardStatusPtr(v PluginCardStatus) *PluginCardStatus { return &v }
func (p *PluginCardStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PluginCardStatus(result.Int64)
	return
}

func (p *PluginCardStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PluginType int64

const (
	PluginType_PLUGIN    PluginType = 1
	PluginType_APP       PluginType = 2
	PluginType_FUNC      PluginType = 3
	PluginType_WORKFLOW  PluginType = 4
	PluginType_IMAGEFLOW PluginType = 5
	PluginType_LOCAL     PluginType = 6
)

func (p PluginType) String() string {
	switch p {
	case PluginType_PLUGIN:
		return "PLUGIN"
	case PluginType_APP:
		return "APP"
	case PluginType_FUNC:
		return "FUNC"
	case PluginType_WORKFLOW:
		return "WORKFLOW"
	case PluginType_IMAGEFLOW:
		return "IMAGEFLOW"
	case PluginType_LOCAL:
		return "LOCAL"
	}
	return "<UNSET>"
}

func PluginTypeFromString(s string) (PluginType, error) {
	switch s {
	case "PLUGIN":
		return PluginType_PLUGIN, nil
	case "APP":
		return PluginType_APP, nil
	case "FUNC":
		return PluginType_FUNC, nil
	case "WORKFLOW":
		return PluginType_WORKFLOW, nil
	case "IMAGEFLOW":
		return PluginType_IMAGEFLOW, nil
	case "LOCAL":
		return PluginType_LOCAL, nil
	}
	return PluginType(0), fmt.Errorf("not a valid PluginType string")
}

func PluginTypePtr(v PluginType) *PluginType { return &v }
func (p *PluginType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PluginType(result.Int64)
	return
}

func (p *PluginType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PluginStatus int64

const (
	PluginStatus_SUBMITTED PluginStatus = 1
	PluginStatus_REVIEWING PluginStatus = 2
	PluginStatus_PREPARED  PluginStatus = 3
	PluginStatus_PUBLISHED PluginStatus = 4
	PluginStatus_OFFLINE   PluginStatus = 5
	// 默认值
	PluginStatus_Draft PluginStatus = 0
	// 禁用
	PluginStatus_BANNED PluginStatus = 6
)

func (p PluginStatus) String() string {
	switch p {
	case PluginStatus_SUBMITTED:
		return "SUBMITTED"
	case PluginStatus_REVIEWING:
		return "REVIEWING"
	case PluginStatus_PREPARED:
		return "PREPARED"
	case PluginStatus_PUBLISHED:
		return "PUBLISHED"
	case PluginStatus_OFFLINE:
		return "OFFLINE"
	case PluginStatus_Draft:
		return "Draft"
	case PluginStatus_BANNED:
		return "BANNED"
	}
	return "<UNSET>"
}

func PluginStatusFromString(s string) (PluginStatus, error) {
	switch s {
	case "SUBMITTED":
		return PluginStatus_SUBMITTED, nil
	case "REVIEWING":
		return PluginStatus_REVIEWING, nil
	case "PREPARED":
		return PluginStatus_PREPARED, nil
	case "PUBLISHED":
		return PluginStatus_PUBLISHED, nil
	case "OFFLINE":
		return PluginStatus_OFFLINE, nil
	case "Draft":
		return PluginStatus_Draft, nil
	case "BANNED":
		return PluginStatus_BANNED, nil
	}
	return PluginStatus(0), fmt.Errorf("not a valid PluginStatus string")
}

func PluginStatusPtr(v PluginStatus) *PluginStatus { return &v }
func (p *PluginStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PluginStatus(result.Int64)
	return
}

func (p *PluginStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ProductStatus int64

const (
	ProductStatus_NeverListed ProductStatus = 0
	ProductStatus_Listed      ProductStatus = 1
	ProductStatus_Unlisted    ProductStatus = 2
	ProductStatus_Banned      ProductStatus = 3
)

func (p ProductStatus) String() string {
	switch p {
	case ProductStatus_NeverListed:
		return "NeverListed"
	case ProductStatus_Listed:
		return "Listed"
	case ProductStatus_Unlisted:
		return "Unlisted"
	case ProductStatus_Banned:
		return "Banned"
	}
	return "<UNSET>"
}

func ProductStatusFromString(s string) (ProductStatus, error) {
	switch s {
	case "NeverListed":
		return ProductStatus_NeverListed, nil
	case "Listed":
		return ProductStatus_Listed, nil
	case "Unlisted":
		return ProductStatus_Unlisted, nil
	case "Banned":
		return ProductStatus_Banned, nil
	}
	return ProductStatus(0), fmt.Errorf("not a valid ProductStatus string")
}

func ProductStatusPtr(v ProductStatus) *ProductStatus { return &v }
func (p *ProductStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ProductStatus(result.Int64)
	return
}

func (p *ProductStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ProductUnlistType int64

const (
	ProductUnlistType_ByAdmin ProductUnlistType = 1
	ProductUnlistType_ByUser  ProductUnlistType = 2
)

func (p ProductUnlistType) String() string {
	switch p {
	case ProductUnlistType_ByAdmin:
		return "ByAdmin"
	case ProductUnlistType_ByUser:
		return "ByUser"
	}
	return "<UNSET>"
}

func ProductUnlistTypeFromString(s string) (ProductUnlistType, error) {
	switch s {
	case "ByAdmin":
		return ProductUnlistType_ByAdmin, nil
	case "ByUser":
		return ProductUnlistType_ByUser, nil
	}
	return ProductUnlistType(0), fmt.Errorf("not a valid ProductUnlistType string")
}

func ProductUnlistTypePtr(v ProductUnlistType) *ProductUnlistType { return &v }
func (p *ProductUnlistType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ProductUnlistType(result.Int64)
	return
}

func (p *ProductUnlistType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type CreationMethod int64

const (
	CreationMethod_COZE CreationMethod = 0
	CreationMethod_IDE  CreationMethod = 1
)

func (p CreationMethod) String() string {
	switch p {
	case CreationMethod_COZE:
		return "COZE"
	case CreationMethod_IDE:
		return "IDE"
	}
	return "<UNSET>"
}

func CreationMethodFromString(s string) (CreationMethod, error) {
	switch s {
	case "COZE":
		return CreationMethod_COZE, nil
	case "IDE":
		return CreationMethod_IDE, nil
	}
	return CreationMethod(0), fmt.Errorf("not a valid CreationMethod string")
}

func CreationMethodPtr(v CreationMethod) *CreationMethod { return &v }
func (p *CreationMethod) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = CreationMethod(result.Int64)
	return
}

func (p *CreationMethod) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type APIListOrderBy int64

const (
	APIListOrderBy_CreateTime APIListOrderBy = 1
)

func (p APIListOrderBy) String() string {
	switch p {
	case APIListOrderBy_CreateTime:
		return "CreateTime"
	}
	return "<UNSET>"
}

func APIListOrderByFromString(s string) (APIListOrderBy, error) {
	switch s {
	case "CreateTime":
		return APIListOrderBy_CreateTime, nil
	}
	return APIListOrderBy(0), fmt.Errorf("not a valid APIListOrderBy string")
}

func APIListOrderByPtr(v APIListOrderBy) *APIListOrderBy { return &v }
func (p *APIListOrderBy) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = APIListOrderBy(result.Int64)
	return
}

func (p *APIListOrderBy) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SpaceRoleType int64

const (
	// 默认
	SpaceRoleType_Default SpaceRoleType = 0
	// owner
	SpaceRoleType_Owner SpaceRoleType = 1
	// 管理员
	SpaceRoleType_Admin SpaceRoleType = 2
	// 普通成员
	SpaceRoleType_Member SpaceRoleType = 3
)

func (p SpaceRoleType) String() string {
	switch p {
	case SpaceRoleType_Default:
		return "Default"
	case SpaceRoleType_Owner:
		return "Owner"
	case SpaceRoleType_Admin:
		return "Admin"
	case SpaceRoleType_Member:
		return "Member"
	}
	return "<UNSET>"
}

func SpaceRoleTypeFromString(s string) (SpaceRoleType, error) {
	switch s {
	case "Default":
		return SpaceRoleType_Default, nil
	case "Owner":
		return SpaceRoleType_Owner, nil
	case "Admin":
		return SpaceRoleType_Admin, nil
	case "Member":
		return SpaceRoleType_Member, nil
	}
	return SpaceRoleType(0), fmt.Errorf("not a valid SpaceRoleType string")
}

func SpaceRoleTypePtr(v SpaceRoleType) *SpaceRoleType { return &v }
func (p *SpaceRoleType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SpaceRoleType(result.Int64)
	return
}

func (p *SpaceRoleType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type RunMode int64

const (
	RunMode_DefaultToSync RunMode = 0
	RunMode_Sync          RunMode = 1
	RunMode_Async         RunMode = 2
	RunMode_Streaming     RunMode = 3
)

func (p RunMode) String() string {
	switch p {
	case RunMode_DefaultToSync:
		return "DefaultToSync"
	case RunMode_Sync:
		return "Sync"
	case RunMode_Async:
		return "Async"
	case RunMode_Streaming:
		return "Streaming"
	}
	return "<UNSET>"
}

func RunModeFromString(s string) (RunMode, error) {
	switch s {
	case "DefaultToSync":
		return RunMode_DefaultToSync, nil
	case "Sync":
		return RunMode_Sync, nil
	case "Async":
		return RunMode_Async, nil
	case "Streaming":
		return RunMode_Streaming, nil
	}
	return RunMode(0), fmt.Errorf("not a valid RunMode string")
}

func RunModePtr(v RunMode) *RunMode { return &v }
func (p *RunMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = RunMode(result.Int64)
	return
}

func (p *RunMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type AuthorizationType int64

const (
	AuthorizationType_None    AuthorizationType = 0
	AuthorizationType_Service AuthorizationType = 1
	AuthorizationType_OAuth   AuthorizationType = 3
	// deprecated, the same as OAuth
	AuthorizationType_Standard AuthorizationType = 4
)

func (p AuthorizationType) String() string {
	switch p {
	case AuthorizationType_None:
		return "None"
	case AuthorizationType_Service:
		return "Service"
	case AuthorizationType_OAuth:
		return "OAuth"
	case AuthorizationType_Standard:
		return "Standard"
	}
	return "<UNSET>"
}

func AuthorizationTypeFromString(s string) (AuthorizationType, error) {
	switch s {
	case "None":
		return AuthorizationType_None, nil
	case "Service":
		return AuthorizationType_Service, nil
	case "OAuth":
		return AuthorizationType_OAuth, nil
	case "Standard":
		return AuthorizationType_Standard, nil
	}
	return AuthorizationType(0), fmt.Errorf("not a valid AuthorizationType string")
}

func AuthorizationTypePtr(v AuthorizationType) *AuthorizationType { return &v }
func (p *AuthorizationType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = AuthorizationType(result.Int64)
	return
}

func (p *AuthorizationType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ServiceAuthSubType int64

const (
	ServiceAuthSubType_ApiKey ServiceAuthSubType = 0
	// for opencoze
	ServiceAuthSubType_OAuthAuthorizationCode ServiceAuthSubType = 4
)

func (p ServiceAuthSubType) String() string {
	switch p {
	case ServiceAuthSubType_ApiKey:
		return "ApiKey"
	case ServiceAuthSubType_OAuthAuthorizationCode:
		return "OAuthAuthorizationCode"
	}
	return "<UNSET>"
}

func ServiceAuthSubTypeFromString(s string) (ServiceAuthSubType, error) {
	switch s {
	case "ApiKey":
		return ServiceAuthSubType_ApiKey, nil
	case "OAuthAuthorizationCode":
		return ServiceAuthSubType_OAuthAuthorizationCode, nil
	}
	return ServiceAuthSubType(0), fmt.Errorf("not a valid ServiceAuthSubType string")
}

func ServiceAuthSubTypePtr(v ServiceAuthSubType) *ServiceAuthSubType { return &v }
func (p *ServiceAuthSubType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ServiceAuthSubType(result.Int64)
	return
}

func (p *ServiceAuthSubType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type AuthorizationServiceLocation int64

const (
	AuthorizationServiceLocation_Header AuthorizationServiceLocation = 1
	AuthorizationServiceLocation_Query  AuthorizationServiceLocation = 2
)

func (p AuthorizationServiceLocation) String() string {
	switch p {
	case AuthorizationServiceLocation_Header:
		return "Header"
	case AuthorizationServiceLocation_Query:
		return "Query"
	}
	return "<UNSET>"
}

func AuthorizationServiceLocationFromString(s string) (AuthorizationServiceLocation, error) {
	switch s {
	case "Header":
		return AuthorizationServiceLocation_Header, nil
	case "Query":
		return AuthorizationServiceLocation_Query, nil
	}
	return AuthorizationServiceLocation(0), fmt.Errorf("not a valid AuthorizationServiceLocation string")
}

func AuthorizationServiceLocationPtr(v AuthorizationServiceLocation) *AuthorizationServiceLocation {
	return &v
}
func (p *AuthorizationServiceLocation) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = AuthorizationServiceLocation(result.Int64)
	return
}

func (p *AuthorizationServiceLocation) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PluginReferrerScene int64

const (
	PluginReferrerScene_SingleAgent     PluginReferrerScene = 0
	PluginReferrerScene_WorkflowLlmNode PluginReferrerScene = 1
)

func (p PluginReferrerScene) String() string {
	switch p {
	case PluginReferrerScene_SingleAgent:
		return "SingleAgent"
	case PluginReferrerScene_WorkflowLlmNode:
		return "WorkflowLlmNode"
	}
	return "<UNSET>"
}

func PluginReferrerSceneFromString(s string) (PluginReferrerScene, error) {
	switch s {
	case "SingleAgent":
		return PluginReferrerScene_SingleAgent, nil
	case "WorkflowLlmNode":
		return PluginReferrerScene_WorkflowLlmNode, nil
	}
	return PluginReferrerScene(0), fmt.Errorf("not a valid PluginReferrerScene string")
}

func PluginReferrerScenePtr(v PluginReferrerScene) *PluginReferrerScene { return &v }
func (p *PluginReferrerScene) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PluginReferrerScene(result.Int64)
	return
}

func (p *PluginReferrerScene) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type WorkflowResponseMode int64

const (
	// 模型总结
	WorkflowResponseMode_UseLLM WorkflowResponseMode = 0
	// 不使用模型总结
	WorkflowResponseMode_SkipLLM WorkflowResponseMode = 1
)

func (p WorkflowResponseMode) String() string {
	switch p {
	case WorkflowResponseMode_UseLLM:
		return "UseLLM"
	case WorkflowResponseMode_SkipLLM:
		return "SkipLLM"
	}
	return "<UNSET>"
}

func WorkflowResponseModeFromString(s string) (WorkflowResponseMode, error) {
	switch s {
	case "UseLLM":
		return WorkflowResponseMode_UseLLM, nil
	case "SkipLLM":
		return WorkflowResponseMode_SkipLLM, nil
	}
	return WorkflowResponseMode(0), fmt.Errorf("not a valid WorkflowResponseMode string")
}

func WorkflowResponseModePtr(v WorkflowResponseMode) *WorkflowResponseMode { return &v }
func (p *WorkflowResponseMode) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = WorkflowResponseMode(result.Int64)
	return
}

func (p *WorkflowResponseMode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// 授权状态
type OAuthStatus int64

const (
	OAuthStatus_Authorized   OAuthStatus = 1
	OAuthStatus_Unauthorized OAuthStatus = 2
)

func (p OAuthStatus) String() string {
	switch p {
	case OAuthStatus_Authorized:
		return "Authorized"
	case OAuthStatus_Unauthorized:
		return "Unauthorized"
	}
	return "<UNSET>"
}

func OAuthStatusFromString(s string) (OAuthStatus, error) {
	switch s {
	case "Authorized":
		return OAuthStatus_Authorized, nil
	case "Unauthorized":
		return OAuthStatus_Unauthorized, nil
	}
	return OAuthStatus(0), fmt.Errorf("not a valid OAuthStatus string")
}

func OAuthStatusPtr(v OAuthStatus) *OAuthStatus { return &v }
func (p *OAuthStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = OAuthStatus(result.Int64)
	return
}

func (p *OAuthStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type DebugOperation int64

const (
	// 调试，会保存调试状态，会校验返回值
	DebugOperation_Debug DebugOperation = 1
	// 仅解析返回值结构
	DebugOperation_Parse DebugOperation = 2
)

func (p DebugOperation) String() string {
	switch p {
	case DebugOperation_Debug:
		return "Debug"
	case DebugOperation_Parse:
		return "Parse"
	}
	return "<UNSET>"
}

func DebugOperationFromString(s string) (DebugOperation, error) {
	switch s {
	case "Debug":
		return DebugOperation_Debug, nil
	case "Parse":
		return DebugOperation_Parse, nil
	}
	return DebugOperation(0), fmt.Errorf("not a valid DebugOperation string")
}

func DebugOperationPtr(v DebugOperation) *DebugOperation { return &v }
func (p *DebugOperation) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = DebugOperation(result.Int64)
	return
}

func (p *DebugOperation) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ScopeType int64

const (
	// 所有
	ScopeType_All ScopeType = 0
	// 自己
	ScopeType_Self ScopeType = 1
)

func (p ScopeType) String() string {
	switch p {
	case ScopeType_All:
		return "All"
	case ScopeType_Self:
		return "Self"
	}
	return "<UNSET>"
}

func ScopeTypeFromString(s string) (ScopeType, error) {
	switch s {
	case "All":
		return ScopeType_All, nil
	case "Self":
		return ScopeType_Self, nil
	}
	return ScopeType(0), fmt.Errorf("not a valid ScopeType string")
}

func ScopeTypePtr(v ScopeType) *ScopeType { return &v }
func (p *ScopeType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ScopeType(result.Int64)
	return
}

func (p *ScopeType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type OrderBy int64

const (
	OrderBy_CreateTime  OrderBy = 0
	OrderBy_UpdateTime  OrderBy = 1
	OrderBy_PublishTime OrderBy = 2
	OrderBy_Hot         OrderBy = 3
)

func (p OrderBy) String() string {
	switch p {
	case OrderBy_CreateTime:
		return "CreateTime"
	case OrderBy_UpdateTime:
		return "UpdateTime"
	case OrderBy_PublishTime:
		return "PublishTime"
	case OrderBy_Hot:
		return "Hot"
	}
	return "<UNSET>"
}

func OrderByFromString(s string) (OrderBy, error) {
	switch s {
	case "CreateTime":
		return OrderBy_CreateTime, nil
	case "UpdateTime":
		return OrderBy_UpdateTime, nil
	case "PublishTime":
		return OrderBy_PublishTime, nil
	case "Hot":
		return OrderBy_Hot, nil
	}
	return OrderBy(0), fmt.Errorf("not a valid OrderBy string")
}

func OrderByPtr(v OrderBy) *OrderBy { return &v }
func (p *OrderBy) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = OrderBy(result.Int64)
	return
}

func (p *OrderBy) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PluginTypeForFilter int64

const (
	// 包含PLUGIN和APP
	PluginTypeForFilter_CloudPlugin PluginTypeForFilter = 1
	// 包含LOCAL
	PluginTypeForFilter_LocalPlugin PluginTypeForFilter = 2
	// 包含WORKFLOW和IMAGEFLOW
	PluginTypeForFilter_WorkflowPlugin PluginTypeForFilter = 3
)

func (p PluginTypeForFilter) String() string {
	switch p {
	case PluginTypeForFilter_CloudPlugin:
		return "CloudPlugin"
	case PluginTypeForFilter_LocalPlugin:
		return "LocalPlugin"
	case PluginTypeForFilter_WorkflowPlugin:
		return "WorkflowPlugin"
	}
	return "<UNSET>"
}

func PluginTypeForFilterFromString(s string) (PluginTypeForFilter, error) {
	switch s {
	case "CloudPlugin":
		return PluginTypeForFilter_CloudPlugin, nil
	case "LocalPlugin":
		return PluginTypeForFilter_LocalPlugin, nil
	case "WorkflowPlugin":
		return PluginTypeForFilter_WorkflowPlugin, nil
	}
	return PluginTypeForFilter(0), fmt.Errorf("not a valid PluginTypeForFilter string")
}

func PluginTypeForFilterPtr(v PluginTypeForFilter) *PluginTypeForFilter { return &v }
func (p *PluginTypeForFilter) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PluginTypeForFilter(result.Int64)
	return
}

func (p *PluginTypeForFilter) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PluginDataFormat int64

const (
	PluginDataFormat_OpenAPI PluginDataFormat = 1
	PluginDataFormat_Curl    PluginDataFormat = 2
	PluginDataFormat_Postman PluginDataFormat = 3
	PluginDataFormat_Swagger PluginDataFormat = 4
)

func (p PluginDataFormat) String() string {
	switch p {
	case PluginDataFormat_OpenAPI:
		return "OpenAPI"
	case PluginDataFormat_Curl:
		return "Curl"
	case PluginDataFormat_Postman:
		return "Postman"
	case PluginDataFormat_Swagger:
		return "Swagger"
	}
	return "<UNSET>"
}

func PluginDataFormatFromString(s string) (PluginDataFormat, error) {
	switch s {
	case "OpenAPI":
		return PluginDataFormat_OpenAPI, nil
	case "Curl":
		return PluginDataFormat_Curl, nil
	case "Postman":
		return PluginDataFormat_Postman, nil
	case "Swagger":
		return PluginDataFormat_Swagger, nil
	}
	return PluginDataFormat(0), fmt.Errorf("not a valid PluginDataFormat string")
}

func PluginDataFormatPtr(v PluginDataFormat) *PluginDataFormat { return &v }
func (p *PluginDataFormat) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PluginDataFormat(result.Int64)
	return
}

func (p *PluginDataFormat) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ResponseStyle struct {
	WorkflowResponseMode WorkflowResponseMode `thrift:"workflow_response_mode,1" form:"workflow_response_mode" json:"workflow_response_mode" query:"workflow_response_mode"`
}

func NewResponseStyle() *ResponseStyle {
	return &ResponseStyle{}
}

func (p *ResponseStyle) InitDefault() {
}

func (p *ResponseStyle) GetWorkflowResponseMode() (v WorkflowResponseMode) {
	return p.WorkflowResponseMode
}

var fieldIDToName_ResponseStyle = map[int16]string{
	1: "workflow_response_mode",
}

func (p *ResponseStyle) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResponseStyle[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResponseStyle) ReadField1(iprot thrift.TProtocol) error {

	var _field WorkflowResponseMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = WorkflowResponseMode(v)
	}
	p.WorkflowResponseMode = _field
	return nil
}

func (p *ResponseStyle) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResponseStyle"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResponseStyle) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("workflow_response_mode", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.WorkflowResponseMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResponseStyle) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResponseStyle(%+v)", *p)

}

type CodeInfo struct {
	// plugin manifest in json string
	PluginDesc string `thrift:"plugin_desc,1" form:"plugin_desc" json:"plugin_desc" query:"plugin_desc"`
	// plugin openapi3 document in yaml string
	OpenapiDesc  string `thrift:"openapi_desc,2" form:"openapi_desc" json:"openapi_desc" query:"openapi_desc"`
	ClientID     string `thrift:"client_id,3" form:"client_id" json:"client_id" query:"client_id"`
	ClientSecret string `thrift:"client_secret,4" form:"client_secret" json:"client_secret" query:"client_secret"`
	ServiceToken string `thrift:"service_token,5" form:"service_token" json:"service_token" query:"service_token"`
}

func NewCodeInfo() *CodeInfo {
	return &CodeInfo{}
}

func (p *CodeInfo) InitDefault() {
}

func (p *CodeInfo) GetPluginDesc() (v string) {
	return p.PluginDesc
}

func (p *CodeInfo) GetOpenapiDesc() (v string) {
	return p.OpenapiDesc
}

func (p *CodeInfo) GetClientID() (v string) {
	return p.ClientID
}

func (p *CodeInfo) GetClientSecret() (v string) {
	return p.ClientSecret
}

func (p *CodeInfo) GetServiceToken() (v string) {
	return p.ServiceToken
}

var fieldIDToName_CodeInfo = map[int16]string{
	1: "plugin_desc",
	2: "openapi_desc",
	3: "client_id",
	4: "client_secret",
	5: "service_token",
}

func (p *CodeInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CodeInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CodeInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginDesc = _field
	return nil
}
func (p *CodeInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OpenapiDesc = _field
	return nil
}
func (p *CodeInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ClientID = _field
	return nil
}
func (p *CodeInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ClientSecret = _field
	return nil
}
func (p *CodeInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ServiceToken = _field
	return nil
}

func (p *CodeInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CodeInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CodeInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_desc", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PluginDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CodeInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("openapi_desc", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OpenapiDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CodeInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("client_id", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ClientID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CodeInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("client_secret", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ClientSecret); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CodeInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("service_token", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ServiceToken); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CodeInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CodeInfo(%+v)", *p)

}

type APIListOrder struct {
	OrderBy APIListOrderBy `thrift:"order_by,1" form:"order_by" json:"order_by" query:"order_by"`
	Desc    bool           `thrift:"desc,2" form:"desc" json:"desc" query:"desc"`
}

func NewAPIListOrder() *APIListOrder {
	return &APIListOrder{}
}

func (p *APIListOrder) InitDefault() {
}

func (p *APIListOrder) GetOrderBy() (v APIListOrderBy) {
	return p.OrderBy
}

func (p *APIListOrder) GetDesc() (v bool) {
	return p.Desc
}

var fieldIDToName_APIListOrder = map[int16]string{
	1: "order_by",
	2: "desc",
}

func (p *APIListOrder) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIListOrder[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIListOrder) ReadField1(iprot thrift.TProtocol) error {

	var _field APIListOrderBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = APIListOrderBy(v)
	}
	p.OrderBy = _field
	return nil
}
func (p *APIListOrder) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Desc = _field
	return nil
}

func (p *APIListOrder) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("APIListOrder"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIListOrder) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_by", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.OrderBy)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *APIListOrder) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("desc", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Desc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *APIListOrder) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIListOrder(%+v)", *p)

}

type UserLabel struct {
	LabelID   string `thrift:"label_id,1" form:"label_id" json:"label_id" query:"label_id"`
	LabelName string `thrift:"label_name,2" form:"label_name" json:"label_name" query:"label_name"`
	IconURI   string `thrift:"icon_uri,3" form:"icon_uri" json:"icon_uri" query:"icon_uri"`
	IconURL   string `thrift:"icon_url,4" form:"icon_url" json:"icon_url" query:"icon_url"`
	JumpLink  string `thrift:"jump_link,5" form:"jump_link" json:"jump_link" query:"jump_link"`
}

func NewUserLabel() *UserLabel {
	return &UserLabel{}
}

func (p *UserLabel) InitDefault() {
}

func (p *UserLabel) GetLabelID() (v string) {
	return p.LabelID
}

func (p *UserLabel) GetLabelName() (v string) {
	return p.LabelName
}

func (p *UserLabel) GetIconURI() (v string) {
	return p.IconURI
}

func (p *UserLabel) GetIconURL() (v string) {
	return p.IconURL
}

func (p *UserLabel) GetJumpLink() (v string) {
	return p.JumpLink
}

var fieldIDToName_UserLabel = map[int16]string{
	1: "label_id",
	2: "label_name",
	3: "icon_uri",
	4: "icon_url",
	5: "jump_link",
}

func (p *UserLabel) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UserLabel[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UserLabel) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LabelID = _field
	return nil
}
func (p *UserLabel) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LabelName = _field
	return nil
}
func (p *UserLabel) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IconURI = _field
	return nil
}
func (p *UserLabel) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IconURL = _field
	return nil
}
func (p *UserLabel) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.JumpLink = _field
	return nil
}

func (p *UserLabel) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserLabel"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UserLabel) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("label_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LabelID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UserLabel) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("label_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LabelName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UserLabel) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("icon_uri", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IconURI); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UserLabel) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("icon_url", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IconURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UserLabel) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("jump_link", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.JumpLink); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *UserLabel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserLabel(%+v)", *p)

}

type PluginMetaInfo struct {
	// 插件名
	Name string `thrift:"name,1" form:"name" json:"name" query:"name"`
	// 插件描述
	Desc string `thrift:"desc,2" form:"desc" json:"desc" query:"desc"`
	// 插件服务地址前缀
	URL string `thrift:"url,3" form:"url" json:"url" query:"url"`
	// 插件图标
	Icon *PluginIcon `thrift:"icon,4" form:"icon" json:"icon" query:"icon"`
	// 插件授权类型，0：无授权，1：service，3：oauth
	AuthType []AuthorizationType `thrift:"auth_type,5" form:"auth_type" json:"auth_type" query:"auth_type"`
	// 子授权类型为api/token时，token参数位置
	Location *AuthorizationServiceLocation `thrift:"location,6,optional" form:"location" json:"location,omitempty" query:"location"`
	// 子授权类型为api/token时，token参数key
	Key *string `thrift:"key,7,optional" form:"key" json:"key,omitempty" query:"key"`
	// 子授权类型为api/token时，token参数值
	ServiceToken *string `thrift:"service_token,8,optional" form:"service_token" json:"service_token,omitempty" query:"service_token"`
	// 子授权类型为oauth时，oauth信息
	OauthInfo *string `thrift:"oauth_info,9,optional" form:"oauth_info" json:"oauth_info,omitempty" query:"oauth_info"`
	// 插件公共参数，key为参数位置，value为参数列表
	CommonParams map[ParameterLocation][]*CommonParamSchema `thrift:"common_params,10,optional" form:"common_params" json:"common_params,omitempty" query:"common_params"`
	// 子授权类型，0: api/token of service, 10: client credentials of oauth
	SubAuthType *int32 `thrift:"sub_auth_type,11,optional" form:"sub_auth_type" json:"sub_auth_type,omitempty" query:"sub_auth_type"`
	// 可忽略
	AuthPayload *string `thrift:"auth_payload,12,optional" form:"auth_payload" json:"auth_payload,omitempty" query:"auth_payload"`
	// 可忽略
	FixedExportIP bool `thrift:"fixed_export_ip,13" form:"fixed_export_ip" json:"fixed_export_ip" query:"fixed_export_ip"`
}

func NewPluginMetaInfo() *PluginMetaInfo {
	return &PluginMetaInfo{}
}

func (p *PluginMetaInfo) InitDefault() {
}

func (p *PluginMetaInfo) GetName() (v string) {
	return p.Name
}

func (p *PluginMetaInfo) GetDesc() (v string) {
	return p.Desc
}

func (p *PluginMetaInfo) GetURL() (v string) {
	return p.URL
}

var PluginMetaInfo_Icon_DEFAULT *PluginIcon

func (p *PluginMetaInfo) GetIcon() (v *PluginIcon) {
	if !p.IsSetIcon() {
		return PluginMetaInfo_Icon_DEFAULT
	}
	return p.Icon
}

func (p *PluginMetaInfo) GetAuthType() (v []AuthorizationType) {
	return p.AuthType
}

var PluginMetaInfo_Location_DEFAULT AuthorizationServiceLocation

func (p *PluginMetaInfo) GetLocation() (v AuthorizationServiceLocation) {
	if !p.IsSetLocation() {
		return PluginMetaInfo_Location_DEFAULT
	}
	return *p.Location
}

var PluginMetaInfo_Key_DEFAULT string

func (p *PluginMetaInfo) GetKey() (v string) {
	if !p.IsSetKey() {
		return PluginMetaInfo_Key_DEFAULT
	}
	return *p.Key
}

var PluginMetaInfo_ServiceToken_DEFAULT string

func (p *PluginMetaInfo) GetServiceToken() (v string) {
	if !p.IsSetServiceToken() {
		return PluginMetaInfo_ServiceToken_DEFAULT
	}
	return *p.ServiceToken
}

var PluginMetaInfo_OauthInfo_DEFAULT string

func (p *PluginMetaInfo) GetOauthInfo() (v string) {
	if !p.IsSetOauthInfo() {
		return PluginMetaInfo_OauthInfo_DEFAULT
	}
	return *p.OauthInfo
}

var PluginMetaInfo_CommonParams_DEFAULT map[ParameterLocation][]*CommonParamSchema

func (p *PluginMetaInfo) GetCommonParams() (v map[ParameterLocation][]*CommonParamSchema) {
	if !p.IsSetCommonParams() {
		return PluginMetaInfo_CommonParams_DEFAULT
	}
	return p.CommonParams
}

var PluginMetaInfo_SubAuthType_DEFAULT int32

func (p *PluginMetaInfo) GetSubAuthType() (v int32) {
	if !p.IsSetSubAuthType() {
		return PluginMetaInfo_SubAuthType_DEFAULT
	}
	return *p.SubAuthType
}

var PluginMetaInfo_AuthPayload_DEFAULT string

func (p *PluginMetaInfo) GetAuthPayload() (v string) {
	if !p.IsSetAuthPayload() {
		return PluginMetaInfo_AuthPayload_DEFAULT
	}
	return *p.AuthPayload
}

func (p *PluginMetaInfo) GetFixedExportIP() (v bool) {
	return p.FixedExportIP
}

var fieldIDToName_PluginMetaInfo = map[int16]string{
	1:  "name",
	2:  "desc",
	3:  "url",
	4:  "icon",
	5:  "auth_type",
	6:  "location",
	7:  "key",
	8:  "service_token",
	9:  "oauth_info",
	10: "common_params",
	11: "sub_auth_type",
	12: "auth_payload",
	13: "fixed_export_ip",
}

func (p *PluginMetaInfo) IsSetIcon() bool {
	return p.Icon != nil
}

func (p *PluginMetaInfo) IsSetLocation() bool {
	return p.Location != nil
}

func (p *PluginMetaInfo) IsSetKey() bool {
	return p.Key != nil
}

func (p *PluginMetaInfo) IsSetServiceToken() bool {
	return p.ServiceToken != nil
}

func (p *PluginMetaInfo) IsSetOauthInfo() bool {
	return p.OauthInfo != nil
}

func (p *PluginMetaInfo) IsSetCommonParams() bool {
	return p.CommonParams != nil
}

func (p *PluginMetaInfo) IsSetSubAuthType() bool {
	return p.SubAuthType != nil
}

func (p *PluginMetaInfo) IsSetAuthPayload() bool {
	return p.AuthPayload != nil
}

func (p *PluginMetaInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PluginMetaInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PluginMetaInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *PluginMetaInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Desc = _field
	return nil
}
func (p *PluginMetaInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.URL = _field
	return nil
}
func (p *PluginMetaInfo) ReadField4(iprot thrift.TProtocol) error {
	_field := NewPluginIcon()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Icon = _field
	return nil
}
func (p *PluginMetaInfo) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]AuthorizationType, 0, size)
	for i := 0; i < size; i++ {

		var _elem AuthorizationType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = AuthorizationType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AuthType = _field
	return nil
}
func (p *PluginMetaInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field *AuthorizationServiceLocation
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AuthorizationServiceLocation(v)
		_field = &tmp
	}
	p.Location = _field
	return nil
}
func (p *PluginMetaInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Key = _field
	return nil
}
func (p *PluginMetaInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ServiceToken = _field
	return nil
}
func (p *PluginMetaInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OauthInfo = _field
	return nil
}
func (p *PluginMetaInfo) ReadField10(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[ParameterLocation][]*CommonParamSchema, size)
	for i := 0; i < size; i++ {
		var _key ParameterLocation
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_key = ParameterLocation(v)
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return err
		}
		_val := make([]*CommonParamSchema, 0, size)
		values := make([]CommonParamSchema, size)
		for i := 0; i < size; i++ {
			_elem := &values[i]
			_elem.InitDefault()

			if err := _elem.Read(iprot); err != nil {
				return err
			}

			_val = append(_val, _elem)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.CommonParams = _field
	return nil
}
func (p *PluginMetaInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SubAuthType = _field
	return nil
}
func (p *PluginMetaInfo) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AuthPayload = _field
	return nil
}
func (p *PluginMetaInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FixedExportIP = _field
	return nil
}

func (p *PluginMetaInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PluginMetaInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PluginMetaInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("desc", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Desc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("url", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.URL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("icon", thrift.STRUCT, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Icon.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("auth_type", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.I32, len(p.AuthType)); err != nil {
		return err
	}
	for _, v := range p.AuthType {
		if err := oprot.WriteI32(int32(v)); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetLocation() {
		if err = oprot.WriteFieldBegin("location", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Location)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetKey() {
		if err = oprot.WriteFieldBegin("key", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Key); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetServiceToken() {
		if err = oprot.WriteFieldBegin("service_token", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ServiceToken); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetOauthInfo() {
		if err = oprot.WriteFieldBegin("oauth_info", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OauthInfo); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetCommonParams() {
		if err = oprot.WriteFieldBegin("common_params", thrift.MAP, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.LIST, len(p.CommonParams)); err != nil {
			return err
		}
		for k, v := range p.CommonParams {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return err
			}
			if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
				return err
			}
			for _, v := range v {
				if err := v.Write(oprot); err != nil {
					return err
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubAuthType() {
		if err = oprot.WriteFieldBegin("sub_auth_type", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.SubAuthType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuthPayload() {
		if err = oprot.WriteFieldBegin("auth_payload", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AuthPayload); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *PluginMetaInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fixed_export_ip", thrift.BOOL, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.FixedExportIP); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *PluginMetaInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PluginMetaInfo(%+v)", *p)

}

type PluginIcon struct {
	URI string `thrift:"uri,1" form:"uri" json:"uri" query:"uri"`
	URL string `thrift:"url,2" form:"url" json:"url" query:"url"`
}

func NewPluginIcon() *PluginIcon {
	return &PluginIcon{}
}

func (p *PluginIcon) InitDefault() {
}

func (p *PluginIcon) GetURI() (v string) {
	return p.URI
}

func (p *PluginIcon) GetURL() (v string) {
	return p.URL
}

var fieldIDToName_PluginIcon = map[int16]string{
	1: "uri",
	2: "url",
}

func (p *PluginIcon) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PluginIcon[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PluginIcon) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.URI = _field
	return nil
}
func (p *PluginIcon) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.URL = _field
	return nil
}

func (p *PluginIcon) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PluginIcon"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PluginIcon) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("uri", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.URI); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PluginIcon) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("url", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.URL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *PluginIcon) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PluginIcon(%+v)", *p)

}

type GetPlaygroundPluginListData struct {
	PluginList []*PluginInfoForPlayground `thrift:"plugin_list,1" form:"plugin_list" json:"plugin_list"`
	Total      int32                      `thrift:"total,2" form:"total" json:"total"`
}

func NewGetPlaygroundPluginListData() *GetPlaygroundPluginListData {
	return &GetPlaygroundPluginListData{}
}

func (p *GetPlaygroundPluginListData) InitDefault() {
}

func (p *GetPlaygroundPluginListData) GetPluginList() (v []*PluginInfoForPlayground) {
	return p.PluginList
}

func (p *GetPlaygroundPluginListData) GetTotal() (v int32) {
	return p.Total
}

var fieldIDToName_GetPlaygroundPluginListData = map[int16]string{
	1: "plugin_list",
	2: "total",
}

func (p *GetPlaygroundPluginListData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetPlaygroundPluginListData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetPlaygroundPluginListData) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PluginInfoForPlayground, 0, size)
	values := make([]PluginInfoForPlayground, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PluginList = _field
	return nil
}
func (p *GetPlaygroundPluginListData) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *GetPlaygroundPluginListData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetPlaygroundPluginListData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetPlaygroundPluginListData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PluginList)); err != nil {
		return err
	}
	for _, v := range p.PluginList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetPlaygroundPluginListData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GetPlaygroundPluginListData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlaygroundPluginListData(%+v)", *p)

}

type PluginInfoForPlayground struct {
	ID string `thrift:"id,1" form:"id" json:"id" query:"id"`
	// name_for_human
	Name string `thrift:"name,2" form:"name" json:"name" query:"name"`
	// description_for_human
	DescForHuman string       `thrift:"desc_for_human,3" form:"desc_for_human" json:"desc_for_human" query:"desc_for_human"`
	PluginIcon   string       `thrift:"plugin_icon,4" form:"plugin_icon" json:"plugin_icon" query:"plugin_icon"`
	PluginType   PluginType   `thrift:"plugin_type,5" form:"plugin_type" json:"plugin_type" query:"plugin_type"`
	Status       PluginStatus `thrift:"status,6" form:"status" json:"status" query:"status"`
	Auth         int32        `thrift:"auth,9" form:"auth" json:"auth" query:"auth"`
	ClientID     string       `thrift:"client_id,10" form:"client_id" json:"client_id" query:"client_id"`
	ClientSecret string       `thrift:"client_secret,11" form:"client_secret" json:"client_secret" query:"client_secret"`
	PluginApis   []*PluginApi `thrift:"plugin_apis,15" form:"plugin_apis" json:"plugin_apis" query:"plugin_apis"`
	// 插件标签
	Tag        int64  `thrift:"tag,16" form:"tag" json:"tag" query:"tag"`
	CreateTime string `thrift:"create_time,17" form:"create_time" json:"create_time" query:"create_time"`
	UpdateTime string `thrift:"update_time,18" form:"update_time" json:"update_time" query:"update_time"`
	// 创建人信息
	Creator *Creator `thrift:"creator,22" form:"creator" json:"creator" query:"creator"`
	// 空间id
	SpaceID string `thrift:"space_id,23" form:"space_id" json:"space_id" query:"space_id"`
	// 插件统计数据
	StatisticData *PluginStatisticData                       `thrift:"statistic_data,24" form:"statistic_data" json:"statistic_data" query:"statistic_data"`
	CommonParams  map[ParameterLocation][]*CommonParamSchema `thrift:"common_params,25,optional" form:"common_params" json:"common_params,omitempty" query:"common_params"`
	// plugin的商品状态
	PluginProductStatus ProductStatus `thrift:"plugin_product_status,26" form:"plugin_product_status" json:"plugin_product_status" query:"plugin_product_status"`
	// plugin商品下架类型
	PluginProductUnlistType ProductUnlistType `thrift:"plugin_product_unlist_type,27" form:"plugin_product_unlist_type" json:"plugin_product_unlist_type" query:"plugin_product_unlist_type"`
	// 素材id
	MaterialID string `thrift:"material_id,28" form:"material_id" json:"material_id" query:"material_id"`
	// 渠道id
	ChannelID int32 `thrift:"channel_id,29" form:"channel_id" json:"channel_id" query:"channel_id"`
	// 插件创建方式
	CreationMethod CreationMethod `thrift:"creation_method,30" form:"creation_method" json:"creation_method" query:"creation_method"`
	// 是否为官方插件
	IsOfficial bool `thrift:"is_official,31" form:"is_official" json:"is_official" query:"is_official"`
	// 项目id
	ProjectID string `thrift:"project_id,32" form:"project_id" json:"project_id" query:"project_id"`
	// 版本号，毫秒时间戳
	VersionTs string `thrift:"version_ts,33" form:"version_ts" json:"version_ts" query:"version_ts"`
	// 版本名称
	VersionName string `thrift:"version_name,34" form:"version_name" json:"version_name" query:"version_name"`
}

func NewPluginInfoForPlayground() *PluginInfoForPlayground {
	return &PluginInfoForPlayground{}
}

func (p *PluginInfoForPlayground) InitDefault() {
}

func (p *PluginInfoForPlayground) GetID() (v string) {
	return p.ID
}

func (p *PluginInfoForPlayground) GetName() (v string) {
	return p.Name
}

func (p *PluginInfoForPlayground) GetDescForHuman() (v string) {
	return p.DescForHuman
}

func (p *PluginInfoForPlayground) GetPluginIcon() (v string) {
	return p.PluginIcon
}

func (p *PluginInfoForPlayground) GetPluginType() (v PluginType) {
	return p.PluginType
}

func (p *PluginInfoForPlayground) GetStatus() (v PluginStatus) {
	return p.Status
}

func (p *PluginInfoForPlayground) GetAuth() (v int32) {
	return p.Auth
}

func (p *PluginInfoForPlayground) GetClientID() (v string) {
	return p.ClientID
}

func (p *PluginInfoForPlayground) GetClientSecret() (v string) {
	return p.ClientSecret
}

func (p *PluginInfoForPlayground) GetPluginApis() (v []*PluginApi) {
	return p.PluginApis
}

func (p *PluginInfoForPlayground) GetTag() (v int64) {
	return p.Tag
}

func (p *PluginInfoForPlayground) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *PluginInfoForPlayground) GetUpdateTime() (v string) {
	return p.UpdateTime
}

var PluginInfoForPlayground_Creator_DEFAULT *Creator

func (p *PluginInfoForPlayground) GetCreator() (v *Creator) {
	if !p.IsSetCreator() {
		return PluginInfoForPlayground_Creator_DEFAULT
	}
	return p.Creator
}

func (p *PluginInfoForPlayground) GetSpaceID() (v string) {
	return p.SpaceID
}

var PluginInfoForPlayground_StatisticData_DEFAULT *PluginStatisticData

func (p *PluginInfoForPlayground) GetStatisticData() (v *PluginStatisticData) {
	if !p.IsSetStatisticData() {
		return PluginInfoForPlayground_StatisticData_DEFAULT
	}
	return p.StatisticData
}

var PluginInfoForPlayground_CommonParams_DEFAULT map[ParameterLocation][]*CommonParamSchema

func (p *PluginInfoForPlayground) GetCommonParams() (v map[ParameterLocation][]*CommonParamSchema) {
	if !p.IsSetCommonParams() {
		return PluginInfoForPlayground_CommonParams_DEFAULT
	}
	return p.CommonParams
}

func (p *PluginInfoForPlayground) GetPluginProductStatus() (v ProductStatus) {
	return p.PluginProductStatus
}

func (p *PluginInfoForPlayground) GetPluginProductUnlistType() (v ProductUnlistType) {
	return p.PluginProductUnlistType
}

func (p *PluginInfoForPlayground) GetMaterialID() (v string) {
	return p.MaterialID
}

func (p *PluginInfoForPlayground) GetChannelID() (v int32) {
	return p.ChannelID
}

func (p *PluginInfoForPlayground) GetCreationMethod() (v CreationMethod) {
	return p.CreationMethod
}

func (p *PluginInfoForPlayground) GetIsOfficial() (v bool) {
	return p.IsOfficial
}

func (p *PluginInfoForPlayground) GetProjectID() (v string) {
	return p.ProjectID
}

func (p *PluginInfoForPlayground) GetVersionTs() (v string) {
	return p.VersionTs
}

func (p *PluginInfoForPlayground) GetVersionName() (v string) {
	return p.VersionName
}

var fieldIDToName_PluginInfoForPlayground = map[int16]string{
	1:  "id",
	2:  "name",
	3:  "desc_for_human",
	4:  "plugin_icon",
	5:  "plugin_type",
	6:  "status",
	9:  "auth",
	10: "client_id",
	11: "client_secret",
	15: "plugin_apis",
	16: "tag",
	17: "create_time",
	18: "update_time",
	22: "creator",
	23: "space_id",
	24: "statistic_data",
	25: "common_params",
	26: "plugin_product_status",
	27: "plugin_product_unlist_type",
	28: "material_id",
	29: "channel_id",
	30: "creation_method",
	31: "is_official",
	32: "project_id",
	33: "version_ts",
	34: "version_name",
}

func (p *PluginInfoForPlayground) IsSetCreator() bool {
	return p.Creator != nil
}

func (p *PluginInfoForPlayground) IsSetStatisticData() bool {
	return p.StatisticData != nil
}

func (p *PluginInfoForPlayground) IsSetCommonParams() bool {
	return p.CommonParams != nil
}

func (p *PluginInfoForPlayground) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 24:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField24(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 25:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField25(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 26:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField26(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 27:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField27(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField28(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField29(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField30(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 31:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField31(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField32(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField33(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField34(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PluginInfoForPlayground[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PluginInfoForPlayground) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DescForHuman = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginIcon = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField5(iprot thrift.TProtocol) error {

	var _field PluginType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = PluginType(v)
	}
	p.PluginType = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField6(iprot thrift.TProtocol) error {

	var _field PluginStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = PluginStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField9(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Auth = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ClientID = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ClientSecret = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PluginApi, 0, size)
	values := make([]PluginApi, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PluginApis = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField16(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Tag = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField18(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField22(iprot thrift.TProtocol) error {
	_field := NewCreator()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Creator = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField23(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SpaceID = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField24(iprot thrift.TProtocol) error {
	_field := NewPluginStatisticData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.StatisticData = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField25(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[ParameterLocation][]*CommonParamSchema, size)
	for i := 0; i < size; i++ {
		var _key ParameterLocation
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_key = ParameterLocation(v)
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return err
		}
		_val := make([]*CommonParamSchema, 0, size)
		values := make([]CommonParamSchema, size)
		for i := 0; i < size; i++ {
			_elem := &values[i]
			_elem.InitDefault()

			if err := _elem.Read(iprot); err != nil {
				return err
			}

			_val = append(_val, _elem)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.CommonParams = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField26(iprot thrift.TProtocol) error {

	var _field ProductStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ProductStatus(v)
	}
	p.PluginProductStatus = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField27(iprot thrift.TProtocol) error {

	var _field ProductUnlistType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ProductUnlistType(v)
	}
	p.PluginProductUnlistType = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField28(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaterialID = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField29(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChannelID = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField30(iprot thrift.TProtocol) error {

	var _field CreationMethod
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = CreationMethod(v)
	}
	p.CreationMethod = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField31(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsOfficial = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField32(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField33(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VersionTs = _field
	return nil
}
func (p *PluginInfoForPlayground) ReadField34(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VersionName = _field
	return nil
}

func (p *PluginInfoForPlayground) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PluginInfoForPlayground"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
		if err = p.writeField24(oprot); err != nil {
			fieldId = 24
			goto WriteFieldError
		}
		if err = p.writeField25(oprot); err != nil {
			fieldId = 25
			goto WriteFieldError
		}
		if err = p.writeField26(oprot); err != nil {
			fieldId = 26
			goto WriteFieldError
		}
		if err = p.writeField27(oprot); err != nil {
			fieldId = 27
			goto WriteFieldError
		}
		if err = p.writeField28(oprot); err != nil {
			fieldId = 28
			goto WriteFieldError
		}
		if err = p.writeField29(oprot); err != nil {
			fieldId = 29
			goto WriteFieldError
		}
		if err = p.writeField30(oprot); err != nil {
			fieldId = 30
			goto WriteFieldError
		}
		if err = p.writeField31(oprot); err != nil {
			fieldId = 31
			goto WriteFieldError
		}
		if err = p.writeField32(oprot); err != nil {
			fieldId = 32
			goto WriteFieldError
		}
		if err = p.writeField33(oprot); err != nil {
			fieldId = 33
			goto WriteFieldError
		}
		if err = p.writeField34(oprot); err != nil {
			fieldId = 34
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PluginInfoForPlayground) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("desc_for_human", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DescForHuman); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_icon", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PluginIcon); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_type", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.PluginType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("status", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("auth", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Auth); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("client_id", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ClientID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("client_secret", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ClientSecret); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_apis", thrift.LIST, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PluginApis)); err != nil {
		return err
	}
	for _, v := range p.PluginApis {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.I64, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Tag); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("create_time", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("update_time", thrift.STRING, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField22(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator", thrift.STRUCT, 22); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Creator.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField23(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("space_id", thrift.STRING, 23); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SpaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField24(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("statistic_data", thrift.STRUCT, 24); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.StatisticData.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField25(oprot thrift.TProtocol) (err error) {
	if p.IsSetCommonParams() {
		if err = oprot.WriteFieldBegin("common_params", thrift.MAP, 25); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.LIST, len(p.CommonParams)); err != nil {
			return err
		}
		for k, v := range p.CommonParams {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return err
			}
			if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
				return err
			}
			for _, v := range v {
				if err := v.Write(oprot); err != nil {
					return err
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField26(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_product_status", thrift.I32, 26); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.PluginProductStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 26 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 26 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField27(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_product_unlist_type", thrift.I32, 27); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.PluginProductUnlistType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 27 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 27 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField28(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("material_id", thrift.STRING, 28); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MaterialID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 28 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 28 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField29(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("channel_id", thrift.I32, 29); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ChannelID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 29 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 29 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField30(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creation_method", thrift.I32, 30); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.CreationMethod)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 30 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 30 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField31(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("is_official", thrift.BOOL, 31); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsOfficial); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 31 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 31 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField32(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("project_id", thrift.STRING, 32); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 32 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 32 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField33(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version_ts", thrift.STRING, 33); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VersionTs); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 33 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 33 end error: ", p), err)
}
func (p *PluginInfoForPlayground) writeField34(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version_name", thrift.STRING, 34); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VersionName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 34 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 34 end error: ", p), err)
}

func (p *PluginInfoForPlayground) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PluginInfoForPlayground(%+v)", *p)

}

type PluginApi struct {
	// operationId
	Name string `thrift:"name,1" form:"name" json:"name" query:"name"`
	// summary
	Desc       string             `thrift:"desc,2" form:"desc" json:"desc" query:"desc"`
	Parameters []*PluginParameter `thrift:"parameters,3" form:"parameters" json:"parameters" query:"parameters"`
	PluginID   string             `thrift:"plugin_id,4" form:"plugin_id" json:"plugin_id" query:"plugin_id"`
	PluginName string             `thrift:"plugin_name,5" form:"plugin_name" json:"plugin_name" query:"plugin_name"`
	// 序号和playground保持一致
	APIID    string `thrift:"api_id,7" form:"api_id" json:"api_id" query:"api_id"`
	RecordID string `thrift:"record_id,8" form:"record_id" json:"record_id" query:"record_id"`
	// 卡片绑定信息，未绑定则为nil
	CardBindingInfo *PresetCardBindingInfo `thrift:"card_binding_info,9,optional" form:"card_binding_info" json:"card_binding_info,omitempty" query:"card_binding_info"`
	// 调试api示例
	DebugExample *DebugExample `thrift:"debug_example,10,optional" form:"debug_example" json:"debug_example,omitempty" query:"debug_example"`
	FunctionName *string       `thrift:"function_name,11,optional" form:"function_name" json:"function_name,omitempty" query:"function_name"`
	// 运行模式
	RunMode RunMode `thrift:"run_mode,12" form:"run_mode" json:"run_mode" query:"run_mode"`
}

func NewPluginApi() *PluginApi {
	return &PluginApi{}
}

func (p *PluginApi) InitDefault() {
}

func (p *PluginApi) GetName() (v string) {
	return p.Name
}

func (p *PluginApi) GetDesc() (v string) {
	return p.Desc
}

func (p *PluginApi) GetParameters() (v []*PluginParameter) {
	return p.Parameters
}

func (p *PluginApi) GetPluginID() (v string) {
	return p.PluginID
}

func (p *PluginApi) GetPluginName() (v string) {
	return p.PluginName
}

func (p *PluginApi) GetAPIID() (v string) {
	return p.APIID
}

func (p *PluginApi) GetRecordID() (v string) {
	return p.RecordID
}

var PluginApi_CardBindingInfo_DEFAULT *PresetCardBindingInfo

func (p *PluginApi) GetCardBindingInfo() (v *PresetCardBindingInfo) {
	if !p.IsSetCardBindingInfo() {
		return PluginApi_CardBindingInfo_DEFAULT
	}
	return p.CardBindingInfo
}

var PluginApi_DebugExample_DEFAULT *DebugExample

func (p *PluginApi) GetDebugExample() (v *DebugExample) {
	if !p.IsSetDebugExample() {
		return PluginApi_DebugExample_DEFAULT
	}
	return p.DebugExample
}

var PluginApi_FunctionName_DEFAULT string

func (p *PluginApi) GetFunctionName() (v string) {
	if !p.IsSetFunctionName() {
		return PluginApi_FunctionName_DEFAULT
	}
	return *p.FunctionName
}

func (p *PluginApi) GetRunMode() (v RunMode) {
	return p.RunMode
}

var fieldIDToName_PluginApi = map[int16]string{
	1:  "name",
	2:  "desc",
	3:  "parameters",
	4:  "plugin_id",
	5:  "plugin_name",
	7:  "api_id",
	8:  "record_id",
	9:  "card_binding_info",
	10: "debug_example",
	11: "function_name",
	12: "run_mode",
}

func (p *PluginApi) IsSetCardBindingInfo() bool {
	return p.CardBindingInfo != nil
}

func (p *PluginApi) IsSetDebugExample() bool {
	return p.DebugExample != nil
}

func (p *PluginApi) IsSetFunctionName() bool {
	return p.FunctionName != nil
}

func (p *PluginApi) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PluginApi[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PluginApi) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *PluginApi) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Desc = _field
	return nil
}
func (p *PluginApi) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PluginParameter, 0, size)
	values := make([]PluginParameter, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Parameters = _field
	return nil
}
func (p *PluginApi) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginID = _field
	return nil
}
func (p *PluginApi) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginName = _field
	return nil
}
func (p *PluginApi) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.APIID = _field
	return nil
}
func (p *PluginApi) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RecordID = _field
	return nil
}
func (p *PluginApi) ReadField9(iprot thrift.TProtocol) error {
	_field := NewPresetCardBindingInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CardBindingInfo = _field
	return nil
}
func (p *PluginApi) ReadField10(iprot thrift.TProtocol) error {
	_field := NewDebugExample()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DebugExample = _field
	return nil
}
func (p *PluginApi) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FunctionName = _field
	return nil
}
func (p *PluginApi) ReadField12(iprot thrift.TProtocol) error {

	var _field RunMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = RunMode(v)
	}
	p.RunMode = _field
	return nil
}

func (p *PluginApi) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PluginApi"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PluginApi) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PluginApi) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("desc", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Desc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PluginApi) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("parameters", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Parameters)); err != nil {
		return err
	}
	for _, v := range p.Parameters {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PluginApi) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_id", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PluginID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PluginApi) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_name", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PluginName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *PluginApi) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("api_id", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.APIID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *PluginApi) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("record_id", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RecordID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *PluginApi) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetCardBindingInfo() {
		if err = oprot.WriteFieldBegin("card_binding_info", thrift.STRUCT, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CardBindingInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *PluginApi) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetDebugExample() {
		if err = oprot.WriteFieldBegin("debug_example", thrift.STRUCT, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DebugExample.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *PluginApi) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetFunctionName() {
		if err = oprot.WriteFieldBegin("function_name", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FunctionName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *PluginApi) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("run_mode", thrift.I32, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.RunMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *PluginApi) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PluginApi(%+v)", *p)

}

type Creator struct {
	ID        string `thrift:"id,1" form:"id" json:"id" query:"id"`
	Name      string `thrift:"name,2" form:"name" json:"name" query:"name"`
	AvatarURL string `thrift:"avatar_url,3" form:"avatar_url" json:"avatar_url" query:"avatar_url"`
	// 是否是自己创建的
	Self          bool          `thrift:"self,4" form:"self" json:"self" query:"self"`
	SpaceRolyType SpaceRoleType `thrift:"space_roly_type,5" form:"space_roly_type" json:"space_roly_type" query:"space_roly_type"`
	// 用户名
	UserUniqueName string `thrift:"user_unique_name,6" form:"user_unique_name" json:"user_unique_name" query:"user_unique_name"`
	// 用户标签
	UserLabel *UserLabel `thrift:"user_label,7" form:"user_label" json:"user_label" query:"user_label"`
}

func NewCreator() *Creator {
	return &Creator{}
}

func (p *Creator) InitDefault() {
}

func (p *Creator) GetID() (v string) {
	return p.ID
}

func (p *Creator) GetName() (v string) {
	return p.Name
}

func (p *Creator) GetAvatarURL() (v string) {
	return p.AvatarURL
}

func (p *Creator) GetSelf() (v bool) {
	return p.Self
}

func (p *Creator) GetSpaceRolyType() (v SpaceRoleType) {
	return p.SpaceRolyType
}

func (p *Creator) GetUserUniqueName() (v string) {
	return p.UserUniqueName
}

var Creator_UserLabel_DEFAULT *UserLabel

func (p *Creator) GetUserLabel() (v *UserLabel) {
	if !p.IsSetUserLabel() {
		return Creator_UserLabel_DEFAULT
	}
	return p.UserLabel
}

var fieldIDToName_Creator = map[int16]string{
	1: "id",
	2: "name",
	3: "avatar_url",
	4: "self",
	5: "space_roly_type",
	6: "user_unique_name",
	7: "user_label",
}

func (p *Creator) IsSetUserLabel() bool {
	return p.UserLabel != nil
}

func (p *Creator) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Creator[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Creator) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *Creator) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *Creator) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AvatarURL = _field
	return nil
}
func (p *Creator) ReadField4(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Self = _field
	return nil
}
func (p *Creator) ReadField5(iprot thrift.TProtocol) error {

	var _field SpaceRoleType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SpaceRoleType(v)
	}
	p.SpaceRolyType = _field
	return nil
}
func (p *Creator) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserUniqueName = _field
	return nil
}
func (p *Creator) ReadField7(iprot thrift.TProtocol) error {
	_field := NewUserLabel()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.UserLabel = _field
	return nil
}

func (p *Creator) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Creator"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Creator) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Creator) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Creator) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("avatar_url", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AvatarURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Creator) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("self", thrift.BOOL, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Self); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Creator) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("space_roly_type", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.SpaceRolyType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Creator) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_unique_name", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserUniqueName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Creator) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_label", thrift.STRUCT, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.UserLabel.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *Creator) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Creator(%+v)", *p)

}

type CommonParamSchema struct {
	Name  string `thrift:"name,1" form:"name" json:"name" query:"name"`
	Value string `thrift:"value,2" form:"value" json:"value" query:"value"`
}

func NewCommonParamSchema() *CommonParamSchema {
	return &CommonParamSchema{}
}

func (p *CommonParamSchema) InitDefault() {
}

func (p *CommonParamSchema) GetName() (v string) {
	return p.Name
}

func (p *CommonParamSchema) GetValue() (v string) {
	return p.Value
}

var fieldIDToName_CommonParamSchema = map[int16]string{
	1: "name",
	2: "value",
}

func (p *CommonParamSchema) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CommonParamSchema[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CommonParamSchema) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *CommonParamSchema) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}

func (p *CommonParamSchema) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("commonParamSchema"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CommonParamSchema) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CommonParamSchema) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("value", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CommonParamSchema) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonParamSchema(%+v)", *p)

}

type PluginParameter struct {
	Name          string             `thrift:"name,1" form:"name" json:"name" query:"name"`
	Desc          string             `thrift:"desc,2" form:"desc" json:"desc" query:"desc"`
	Required      bool               `thrift:"required,3" form:"required" json:"required" query:"required"`
	Type          string             `thrift:"type,4" form:"type" json:"type" query:"type"`
	SubParameters []*PluginParameter `thrift:"sub_parameters,5" form:"sub_parameters" json:"sub_parameters" query:"sub_parameters"`
	// 如果Type是数组，则有subtype
	SubType string `thrift:"sub_type,6" form:"sub_type" json:"sub_type" query:"sub_type"`
	// 如果入参的值是引用的则有fromNodeId
	FromNodeID *string `thrift:"from_node_id,7,optional" form:"from_node_id" json:"from_node_id,omitempty" query:"from_node_id"`
	// 具体引用哪个节点的key
	FromOutput []string `thrift:"from_output,8,optional" form:"from_output" json:"from_output,omitempty" query:"from_output"`
	// 如果入参是用户手输 就放这里
	Value *string `thrift:"value,9,optional" form:"value" json:"value,omitempty" query:"value"`
	// 格式化参数
	Format *PluginParamTypeFormat `thrift:"format,10,optional" form:"format" json:"format,omitempty" query:"format"`
}

func NewPluginParameter() *PluginParameter {
	return &PluginParameter{}
}

func (p *PluginParameter) InitDefault() {
}

func (p *PluginParameter) GetName() (v string) {
	return p.Name
}

func (p *PluginParameter) GetDesc() (v string) {
	return p.Desc
}

func (p *PluginParameter) GetRequired() (v bool) {
	return p.Required
}

func (p *PluginParameter) GetType() (v string) {
	return p.Type
}

func (p *PluginParameter) GetSubParameters() (v []*PluginParameter) {
	return p.SubParameters
}

func (p *PluginParameter) GetSubType() (v string) {
	return p.SubType
}

var PluginParameter_FromNodeID_DEFAULT string

func (p *PluginParameter) GetFromNodeID() (v string) {
	if !p.IsSetFromNodeID() {
		return PluginParameter_FromNodeID_DEFAULT
	}
	return *p.FromNodeID
}

var PluginParameter_FromOutput_DEFAULT []string

func (p *PluginParameter) GetFromOutput() (v []string) {
	if !p.IsSetFromOutput() {
		return PluginParameter_FromOutput_DEFAULT
	}
	return p.FromOutput
}

var PluginParameter_Value_DEFAULT string

func (p *PluginParameter) GetValue() (v string) {
	if !p.IsSetValue() {
		return PluginParameter_Value_DEFAULT
	}
	return *p.Value
}

var PluginParameter_Format_DEFAULT PluginParamTypeFormat

func (p *PluginParameter) GetFormat() (v PluginParamTypeFormat) {
	if !p.IsSetFormat() {
		return PluginParameter_Format_DEFAULT
	}
	return *p.Format
}

var fieldIDToName_PluginParameter = map[int16]string{
	1:  "name",
	2:  "desc",
	3:  "required",
	4:  "type",
	5:  "sub_parameters",
	6:  "sub_type",
	7:  "from_node_id",
	8:  "from_output",
	9:  "value",
	10: "format",
}

func (p *PluginParameter) IsSetFromNodeID() bool {
	return p.FromNodeID != nil
}

func (p *PluginParameter) IsSetFromOutput() bool {
	return p.FromOutput != nil
}

func (p *PluginParameter) IsSetValue() bool {
	return p.Value != nil
}

func (p *PluginParameter) IsSetFormat() bool {
	return p.Format != nil
}

func (p *PluginParameter) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PluginParameter[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PluginParameter) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *PluginParameter) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Desc = _field
	return nil
}
func (p *PluginParameter) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Required = _field
	return nil
}
func (p *PluginParameter) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *PluginParameter) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PluginParameter, 0, size)
	values := make([]PluginParameter, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SubParameters = _field
	return nil
}
func (p *PluginParameter) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SubType = _field
	return nil
}
func (p *PluginParameter) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FromNodeID = _field
	return nil
}
func (p *PluginParameter) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FromOutput = _field
	return nil
}
func (p *PluginParameter) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Value = _field
	return nil
}
func (p *PluginParameter) ReadField10(iprot thrift.TProtocol) error {

	var _field *PluginParamTypeFormat
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := PluginParamTypeFormat(v)
		_field = &tmp
	}
	p.Format = _field
	return nil
}

func (p *PluginParameter) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PluginParameter"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PluginParameter) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PluginParameter) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("desc", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Desc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PluginParameter) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("required", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Required); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PluginParameter) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PluginParameter) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sub_parameters", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SubParameters)); err != nil {
		return err
	}
	for _, v := range p.SubParameters {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *PluginParameter) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sub_type", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SubType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *PluginParameter) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetFromNodeID() {
		if err = oprot.WriteFieldBegin("from_node_id", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FromNodeID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *PluginParameter) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetFromOutput() {
		if err = oprot.WriteFieldBegin("from_output", thrift.LIST, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.FromOutput)); err != nil {
			return err
		}
		for _, v := range p.FromOutput {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *PluginParameter) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetValue() {
		if err = oprot.WriteFieldBegin("value", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Value); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *PluginParameter) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetFormat() {
		if err = oprot.WriteFieldBegin("format", thrift.I32, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Format)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *PluginParameter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PluginParameter(%+v)", *p)

}

type PluginAPIInfo struct {
	PluginID       string          `thrift:"plugin_id,1" form:"plugin_id" json:"plugin_id" query:"plugin_id"`
	APIID          string          `thrift:"api_id,2" form:"api_id" json:"api_id" query:"api_id"`
	Name           string          `thrift:"name,3" form:"name" json:"name" query:"name"`
	Desc           string          `thrift:"desc,4" form:"desc" json:"desc" query:"desc"`
	Path           string          `thrift:"path,5" form:"path" json:"path" query:"path"`
	Method         APIMethod       `thrift:"method,6" form:"method" json:"method" query:"method"`
	RequestParams  []*APIParameter `thrift:"request_params,7" form:"request_params" json:"request_params" query:"request_params"`
	ResponseParams []*APIParameter `thrift:"response_params,8" form:"response_params" json:"response_params" query:"response_params"`
	CreateTime     string          `thrift:"create_time,9" form:"create_time" json:"create_time" query:"create_time"`
	DebugStatus    APIDebugStatus  `thrift:"debug_status,10" form:"debug_status" json:"debug_status" query:"debug_status"`
	// ignore
	Disabled bool `thrift:"disabled,11" form:"disabled" json:"disabled" query:"disabled"`
	// ignore
	StatisticData *PluginStatisticData `thrift:"statistic_data,12" form:"statistic_data" json:"statistic_data" query:"statistic_data"`
	// if tool has been published, online_status is Online
	OnlineStatus OnlineStatus `thrift:"online_status,13" form:"online_status" json:"online_status" query:"online_status"`
	// ignore
	APIExtend *APIExtend `thrift:"api_extend,14" form:"api_extend" json:"api_extend" query:"api_extend"`
	// ignore
	CardBindingInfo *PresetCardBindingInfo `thrift:"card_binding_info,15,optional" form:"card_binding_info" json:"card_binding_info,omitempty" query:"card_binding_info"`
	// 调试示例
	DebugExample *DebugExample `thrift:"debug_example,16,optional" form:"debug_example" json:"debug_example,omitempty" query:"debug_example"`
	// 调试示例状态
	DebugExampleStatus DebugExampleStatus `thrift:"debug_example_status,17" form:"debug_example_status" json:"debug_example_status" query:"debug_example_status"`
	// ignore
	FunctionName string `thrift:"function_name,18" form:"function_name" json:"function_name" query:"function_name"`
}

func NewPluginAPIInfo() *PluginAPIInfo {
	return &PluginAPIInfo{}
}

func (p *PluginAPIInfo) InitDefault() {
}

func (p *PluginAPIInfo) GetPluginID() (v string) {
	return p.PluginID
}

func (p *PluginAPIInfo) GetAPIID() (v string) {
	return p.APIID
}

func (p *PluginAPIInfo) GetName() (v string) {
	return p.Name
}

func (p *PluginAPIInfo) GetDesc() (v string) {
	return p.Desc
}

func (p *PluginAPIInfo) GetPath() (v string) {
	return p.Path
}

func (p *PluginAPIInfo) GetMethod() (v APIMethod) {
	return p.Method
}

func (p *PluginAPIInfo) GetRequestParams() (v []*APIParameter) {
	return p.RequestParams
}

func (p *PluginAPIInfo) GetResponseParams() (v []*APIParameter) {
	return p.ResponseParams
}

func (p *PluginAPIInfo) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *PluginAPIInfo) GetDebugStatus() (v APIDebugStatus) {
	return p.DebugStatus
}

func (p *PluginAPIInfo) GetDisabled() (v bool) {
	return p.Disabled
}

var PluginAPIInfo_StatisticData_DEFAULT *PluginStatisticData

func (p *PluginAPIInfo) GetStatisticData() (v *PluginStatisticData) {
	if !p.IsSetStatisticData() {
		return PluginAPIInfo_StatisticData_DEFAULT
	}
	return p.StatisticData
}

func (p *PluginAPIInfo) GetOnlineStatus() (v OnlineStatus) {
	return p.OnlineStatus
}

var PluginAPIInfo_APIExtend_DEFAULT *APIExtend

func (p *PluginAPIInfo) GetAPIExtend() (v *APIExtend) {
	if !p.IsSetAPIExtend() {
		return PluginAPIInfo_APIExtend_DEFAULT
	}
	return p.APIExtend
}

var PluginAPIInfo_CardBindingInfo_DEFAULT *PresetCardBindingInfo

func (p *PluginAPIInfo) GetCardBindingInfo() (v *PresetCardBindingInfo) {
	if !p.IsSetCardBindingInfo() {
		return PluginAPIInfo_CardBindingInfo_DEFAULT
	}
	return p.CardBindingInfo
}

var PluginAPIInfo_DebugExample_DEFAULT *DebugExample

func (p *PluginAPIInfo) GetDebugExample() (v *DebugExample) {
	if !p.IsSetDebugExample() {
		return PluginAPIInfo_DebugExample_DEFAULT
	}
	return p.DebugExample
}

func (p *PluginAPIInfo) GetDebugExampleStatus() (v DebugExampleStatus) {
	return p.DebugExampleStatus
}

func (p *PluginAPIInfo) GetFunctionName() (v string) {
	return p.FunctionName
}

var fieldIDToName_PluginAPIInfo = map[int16]string{
	1:  "plugin_id",
	2:  "api_id",
	3:  "name",
	4:  "desc",
	5:  "path",
	6:  "method",
	7:  "request_params",
	8:  "response_params",
	9:  "create_time",
	10: "debug_status",
	11: "disabled",
	12: "statistic_data",
	13: "online_status",
	14: "api_extend",
	15: "card_binding_info",
	16: "debug_example",
	17: "debug_example_status",
	18: "function_name",
}

func (p *PluginAPIInfo) IsSetStatisticData() bool {
	return p.StatisticData != nil
}

func (p *PluginAPIInfo) IsSetAPIExtend() bool {
	return p.APIExtend != nil
}

func (p *PluginAPIInfo) IsSetCardBindingInfo() bool {
	return p.CardBindingInfo != nil
}

func (p *PluginAPIInfo) IsSetDebugExample() bool {
	return p.DebugExample != nil
}

func (p *PluginAPIInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PluginAPIInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PluginAPIInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginID = _field
	return nil
}
func (p *PluginAPIInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.APIID = _field
	return nil
}
func (p *PluginAPIInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *PluginAPIInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Desc = _field
	return nil
}
func (p *PluginAPIInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Path = _field
	return nil
}
func (p *PluginAPIInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field APIMethod
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = APIMethod(v)
	}
	p.Method = _field
	return nil
}
func (p *PluginAPIInfo) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*APIParameter, 0, size)
	values := make([]APIParameter, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RequestParams = _field
	return nil
}
func (p *PluginAPIInfo) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*APIParameter, 0, size)
	values := make([]APIParameter, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ResponseParams = _field
	return nil
}
func (p *PluginAPIInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *PluginAPIInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field APIDebugStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = APIDebugStatus(v)
	}
	p.DebugStatus = _field
	return nil
}
func (p *PluginAPIInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Disabled = _field
	return nil
}
func (p *PluginAPIInfo) ReadField12(iprot thrift.TProtocol) error {
	_field := NewPluginStatisticData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.StatisticData = _field
	return nil
}
func (p *PluginAPIInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field OnlineStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = OnlineStatus(v)
	}
	p.OnlineStatus = _field
	return nil
}
func (p *PluginAPIInfo) ReadField14(iprot thrift.TProtocol) error {
	_field := NewAPIExtend()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.APIExtend = _field
	return nil
}
func (p *PluginAPIInfo) ReadField15(iprot thrift.TProtocol) error {
	_field := NewPresetCardBindingInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CardBindingInfo = _field
	return nil
}
func (p *PluginAPIInfo) ReadField16(iprot thrift.TProtocol) error {
	_field := NewDebugExample()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DebugExample = _field
	return nil
}
func (p *PluginAPIInfo) ReadField17(iprot thrift.TProtocol) error {

	var _field DebugExampleStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = DebugExampleStatus(v)
	}
	p.DebugExampleStatus = _field
	return nil
}
func (p *PluginAPIInfo) ReadField18(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FunctionName = _field
	return nil
}

func (p *PluginAPIInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PluginAPIInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PluginAPIInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PluginID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("api_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.APIID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("desc", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Desc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("path", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Path); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("method", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Method)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("request_params", thrift.LIST, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RequestParams)); err != nil {
		return err
	}
	for _, v := range p.RequestParams {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("response_params", thrift.LIST, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResponseParams)); err != nil {
		return err
	}
	for _, v := range p.ResponseParams {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("create_time", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("debug_status", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DebugStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("disabled", thrift.BOOL, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Disabled); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("statistic_data", thrift.STRUCT, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.StatisticData.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("online_status", thrift.I32, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.OnlineStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("api_extend", thrift.STRUCT, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.APIExtend.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetCardBindingInfo() {
		if err = oprot.WriteFieldBegin("card_binding_info", thrift.STRUCT, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CardBindingInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetDebugExample() {
		if err = oprot.WriteFieldBegin("debug_example", thrift.STRUCT, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DebugExample.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("debug_example_status", thrift.I32, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DebugExampleStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *PluginAPIInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("function_name", thrift.STRING, 18); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FunctionName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *PluginAPIInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PluginAPIInfo(%+v)", *p)

}

type APIParameter struct {
	// for前端，无实际意义
	ID string `thrift:"id,1" form:"id" json:"id" query:"id"`
	// parameter name
	Name string `thrift:"name,2" form:"name" json:"name" query:"name"`
	// parameter desc
	Desc string `thrift:"desc,3" form:"desc" json:"desc" query:"desc"`
	// parameter type
	Type ParameterType `thrift:"type,4" form:"type" json:"type" query:"type"`
	// 可忽略
	SubType *ParameterType `thrift:"sub_type,5,optional" form:"sub_type" json:"sub_type,omitempty" query:"sub_type"`
	// 参数位置
	Location ParameterLocation `thrift:"location,6" form:"location" json:"location" query:"location"`
	// 是否必填
	IsRequired bool `thrift:"is_required,7" form:"is_required" json:"is_required" query:"is_required"`
	// 子参数
	SubParameters []*APIParameter `thrift:"sub_parameters,8" form:"sub_parameters" json:"sub_parameters" query:"sub_parameters"`
	// 全局默认值
	GlobalDefault *string `thrift:"global_default,9,optional" form:"global_default" json:"global_default,omitempty" query:"global_default"`
	// 全局是否启用
	GlobalDisable bool `thrift:"global_disable,10" form:"global_disable" json:"global_disable" query:"global_disable"`
	// 智能体内设置的默认值
	LocalDefault *string `thrift:"local_default,11,optional" form:"local_default" json:"local_default,omitempty" query:"local_default"`
	// 智能体内是否启用
	LocalDisable bool `thrift:"local_disable,12" form:"local_disable" json:"local_disable" query:"local_disable"`
	// 可忽略
	DefaultParamSource *DefaultParamSource `thrift:"default_param_source,13,optional" form:"default_param_source" json:"default_param_source,omitempty" query:"default_param_source"`
	// 引用variable的key
	VariableRef *string `thrift:"variable_ref,14,optional" form:"variable_ref" json:"variable_ref,omitempty" query:"variable_ref"`
	// 多模态辅助参数类型
	AssistType *AssistParameterType `thrift:"assist_type,15,optional" form:"assist_type" json:"assist_type,omitempty" query:"assist_type"`
}

func NewAPIParameter() *APIParameter {
	return &APIParameter{}
}

func (p *APIParameter) InitDefault() {
}

func (p *APIParameter) GetID() (v string) {
	return p.ID
}

func (p *APIParameter) GetName() (v string) {
	return p.Name
}

func (p *APIParameter) GetDesc() (v string) {
	return p.Desc
}

func (p *APIParameter) GetType() (v ParameterType) {
	return p.Type
}

var APIParameter_SubType_DEFAULT ParameterType

func (p *APIParameter) GetSubType() (v ParameterType) {
	if !p.IsSetSubType() {
		return APIParameter_SubType_DEFAULT
	}
	return *p.SubType
}

func (p *APIParameter) GetLocation() (v ParameterLocation) {
	return p.Location
}

func (p *APIParameter) GetIsRequired() (v bool) {
	return p.IsRequired
}

func (p *APIParameter) GetSubParameters() (v []*APIParameter) {
	return p.SubParameters
}

var APIParameter_GlobalDefault_DEFAULT string

func (p *APIParameter) GetGlobalDefault() (v string) {
	if !p.IsSetGlobalDefault() {
		return APIParameter_GlobalDefault_DEFAULT
	}
	return *p.GlobalDefault
}

func (p *APIParameter) GetGlobalDisable() (v bool) {
	return p.GlobalDisable
}

var APIParameter_LocalDefault_DEFAULT string

func (p *APIParameter) GetLocalDefault() (v string) {
	if !p.IsSetLocalDefault() {
		return APIParameter_LocalDefault_DEFAULT
	}
	return *p.LocalDefault
}

func (p *APIParameter) GetLocalDisable() (v bool) {
	return p.LocalDisable
}

var APIParameter_DefaultParamSource_DEFAULT DefaultParamSource

func (p *APIParameter) GetDefaultParamSource() (v DefaultParamSource) {
	if !p.IsSetDefaultParamSource() {
		return APIParameter_DefaultParamSource_DEFAULT
	}
	return *p.DefaultParamSource
}

var APIParameter_VariableRef_DEFAULT string

func (p *APIParameter) GetVariableRef() (v string) {
	if !p.IsSetVariableRef() {
		return APIParameter_VariableRef_DEFAULT
	}
	return *p.VariableRef
}

var APIParameter_AssistType_DEFAULT AssistParameterType

func (p *APIParameter) GetAssistType() (v AssistParameterType) {
	if !p.IsSetAssistType() {
		return APIParameter_AssistType_DEFAULT
	}
	return *p.AssistType
}

var fieldIDToName_APIParameter = map[int16]string{
	1:  "id",
	2:  "name",
	3:  "desc",
	4:  "type",
	5:  "sub_type",
	6:  "location",
	7:  "is_required",
	8:  "sub_parameters",
	9:  "global_default",
	10: "global_disable",
	11: "local_default",
	12: "local_disable",
	13: "default_param_source",
	14: "variable_ref",
	15: "assist_type",
}

func (p *APIParameter) IsSetSubType() bool {
	return p.SubType != nil
}

func (p *APIParameter) IsSetGlobalDefault() bool {
	return p.GlobalDefault != nil
}

func (p *APIParameter) IsSetLocalDefault() bool {
	return p.LocalDefault != nil
}

func (p *APIParameter) IsSetDefaultParamSource() bool {
	return p.DefaultParamSource != nil
}

func (p *APIParameter) IsSetVariableRef() bool {
	return p.VariableRef != nil
}

func (p *APIParameter) IsSetAssistType() bool {
	return p.AssistType != nil
}

func (p *APIParameter) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIParameter[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIParameter) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *APIParameter) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *APIParameter) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Desc = _field
	return nil
}
func (p *APIParameter) ReadField4(iprot thrift.TProtocol) error {

	var _field ParameterType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ParameterType(v)
	}
	p.Type = _field
	return nil
}
func (p *APIParameter) ReadField5(iprot thrift.TProtocol) error {

	var _field *ParameterType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ParameterType(v)
		_field = &tmp
	}
	p.SubType = _field
	return nil
}
func (p *APIParameter) ReadField6(iprot thrift.TProtocol) error {

	var _field ParameterLocation
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ParameterLocation(v)
	}
	p.Location = _field
	return nil
}
func (p *APIParameter) ReadField7(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsRequired = _field
	return nil
}
func (p *APIParameter) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*APIParameter, 0, size)
	values := make([]APIParameter, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SubParameters = _field
	return nil
}
func (p *APIParameter) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.GlobalDefault = _field
	return nil
}
func (p *APIParameter) ReadField10(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GlobalDisable = _field
	return nil
}
func (p *APIParameter) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.LocalDefault = _field
	return nil
}
func (p *APIParameter) ReadField12(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LocalDisable = _field
	return nil
}
func (p *APIParameter) ReadField13(iprot thrift.TProtocol) error {

	var _field *DefaultParamSource
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DefaultParamSource(v)
		_field = &tmp
	}
	p.DefaultParamSource = _field
	return nil
}
func (p *APIParameter) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.VariableRef = _field
	return nil
}
func (p *APIParameter) ReadField15(iprot thrift.TProtocol) error {

	var _field *AssistParameterType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AssistParameterType(v)
		_field = &tmp
	}
	p.AssistType = _field
	return nil
}

func (p *APIParameter) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("APIParameter"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIParameter) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *APIParameter) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *APIParameter) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("desc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Desc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *APIParameter) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *APIParameter) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubType() {
		if err = oprot.WriteFieldBegin("sub_type", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SubType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *APIParameter) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("location", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Location)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *APIParameter) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("is_required", thrift.BOOL, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsRequired); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *APIParameter) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sub_parameters", thrift.LIST, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SubParameters)); err != nil {
		return err
	}
	for _, v := range p.SubParameters {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *APIParameter) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetGlobalDefault() {
		if err = oprot.WriteFieldBegin("global_default", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.GlobalDefault); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *APIParameter) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("global_disable", thrift.BOOL, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.GlobalDisable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *APIParameter) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetLocalDefault() {
		if err = oprot.WriteFieldBegin("local_default", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.LocalDefault); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *APIParameter) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("local_disable", thrift.BOOL, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.LocalDisable); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *APIParameter) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetDefaultParamSource() {
		if err = oprot.WriteFieldBegin("default_param_source", thrift.I32, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DefaultParamSource)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *APIParameter) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetVariableRef() {
		if err = oprot.WriteFieldBegin("variable_ref", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.VariableRef); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *APIParameter) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetAssistType() {
		if err = oprot.WriteFieldBegin("assist_type", thrift.I32, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.AssistType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *APIParameter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIParameter(%+v)", *p)

}

type PluginStatisticData struct {
	// 为空就不展示
	BotQuote *int32 `thrift:"bot_quote,1,optional" form:"bot_quote" json:"bot_quote,omitempty" query:"bot_quote"`
}

func NewPluginStatisticData() *PluginStatisticData {
	return &PluginStatisticData{}
}

func (p *PluginStatisticData) InitDefault() {
}

var PluginStatisticData_BotQuote_DEFAULT int32

func (p *PluginStatisticData) GetBotQuote() (v int32) {
	if !p.IsSetBotQuote() {
		return PluginStatisticData_BotQuote_DEFAULT
	}
	return *p.BotQuote
}

var fieldIDToName_PluginStatisticData = map[int16]string{
	1: "bot_quote",
}

func (p *PluginStatisticData) IsSetBotQuote() bool {
	return p.BotQuote != nil
}

func (p *PluginStatisticData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PluginStatisticData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PluginStatisticData) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BotQuote = _field
	return nil
}

func (p *PluginStatisticData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PluginStatisticData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PluginStatisticData) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBotQuote() {
		if err = oprot.WriteFieldBegin("bot_quote", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.BotQuote); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PluginStatisticData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PluginStatisticData(%+v)", *p)

}

type APIExtend struct {
	// tool维度授权类型
	AuthMode PluginToolAuthType `thrift:"auth_mode,1" form:"auth_mode" json:"auth_mode" query:"auth_mode"`
}

func NewAPIExtend() *APIExtend {
	return &APIExtend{}
}

func (p *APIExtend) InitDefault() {
}

func (p *APIExtend) GetAuthMode() (v PluginToolAuthType) {
	return p.AuthMode
}

var fieldIDToName_APIExtend = map[int16]string{
	1: "auth_mode",
}

func (p *APIExtend) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_APIExtend[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *APIExtend) ReadField1(iprot thrift.TProtocol) error {

	var _field PluginToolAuthType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = PluginToolAuthType(v)
	}
	p.AuthMode = _field
	return nil
}

func (p *APIExtend) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("APIExtend"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *APIExtend) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("auth_mode", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.AuthMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *APIExtend) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("APIExtend(%+v)", *p)

}

// 插件预置卡片绑定信息
type PresetCardBindingInfo struct {
	CardID         string           `thrift:"card_id,1" form:"card_id" json:"card_id" query:"card_id"`
	CardVersionNum string           `thrift:"card_version_num,2" form:"card_version_num" json:"card_version_num" query:"card_version_num"`
	Status         PluginCardStatus `thrift:"status,3" form:"status" json:"status" query:"status"`
	// 缩略图
	Thumbnail string `thrift:"thumbnail,4" form:"thumbnail" json:"thumbnail" query:"thumbnail"`
}

func NewPresetCardBindingInfo() *PresetCardBindingInfo {
	return &PresetCardBindingInfo{}
}

func (p *PresetCardBindingInfo) InitDefault() {
}

func (p *PresetCardBindingInfo) GetCardID() (v string) {
	return p.CardID
}

func (p *PresetCardBindingInfo) GetCardVersionNum() (v string) {
	return p.CardVersionNum
}

func (p *PresetCardBindingInfo) GetStatus() (v PluginCardStatus) {
	return p.Status
}

func (p *PresetCardBindingInfo) GetThumbnail() (v string) {
	return p.Thumbnail
}

var fieldIDToName_PresetCardBindingInfo = map[int16]string{
	1: "card_id",
	2: "card_version_num",
	3: "status",
	4: "thumbnail",
}

func (p *PresetCardBindingInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PresetCardBindingInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PresetCardBindingInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CardID = _field
	return nil
}
func (p *PresetCardBindingInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CardVersionNum = _field
	return nil
}
func (p *PresetCardBindingInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field PluginCardStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = PluginCardStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *PresetCardBindingInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Thumbnail = _field
	return nil
}

func (p *PresetCardBindingInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PresetCardBindingInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PresetCardBindingInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("card_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CardID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PresetCardBindingInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("card_version_num", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CardVersionNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PresetCardBindingInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PresetCardBindingInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("thumbnail", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Thumbnail); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *PresetCardBindingInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PresetCardBindingInfo(%+v)", *p)

}

type DebugExample struct {
	// request example in json
	ReqExample string `thrift:"req_example,1" form:"req_example" json:"req_example" query:"req_example"`
	// response example in json
	RespExample string `thrift:"resp_example,2" form:"resp_example" json:"resp_example" query:"resp_example"`
}

func NewDebugExample() *DebugExample {
	return &DebugExample{}
}

func (p *DebugExample) InitDefault() {
}

func (p *DebugExample) GetReqExample() (v string) {
	return p.ReqExample
}

func (p *DebugExample) GetRespExample() (v string) {
	return p.RespExample
}

var fieldIDToName_DebugExample = map[int16]string{
	1: "req_example",
	2: "resp_example",
}

func (p *DebugExample) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DebugExample[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DebugExample) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReqExample = _field
	return nil
}
func (p *DebugExample) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RespExample = _field
	return nil
}

func (p *DebugExample) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DebugExample"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DebugExample) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req_example", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReqExample); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DebugExample) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("resp_example", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RespExample); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DebugExample) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DebugExample(%+v)", *p)

}

type UpdatePluginData struct {
	Res         bool  `thrift:"res,1" form:"res" json:"res" query:"res"`
	EditVersion int32 `thrift:"edit_version,2" form:"edit_version" json:"edit_version" query:"edit_version"`
}

func NewUpdatePluginData() *UpdatePluginData {
	return &UpdatePluginData{}
}

func (p *UpdatePluginData) InitDefault() {
}

func (p *UpdatePluginData) GetRes() (v bool) {
	return p.Res
}

func (p *UpdatePluginData) GetEditVersion() (v int32) {
	return p.EditVersion
}

var fieldIDToName_UpdatePluginData = map[int16]string{
	1: "res",
	2: "edit_version",
}

func (p *UpdatePluginData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdatePluginData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdatePluginData) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Res = _field
	return nil
}
func (p *UpdatePluginData) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EditVersion = _field
	return nil
}

func (p *UpdatePluginData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdatePluginData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdatePluginData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("res", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Res); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdatePluginData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("edit_version", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EditVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UpdatePluginData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdatePluginData(%+v)", *p)

}

type GetUserAuthorityData struct {
	CanEdit          bool `thrift:"can_edit,1" form:"can_edit" json:"can_edit" query:"can_edit"`
	CanRead          bool `thrift:"can_read,2" form:"can_read" json:"can_read" query:"can_read"`
	CanDelete        bool `thrift:"can_delete,3" form:"can_delete" json:"can_delete" query:"can_delete"`
	CanDebug         bool `thrift:"can_debug,4" form:"can_debug" json:"can_debug" query:"can_debug"`
	CanPublish       bool `thrift:"can_publish,5" form:"can_publish" json:"can_publish" query:"can_publish"`
	CanReadChangelog bool `thrift:"can_read_changelog,6" form:"can_read_changelog" json:"can_read_changelog" query:"can_read_changelog"`
}

func NewGetUserAuthorityData() *GetUserAuthorityData {
	return &GetUserAuthorityData{}
}

func (p *GetUserAuthorityData) InitDefault() {
}

func (p *GetUserAuthorityData) GetCanEdit() (v bool) {
	return p.CanEdit
}

func (p *GetUserAuthorityData) GetCanRead() (v bool) {
	return p.CanRead
}

func (p *GetUserAuthorityData) GetCanDelete() (v bool) {
	return p.CanDelete
}

func (p *GetUserAuthorityData) GetCanDebug() (v bool) {
	return p.CanDebug
}

func (p *GetUserAuthorityData) GetCanPublish() (v bool) {
	return p.CanPublish
}

func (p *GetUserAuthorityData) GetCanReadChangelog() (v bool) {
	return p.CanReadChangelog
}

var fieldIDToName_GetUserAuthorityData = map[int16]string{
	1: "can_edit",
	2: "can_read",
	3: "can_delete",
	4: "can_debug",
	5: "can_publish",
	6: "can_read_changelog",
}

func (p *GetUserAuthorityData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetUserAuthorityData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetUserAuthorityData) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CanEdit = _field
	return nil
}
func (p *GetUserAuthorityData) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CanRead = _field
	return nil
}
func (p *GetUserAuthorityData) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CanDelete = _field
	return nil
}
func (p *GetUserAuthorityData) ReadField4(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CanDebug = _field
	return nil
}
func (p *GetUserAuthorityData) ReadField5(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CanPublish = _field
	return nil
}
func (p *GetUserAuthorityData) ReadField6(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CanReadChangelog = _field
	return nil
}

func (p *GetUserAuthorityData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetUserAuthorityData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetUserAuthorityData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("can_edit", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.CanEdit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetUserAuthorityData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("can_read", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.CanRead); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetUserAuthorityData) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("can_delete", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.CanDelete); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetUserAuthorityData) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("can_debug", thrift.BOOL, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.CanDebug); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GetUserAuthorityData) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("can_publish", thrift.BOOL, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.CanPublish); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *GetUserAuthorityData) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("can_read_changelog", thrift.BOOL, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.CanReadChangelog); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *GetUserAuthorityData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetUserAuthorityData(%+v)", *p)

}

type CheckAndLockPluginEditData struct {
	// 是否已被占用
	Occupied bool `thrift:"Occupied,1" form:"Occupied" json:"Occupied" query:"Occupied"`
	// 如果已经被占用了，返回用户ID
	User *Creator `thrift:"user,2" form:"user" json:"user" query:"user"`
	// 是否强占成功
	Seized bool `thrift:"Seized,3" form:"Seized" json:"Seized" query:"Seized"`
}

func NewCheckAndLockPluginEditData() *CheckAndLockPluginEditData {
	return &CheckAndLockPluginEditData{}
}

func (p *CheckAndLockPluginEditData) InitDefault() {
}

func (p *CheckAndLockPluginEditData) GetOccupied() (v bool) {
	return p.Occupied
}

var CheckAndLockPluginEditData_User_DEFAULT *Creator

func (p *CheckAndLockPluginEditData) GetUser() (v *Creator) {
	if !p.IsSetUser() {
		return CheckAndLockPluginEditData_User_DEFAULT
	}
	return p.User
}

func (p *CheckAndLockPluginEditData) GetSeized() (v bool) {
	return p.Seized
}

var fieldIDToName_CheckAndLockPluginEditData = map[int16]string{
	1: "Occupied",
	2: "user",
	3: "Seized",
}

func (p *CheckAndLockPluginEditData) IsSetUser() bool {
	return p.User != nil
}

func (p *CheckAndLockPluginEditData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckAndLockPluginEditData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckAndLockPluginEditData) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Occupied = _field
	return nil
}
func (p *CheckAndLockPluginEditData) ReadField2(iprot thrift.TProtocol) error {
	_field := NewCreator()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.User = _field
	return nil
}
func (p *CheckAndLockPluginEditData) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Seized = _field
	return nil
}

func (p *CheckAndLockPluginEditData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CheckAndLockPluginEditData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckAndLockPluginEditData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Occupied", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Occupied); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CheckAndLockPluginEditData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.User.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CheckAndLockPluginEditData) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Seized", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Seized); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CheckAndLockPluginEditData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckAndLockPluginEditData(%+v)", *p)

}

type PluginPublishInfo struct {
	// 发布人
	PublisherID int64 `thrift:"publisher_id,1" form:"publisher_id" json:"publisher_id,string" query:"publisher_id"`
	// 版本，毫秒时间戳
	VersionTs int64 `thrift:"version_ts,2" form:"version_ts" json:"version_ts" query:"version_ts"`
	// 版本名称
	VersionName string `thrift:"version_name,3" form:"version_name" json:"version_name" query:"version_name"`
	// 版本描述
	VersionDesc string `thrift:"version_desc,4" form:"version_desc" json:"version_desc" query:"version_desc"`
}

func NewPluginPublishInfo() *PluginPublishInfo {
	return &PluginPublishInfo{}
}

func (p *PluginPublishInfo) InitDefault() {
}

func (p *PluginPublishInfo) GetPublisherID() (v int64) {
	return p.PublisherID
}

func (p *PluginPublishInfo) GetVersionTs() (v int64) {
	return p.VersionTs
}

func (p *PluginPublishInfo) GetVersionName() (v string) {
	return p.VersionName
}

func (p *PluginPublishInfo) GetVersionDesc() (v string) {
	return p.VersionDesc
}

var fieldIDToName_PluginPublishInfo = map[int16]string{
	1: "publisher_id",
	2: "version_ts",
	3: "version_name",
	4: "version_desc",
}

func (p *PluginPublishInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PluginPublishInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PluginPublishInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PublisherID = _field
	return nil
}
func (p *PluginPublishInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VersionTs = _field
	return nil
}
func (p *PluginPublishInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VersionName = _field
	return nil
}
func (p *PluginPublishInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.VersionDesc = _field
	return nil
}

func (p *PluginPublishInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PluginPublishInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PluginPublishInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("publisher_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.PublisherID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PluginPublishInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version_ts", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.VersionTs); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PluginPublishInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version_name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VersionName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PluginPublishInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("version_desc", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.VersionDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *PluginPublishInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PluginPublishInfo(%+v)", *p)

}

type RegisterPluginData struct {
	PluginID int64 `thrift:"plugin_id,1" form:"plugin_id" json:"plugin_id,string" query:"plugin_id"`
	// the same as the request 'openapi'
	Openapi string `thrift:"openapi,2" form:"openapi" json:"openapi" query:"openapi"`
}

func NewRegisterPluginData() *RegisterPluginData {
	return &RegisterPluginData{}
}

func (p *RegisterPluginData) InitDefault() {
}

func (p *RegisterPluginData) GetPluginID() (v int64) {
	return p.PluginID
}

func (p *RegisterPluginData) GetOpenapi() (v string) {
	return p.Openapi
}

var fieldIDToName_RegisterPluginData = map[int16]string{
	1: "plugin_id",
	2: "openapi",
}

func (p *RegisterPluginData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RegisterPluginData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RegisterPluginData) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PluginID = _field
	return nil
}
func (p *RegisterPluginData) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Openapi = _field
	return nil
}

func (p *RegisterPluginData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("RegisterPluginData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RegisterPluginData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("plugin_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.PluginID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RegisterPluginData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("openapi", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Openapi); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RegisterPluginData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterPluginData(%+v)", *p)

}

type DuplicateAPIInfo struct {
	Method string `thrift:"method,1" form:"method" json:"method" query:"method"`
	Path   string `thrift:"path,2" form:"path" json:"path" query:"path"`
	Count  int64  `thrift:"count,3" form:"count" json:"count" query:"count"`
}

func NewDuplicateAPIInfo() *DuplicateAPIInfo {
	return &DuplicateAPIInfo{}
}

func (p *DuplicateAPIInfo) InitDefault() {
}

func (p *DuplicateAPIInfo) GetMethod() (v string) {
	return p.Method
}

func (p *DuplicateAPIInfo) GetPath() (v string) {
	return p.Path
}

func (p *DuplicateAPIInfo) GetCount() (v int64) {
	return p.Count
}

var fieldIDToName_DuplicateAPIInfo = map[int16]string{
	1: "method",
	2: "path",
	3: "count",
}

func (p *DuplicateAPIInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DuplicateAPIInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DuplicateAPIInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Method = _field
	return nil
}
func (p *DuplicateAPIInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Path = _field
	return nil
}
func (p *DuplicateAPIInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Count = _field
	return nil
}

func (p *DuplicateAPIInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DuplicateAPIInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DuplicateAPIInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("method", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Method); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DuplicateAPIInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("path", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Path); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DuplicateAPIInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("count", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Count); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DuplicateAPIInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DuplicateAPIInfo(%+v)", *p)

}
