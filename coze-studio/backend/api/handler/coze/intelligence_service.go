/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"
	"fmt"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	"github.com/coze-dev/coze-studio/backend/api/model/intelligence"
	"github.com/coze-dev/coze-studio/backend/api/model/intelligence/common"
	project "github.com/coze-dev/coze-studio/backend/api/model/project"
	publish "github.com/coze-dev/coze-studio/backend/api/model/publish"
	task "github.com/coze-dev/coze-studio/backend/api/model/task"
	appApplication "github.com/coze-dev/coze-studio/backend/application/app"
	"github.com/coze-dev/coze-studio/backend/application/search"
)

// GetDraftIntelligenceList .
// @router /api/intelligence_api/search/get_draft_intelligence_list [POST]
func GetDraftIntelligenceList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req intelligence.GetDraftIntelligenceListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := search.SearchSVC.GetDraftIntelligenceList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetDraftIntelligenceInfo .
// @router /api/intelligence_api/search/get_draft_intelligence_info [POST]
func GetDraftIntelligenceInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req intelligence.GetDraftIntelligenceInfoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.IntelligenceID <= 0 {
		invalidParamRequestResponse(c, "invalid intelligence id")
		return
	}
	if req.IntelligenceType != common.IntelligenceType_Project {
		invalidParamRequestResponse(c, fmt.Sprintf("invalid intelligence type '%d'", req.IntelligenceType))
		return
	}

	resp, err := appApplication.APPApplicationSVC.GetDraftIntelligenceInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetUserRecentlyEditIntelligence .
// @router /api/intelligence_api/search/get_recently_edit_intelligence [POST]
func GetUserRecentlyEditIntelligence(ctx context.Context, c *app.RequestContext) {
	var err error
	var req intelligence.GetUserRecentlyEditIntelligenceRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(intelligence.GetUserRecentlyEditIntelligenceResponse)

	c.JSON(consts.StatusOK, resp)
}

// DraftProjectCreate .
// @router /api/intelligence_api/draft_project/create [POST]
func DraftProjectCreate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req project.DraftProjectCreateRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.SpaceID <= 0 {
		invalidParamRequestResponse(c, "invalid space id")
		return
	}
	if req.Name == "" || len(req.Name) > 256 {
		invalidParamRequestResponse(c, "invalid name")
		return
	}
	if req.IconURI == "" || len(req.IconURI) > 512 {
		invalidParamRequestResponse(c, "invalid icon uri")
		return
	}

	resp, err := appApplication.APPApplicationSVC.DraftProjectCreate(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DraftProjectUpdate .
// @router /api/intelligence_api/draft_project/update [POST]
func DraftProjectUpdate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req project.DraftProjectUpdateRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "invalid project id")
		return
	}
	if req.Name != nil && (len(*req.Name) == 0 || len(*req.Name) > 256) {
		invalidParamRequestResponse(c, "invalid name")
		return
	}
	if req.IconURI != nil && (len(*req.IconURI) == 0 || len(*req.IconURI) > 512) {
		invalidParamRequestResponse(c, "invalid icon uri")
		return
	}

	resp, err := appApplication.APPApplicationSVC.DraftProjectUpdate(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DraftProjectDelete .
// @router /api/intelligence_api/draft_project/delete [POST]
func DraftProjectDelete(ctx context.Context, c *app.RequestContext) {
	var err error
	var req project.DraftProjectDeleteRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "invalid project id")
		return
	}

	resp, err := appApplication.APPApplicationSVC.DraftProjectDelete(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetProjectPublishedConnector .
// @router /api/intelligence_api/publish/get_published_connector [POST]
func GetProjectPublishedConnector(ctx context.Context, c *app.RequestContext) {
	var err error
	var req publish.GetProjectPublishedConnectorRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(publish.GetProjectPublishedConnectorResponse)

	c.JSON(consts.StatusOK, resp)
}

// CheckProjectVersionNumber .
// @router /api/intelligence_api/publish/check_version_number [POST]
func CheckProjectVersionNumber(ctx context.Context, c *app.RequestContext) {
	var err error
	var req publish.CheckProjectVersionNumberRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "invalid project id")
		return
	}
	if req.VersionNumber == "" {
		invalidParamRequestResponse(c, "invalid version number")
		return
	}

	resp, err := appApplication.APPApplicationSVC.CheckProjectVersionNumber(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// PublishProject .
// @router /api/intelligence_api/publish/publish_project [POST]
func PublishProject(ctx context.Context, c *app.RequestContext) {
	var err error
	var req publish.PublishProjectRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "invalid project id")
		return
	}
	if req.VersionNumber == "" {
		invalidParamRequestResponse(c, "invalid version number")
		return
	}

	resp, err := appApplication.APPApplicationSVC.PublishAPP(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetPublishRecordList .
// @router /api/intelligence_api/publish/publish_record_list [POST]
func GetPublishRecordList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req publish.GetPublishRecordListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "invalid project id")
		return
	}

	resp, err := appApplication.APPApplicationSVC.GetPublishRecordList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// ProjectPublishConnectorList .
// @router /api/intelligence_api/publish/connector_list [POST]
func ProjectPublishConnectorList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req publish.PublishConnectorListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "invalid project id")
		return
	}

	resp, err := appApplication.APPApplicationSVC.ProjectPublishConnectorList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetPublishRecordDetail .
// @router /api/intelligence_api/publish/publish_record_detail [POST]
func GetPublishRecordDetail(ctx context.Context, c *app.RequestContext) {
	var err error
	var req publish.GetPublishRecordDetailRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "invalid project id")
		return
	}
	if req.PublishRecordID != nil && *req.PublishRecordID <= 0 {
		invalidParamRequestResponse(c, "invalid publish record id")
		return
	}

	resp, err := appApplication.APPApplicationSVC.GetPublishRecordDetail(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DraftProjectInnerTaskList .
// @router /api/intelligence_api/draft_project/inner_task_list [POST]
func DraftProjectInnerTaskList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req task.DraftProjectInnerTaskListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "invalid project id")
		return
	}

	resp, err := appApplication.APPApplicationSVC.DraftProjectInnerTaskList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DraftProjectCopy .
// @router /api/intelligence_api/draft_project/copy [POST]
func DraftProjectCopy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req project.DraftProjectCopyRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.ProjectID <= 0 {
		invalidParamRequestResponse(c, "invalid project id")
		return
	}
	if req.ToSpaceID <= 0 {
		invalidParamRequestResponse(c, "invalid to space id")
		return
	}
	if req.Name == "" || len(req.Name) > 256 {
		invalidParamRequestResponse(c, "invalid name")
		return
	}
	if req.IconURI == "" || len(req.IconURI) > 512 {
		invalidParamRequestResponse(c, "invalid icon uri")
		return
	}

	resp, err := appApplication.APPApplicationSVC.DraftProjectCopy(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}
