// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameDataCopyTask = "data_copy_task"

// DataCopyTask data方向复制任务记录表
type DataCopyTask struct {
	MasterTaskID  string `gorm:"column:master_task_id;not null;comment:复制任务ID" json:"master_task_id"`             // 复制任务ID
	OriginDataID  int64  `gorm:"column:origin_data_id;not null;comment:源id" json:"origin_data_id"`                // 源id
	TargetDataID  int64  `gorm:"column:target_data_id;not null;comment:目标id" json:"target_data_id"`               // 目标id
	OriginSpaceID int64  `gorm:"column:origin_space_id;not null;comment:源团队空间" json:"origin_space_id"`            // 源团队空间
	TargetSpaceID int64  `gorm:"column:target_space_id;not null;comment:目标团队空间" json:"target_space_id"`           // 目标团队空间
	OriginUserID  int64  `gorm:"column:origin_user_id;not null;comment:源用户ID" json:"origin_user_id"`              // 源用户ID
	TargetUserID  int64  `gorm:"column:target_user_id;not null;comment:目标用户ID" json:"target_user_id"`             // 目标用户ID
	OriginAppID   int64  `gorm:"column:origin_app_id;not null;comment:源AppID" json:"origin_app_id"`               // 源AppID
	TargetAppID   int64  `gorm:"column:target_app_id;not null;comment:目标AppID" json:"target_app_id"`              // 目标AppID
	DataType      int32  `gorm:"column:data_type;not null;comment:数据类型 1:knowledge, 2:database" json:"data_type"` // 数据类型 1:knowledge, 2:database
	ExtInfo       string `gorm:"column:ext_info;not null;comment:存储额外信息" json:"ext_info"`                         // 存储额外信息
	StartTime     int64  `gorm:"column:start_time;not null;comment:任务开始时间" json:"start_time"`                     // 任务开始时间
	FinishTime    int64  `gorm:"column:finish_time;comment:任务结束时间" json:"finish_time"`                            // 任务结束时间
	Status        int32  `gorm:"column:status;not null;default:1;comment:1:创建 2:执行中 3:成功 4:失败" json:"status"`     // 1:创建 2:执行中 3:成功 4:失败
	ErrorMsg      string `gorm:"column:error_msg;comment:错误信息" json:"error_msg"`                                  // 错误信息
	ID            int64  `gorm:"column:id;primaryKey;autoIncrement:true;comment:ID" json:"id"`                    // ID
}

// TableName DataCopyTask's table name
func (*DataCopyTask) TableName() string {
	return TableNameDataCopyTask
}
