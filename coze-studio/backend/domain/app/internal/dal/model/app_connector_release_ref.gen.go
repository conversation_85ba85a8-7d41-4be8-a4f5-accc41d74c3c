// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "github.com/coze-dev/coze-studio/backend/domain/app/entity"

const TableNameAppConnectorReleaseRef = "app_connector_release_ref"

// AppConnectorReleaseRef Connector Release Record Reference
type AppConnectorReleaseRef struct {
	ID            int64                `gorm:"column:id;primaryKey;comment:Primary Key" json:"id"`                                                    // Primary Key
	RecordID      int64                `gorm:"column:record_id;not null;comment:Publish Record ID" json:"record_id"`                                  // Publish Record ID
	ConnectorID   int64                `gorm:"column:connector_id;comment:Publish Connector ID" json:"connector_id"`                                  // Publish Connector ID
	PublishConfig entity.PublishConfig `gorm:"column:publish_config;comment:Publish Configuration;serializer:json" json:"publish_config"`             // Publish Configuration
	PublishStatus int32                `gorm:"column:publish_status;not null;comment:Publish Status" json:"publish_status"`                           // Publish Status
	CreatedAt     int64                `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt     int64                `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
}

// TableName AppConnectorReleaseRef's table name
func (*AppConnectorReleaseRef) TableName() string {
	return TableNameAppConnectorReleaseRef
}
