// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/app/internal/dal/model"
)

func newAppDraft(db *gorm.DB, opts ...gen.DOOption) appDraft {
	_appDraft := appDraft{}

	_appDraft.appDraftDo.UseDB(db, opts...)
	_appDraft.appDraftDo.UseModel(&model.AppDraft{})

	tableName := _appDraft.appDraftDo.TableName()
	_appDraft.ALL = field.NewAsterisk(tableName)
	_appDraft.ID = field.NewInt64(tableName, "id")
	_appDraft.SpaceID = field.NewInt64(tableName, "space_id")
	_appDraft.OwnerID = field.NewInt64(tableName, "owner_id")
	_appDraft.IconURI = field.NewString(tableName, "icon_uri")
	_appDraft.Name = field.NewString(tableName, "name")
	_appDraft.Description = field.NewString(tableName, "description")
	_appDraft.CreatedAt = field.NewInt64(tableName, "created_at")
	_appDraft.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_appDraft.DeletedAt = field.NewField(tableName, "deleted_at")

	_appDraft.fillFieldMap()

	return _appDraft
}

// appDraft Draft Application
type appDraft struct {
	appDraftDo

	ALL         field.Asterisk
	ID          field.Int64  // APP ID
	SpaceID     field.Int64  // Space ID
	OwnerID     field.Int64  // Owner ID
	IconURI     field.String // Icon URI
	Name        field.String // Application Name
	Description field.String // Application Description
	CreatedAt   field.Int64  // Create Time in Milliseconds
	UpdatedAt   field.Int64  // Update Time in Milliseconds
	DeletedAt   field.Field  // Delete Time

	fieldMap map[string]field.Expr
}

func (a appDraft) Table(newTableName string) *appDraft {
	a.appDraftDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appDraft) As(alias string) *appDraft {
	a.appDraftDo.DO = *(a.appDraftDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appDraft) updateTableName(table string) *appDraft {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.SpaceID = field.NewInt64(table, "space_id")
	a.OwnerID = field.NewInt64(table, "owner_id")
	a.IconURI = field.NewString(table, "icon_uri")
	a.Name = field.NewString(table, "name")
	a.Description = field.NewString(table, "description")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.DeletedAt = field.NewField(table, "deleted_at")

	a.fillFieldMap()

	return a
}

func (a *appDraft) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appDraft) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 9)
	a.fieldMap["id"] = a.ID
	a.fieldMap["space_id"] = a.SpaceID
	a.fieldMap["owner_id"] = a.OwnerID
	a.fieldMap["icon_uri"] = a.IconURI
	a.fieldMap["name"] = a.Name
	a.fieldMap["description"] = a.Description
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
}

func (a appDraft) clone(db *gorm.DB) appDraft {
	a.appDraftDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appDraft) replaceDB(db *gorm.DB) appDraft {
	a.appDraftDo.ReplaceDB(db)
	return a
}

type appDraftDo struct{ gen.DO }

type IAppDraftDo interface {
	gen.SubQuery
	Debug() IAppDraftDo
	WithContext(ctx context.Context) IAppDraftDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppDraftDo
	WriteDB() IAppDraftDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppDraftDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppDraftDo
	Not(conds ...gen.Condition) IAppDraftDo
	Or(conds ...gen.Condition) IAppDraftDo
	Select(conds ...field.Expr) IAppDraftDo
	Where(conds ...gen.Condition) IAppDraftDo
	Order(conds ...field.Expr) IAppDraftDo
	Distinct(cols ...field.Expr) IAppDraftDo
	Omit(cols ...field.Expr) IAppDraftDo
	Join(table schema.Tabler, on ...field.Expr) IAppDraftDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppDraftDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppDraftDo
	Group(cols ...field.Expr) IAppDraftDo
	Having(conds ...gen.Condition) IAppDraftDo
	Limit(limit int) IAppDraftDo
	Offset(offset int) IAppDraftDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppDraftDo
	Unscoped() IAppDraftDo
	Create(values ...*model.AppDraft) error
	CreateInBatches(values []*model.AppDraft, batchSize int) error
	Save(values ...*model.AppDraft) error
	First() (*model.AppDraft, error)
	Take() (*model.AppDraft, error)
	Last() (*model.AppDraft, error)
	Find() ([]*model.AppDraft, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppDraft, err error)
	FindInBatches(result *[]*model.AppDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AppDraft) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppDraftDo
	Assign(attrs ...field.AssignExpr) IAppDraftDo
	Joins(fields ...field.RelationField) IAppDraftDo
	Preload(fields ...field.RelationField) IAppDraftDo
	FirstOrInit() (*model.AppDraft, error)
	FirstOrCreate() (*model.AppDraft, error)
	FindByPage(offset int, limit int) (result []*model.AppDraft, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppDraftDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appDraftDo) Debug() IAppDraftDo {
	return a.withDO(a.DO.Debug())
}

func (a appDraftDo) WithContext(ctx context.Context) IAppDraftDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appDraftDo) ReadDB() IAppDraftDo {
	return a.Clauses(dbresolver.Read)
}

func (a appDraftDo) WriteDB() IAppDraftDo {
	return a.Clauses(dbresolver.Write)
}

func (a appDraftDo) Session(config *gorm.Session) IAppDraftDo {
	return a.withDO(a.DO.Session(config))
}

func (a appDraftDo) Clauses(conds ...clause.Expression) IAppDraftDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appDraftDo) Returning(value interface{}, columns ...string) IAppDraftDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appDraftDo) Not(conds ...gen.Condition) IAppDraftDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appDraftDo) Or(conds ...gen.Condition) IAppDraftDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appDraftDo) Select(conds ...field.Expr) IAppDraftDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appDraftDo) Where(conds ...gen.Condition) IAppDraftDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appDraftDo) Order(conds ...field.Expr) IAppDraftDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appDraftDo) Distinct(cols ...field.Expr) IAppDraftDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appDraftDo) Omit(cols ...field.Expr) IAppDraftDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appDraftDo) Join(table schema.Tabler, on ...field.Expr) IAppDraftDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appDraftDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppDraftDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appDraftDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppDraftDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appDraftDo) Group(cols ...field.Expr) IAppDraftDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appDraftDo) Having(conds ...gen.Condition) IAppDraftDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appDraftDo) Limit(limit int) IAppDraftDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appDraftDo) Offset(offset int) IAppDraftDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appDraftDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppDraftDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appDraftDo) Unscoped() IAppDraftDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appDraftDo) Create(values ...*model.AppDraft) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appDraftDo) CreateInBatches(values []*model.AppDraft, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appDraftDo) Save(values ...*model.AppDraft) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appDraftDo) First() (*model.AppDraft, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDraft), nil
	}
}

func (a appDraftDo) Take() (*model.AppDraft, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDraft), nil
	}
}

func (a appDraftDo) Last() (*model.AppDraft, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDraft), nil
	}
}

func (a appDraftDo) Find() ([]*model.AppDraft, error) {
	result, err := a.DO.Find()
	return result.([]*model.AppDraft), err
}

func (a appDraftDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppDraft, err error) {
	buf := make([]*model.AppDraft, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appDraftDo) FindInBatches(result *[]*model.AppDraft, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appDraftDo) Attrs(attrs ...field.AssignExpr) IAppDraftDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appDraftDo) Assign(attrs ...field.AssignExpr) IAppDraftDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appDraftDo) Joins(fields ...field.RelationField) IAppDraftDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appDraftDo) Preload(fields ...field.RelationField) IAppDraftDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appDraftDo) FirstOrInit() (*model.AppDraft, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDraft), nil
	}
}

func (a appDraftDo) FirstOrCreate() (*model.AppDraft, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppDraft), nil
	}
}

func (a appDraftDo) FindByPage(offset int, limit int) (result []*model.AppDraft, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appDraftDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appDraftDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appDraftDo) Delete(models ...*model.AppDraft) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appDraftDo) withDO(do gen.Dao) *appDraftDo {
	a.DO = *do.(*gen.DO)
	return a
}
