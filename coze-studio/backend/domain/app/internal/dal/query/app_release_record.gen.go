// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/app/internal/dal/model"
)

func newAppReleaseRecord(db *gorm.DB, opts ...gen.DOOption) appReleaseRecord {
	_appReleaseRecord := appReleaseRecord{}

	_appReleaseRecord.appReleaseRecordDo.UseDB(db, opts...)
	_appReleaseRecord.appReleaseRecordDo.UseModel(&model.AppReleaseRecord{})

	tableName := _appReleaseRecord.appReleaseRecordDo.TableName()
	_appReleaseRecord.ALL = field.NewAsterisk(tableName)
	_appReleaseRecord.ID = field.NewInt64(tableName, "id")
	_appReleaseRecord.AppID = field.NewInt64(tableName, "app_id")
	_appReleaseRecord.SpaceID = field.NewInt64(tableName, "space_id")
	_appReleaseRecord.OwnerID = field.NewInt64(tableName, "owner_id")
	_appReleaseRecord.IconURI = field.NewString(tableName, "icon_uri")
	_appReleaseRecord.Name = field.NewString(tableName, "name")
	_appReleaseRecord.Description = field.NewString(tableName, "description")
	_appReleaseRecord.ConnectorIds = field.NewField(tableName, "connector_ids")
	_appReleaseRecord.ExtraInfo = field.NewField(tableName, "extra_info")
	_appReleaseRecord.Version = field.NewString(tableName, "version")
	_appReleaseRecord.VersionDesc = field.NewString(tableName, "version_desc")
	_appReleaseRecord.PublishStatus = field.NewInt32(tableName, "publish_status")
	_appReleaseRecord.PublishAt = field.NewInt64(tableName, "publish_at")
	_appReleaseRecord.CreatedAt = field.NewInt64(tableName, "created_at")
	_appReleaseRecord.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_appReleaseRecord.fillFieldMap()

	return _appReleaseRecord
}

// appReleaseRecord Application Release Record
type appReleaseRecord struct {
	appReleaseRecordDo

	ALL           field.Asterisk
	ID            field.Int64  // Publish Record ID
	AppID         field.Int64  // Application ID
	SpaceID       field.Int64  // Space ID
	OwnerID       field.Int64  // Owner ID
	IconURI       field.String // Icon URI
	Name          field.String // Application Name
	Description   field.String // Application Description
	ConnectorIds  field.Field  // Publish Connector IDs
	ExtraInfo     field.Field  // Publish Extra Info
	Version       field.String // Release Version
	VersionDesc   field.String // Version Description
	PublishStatus field.Int32  // Publish Status
	PublishAt     field.Int64  // Publish Time in Milliseconds
	CreatedAt     field.Int64  // Create Time in Milliseconds
	UpdatedAt     field.Int64  // Update Time in Milliseconds

	fieldMap map[string]field.Expr
}

func (a appReleaseRecord) Table(newTableName string) *appReleaseRecord {
	a.appReleaseRecordDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appReleaseRecord) As(alias string) *appReleaseRecord {
	a.appReleaseRecordDo.DO = *(a.appReleaseRecordDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appReleaseRecord) updateTableName(table string) *appReleaseRecord {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.AppID = field.NewInt64(table, "app_id")
	a.SpaceID = field.NewInt64(table, "space_id")
	a.OwnerID = field.NewInt64(table, "owner_id")
	a.IconURI = field.NewString(table, "icon_uri")
	a.Name = field.NewString(table, "name")
	a.Description = field.NewString(table, "description")
	a.ConnectorIds = field.NewField(table, "connector_ids")
	a.ExtraInfo = field.NewField(table, "extra_info")
	a.Version = field.NewString(table, "version")
	a.VersionDesc = field.NewString(table, "version_desc")
	a.PublishStatus = field.NewInt32(table, "publish_status")
	a.PublishAt = field.NewInt64(table, "publish_at")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")

	a.fillFieldMap()

	return a
}

func (a *appReleaseRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appReleaseRecord) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 15)
	a.fieldMap["id"] = a.ID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["space_id"] = a.SpaceID
	a.fieldMap["owner_id"] = a.OwnerID
	a.fieldMap["icon_uri"] = a.IconURI
	a.fieldMap["name"] = a.Name
	a.fieldMap["description"] = a.Description
	a.fieldMap["connector_ids"] = a.ConnectorIds
	a.fieldMap["extra_info"] = a.ExtraInfo
	a.fieldMap["version"] = a.Version
	a.fieldMap["version_desc"] = a.VersionDesc
	a.fieldMap["publish_status"] = a.PublishStatus
	a.fieldMap["publish_at"] = a.PublishAt
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
}

func (a appReleaseRecord) clone(db *gorm.DB) appReleaseRecord {
	a.appReleaseRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appReleaseRecord) replaceDB(db *gorm.DB) appReleaseRecord {
	a.appReleaseRecordDo.ReplaceDB(db)
	return a
}

type appReleaseRecordDo struct{ gen.DO }

type IAppReleaseRecordDo interface {
	gen.SubQuery
	Debug() IAppReleaseRecordDo
	WithContext(ctx context.Context) IAppReleaseRecordDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAppReleaseRecordDo
	WriteDB() IAppReleaseRecordDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAppReleaseRecordDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAppReleaseRecordDo
	Not(conds ...gen.Condition) IAppReleaseRecordDo
	Or(conds ...gen.Condition) IAppReleaseRecordDo
	Select(conds ...field.Expr) IAppReleaseRecordDo
	Where(conds ...gen.Condition) IAppReleaseRecordDo
	Order(conds ...field.Expr) IAppReleaseRecordDo
	Distinct(cols ...field.Expr) IAppReleaseRecordDo
	Omit(cols ...field.Expr) IAppReleaseRecordDo
	Join(table schema.Tabler, on ...field.Expr) IAppReleaseRecordDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAppReleaseRecordDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAppReleaseRecordDo
	Group(cols ...field.Expr) IAppReleaseRecordDo
	Having(conds ...gen.Condition) IAppReleaseRecordDo
	Limit(limit int) IAppReleaseRecordDo
	Offset(offset int) IAppReleaseRecordDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAppReleaseRecordDo
	Unscoped() IAppReleaseRecordDo
	Create(values ...*model.AppReleaseRecord) error
	CreateInBatches(values []*model.AppReleaseRecord, batchSize int) error
	Save(values ...*model.AppReleaseRecord) error
	First() (*model.AppReleaseRecord, error)
	Take() (*model.AppReleaseRecord, error)
	Last() (*model.AppReleaseRecord, error)
	Find() ([]*model.AppReleaseRecord, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppReleaseRecord, err error)
	FindInBatches(result *[]*model.AppReleaseRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AppReleaseRecord) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAppReleaseRecordDo
	Assign(attrs ...field.AssignExpr) IAppReleaseRecordDo
	Joins(fields ...field.RelationField) IAppReleaseRecordDo
	Preload(fields ...field.RelationField) IAppReleaseRecordDo
	FirstOrInit() (*model.AppReleaseRecord, error)
	FirstOrCreate() (*model.AppReleaseRecord, error)
	FindByPage(offset int, limit int) (result []*model.AppReleaseRecord, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAppReleaseRecordDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a appReleaseRecordDo) Debug() IAppReleaseRecordDo {
	return a.withDO(a.DO.Debug())
}

func (a appReleaseRecordDo) WithContext(ctx context.Context) IAppReleaseRecordDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appReleaseRecordDo) ReadDB() IAppReleaseRecordDo {
	return a.Clauses(dbresolver.Read)
}

func (a appReleaseRecordDo) WriteDB() IAppReleaseRecordDo {
	return a.Clauses(dbresolver.Write)
}

func (a appReleaseRecordDo) Session(config *gorm.Session) IAppReleaseRecordDo {
	return a.withDO(a.DO.Session(config))
}

func (a appReleaseRecordDo) Clauses(conds ...clause.Expression) IAppReleaseRecordDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appReleaseRecordDo) Returning(value interface{}, columns ...string) IAppReleaseRecordDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appReleaseRecordDo) Not(conds ...gen.Condition) IAppReleaseRecordDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appReleaseRecordDo) Or(conds ...gen.Condition) IAppReleaseRecordDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appReleaseRecordDo) Select(conds ...field.Expr) IAppReleaseRecordDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appReleaseRecordDo) Where(conds ...gen.Condition) IAppReleaseRecordDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appReleaseRecordDo) Order(conds ...field.Expr) IAppReleaseRecordDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appReleaseRecordDo) Distinct(cols ...field.Expr) IAppReleaseRecordDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appReleaseRecordDo) Omit(cols ...field.Expr) IAppReleaseRecordDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appReleaseRecordDo) Join(table schema.Tabler, on ...field.Expr) IAppReleaseRecordDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appReleaseRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAppReleaseRecordDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appReleaseRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) IAppReleaseRecordDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appReleaseRecordDo) Group(cols ...field.Expr) IAppReleaseRecordDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appReleaseRecordDo) Having(conds ...gen.Condition) IAppReleaseRecordDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appReleaseRecordDo) Limit(limit int) IAppReleaseRecordDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appReleaseRecordDo) Offset(offset int) IAppReleaseRecordDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appReleaseRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAppReleaseRecordDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appReleaseRecordDo) Unscoped() IAppReleaseRecordDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appReleaseRecordDo) Create(values ...*model.AppReleaseRecord) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appReleaseRecordDo) CreateInBatches(values []*model.AppReleaseRecord, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appReleaseRecordDo) Save(values ...*model.AppReleaseRecord) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appReleaseRecordDo) First() (*model.AppReleaseRecord, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppReleaseRecord), nil
	}
}

func (a appReleaseRecordDo) Take() (*model.AppReleaseRecord, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppReleaseRecord), nil
	}
}

func (a appReleaseRecordDo) Last() (*model.AppReleaseRecord, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppReleaseRecord), nil
	}
}

func (a appReleaseRecordDo) Find() ([]*model.AppReleaseRecord, error) {
	result, err := a.DO.Find()
	return result.([]*model.AppReleaseRecord), err
}

func (a appReleaseRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppReleaseRecord, err error) {
	buf := make([]*model.AppReleaseRecord, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appReleaseRecordDo) FindInBatches(result *[]*model.AppReleaseRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appReleaseRecordDo) Attrs(attrs ...field.AssignExpr) IAppReleaseRecordDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appReleaseRecordDo) Assign(attrs ...field.AssignExpr) IAppReleaseRecordDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appReleaseRecordDo) Joins(fields ...field.RelationField) IAppReleaseRecordDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appReleaseRecordDo) Preload(fields ...field.RelationField) IAppReleaseRecordDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appReleaseRecordDo) FirstOrInit() (*model.AppReleaseRecord, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppReleaseRecord), nil
	}
}

func (a appReleaseRecordDo) FirstOrCreate() (*model.AppReleaseRecord, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppReleaseRecord), nil
	}
}

func (a appReleaseRecordDo) FindByPage(offset int, limit int) (result []*model.AppReleaseRecord, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appReleaseRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appReleaseRecordDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appReleaseRecordDo) Delete(models ...*model.AppReleaseRecord) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appReleaseRecordDo) withDO(do gen.Dao) *appReleaseRecordDo {
	a.DO = *do.(*gen.DO)
	return a
}
