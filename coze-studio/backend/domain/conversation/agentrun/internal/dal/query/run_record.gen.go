// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/conversation/agentrun/internal/dal/model"
)

func newRunRecord(db *gorm.DB, opts ...gen.DOOption) runRecord {
	_runRecord := runRecord{}

	_runRecord.runRecordDo.UseDB(db, opts...)
	_runRecord.runRecordDo.UseModel(&model.RunRecord{})

	tableName := _runRecord.runRecordDo.TableName()
	_runRecord.ALL = field.NewAsterisk(tableName)
	_runRecord.ID = field.NewInt64(tableName, "id")
	_runRecord.ConversationID = field.NewInt64(tableName, "conversation_id")
	_runRecord.SectionID = field.NewInt64(tableName, "section_id")
	_runRecord.AgentID = field.NewInt64(tableName, "agent_id")
	_runRecord.UserID = field.NewString(tableName, "user_id")
	_runRecord.Source = field.NewInt32(tableName, "source")
	_runRecord.Status = field.NewString(tableName, "status")
	_runRecord.CreatorID = field.NewInt64(tableName, "creator_id")
	_runRecord.CreatedAt = field.NewInt64(tableName, "created_at")
	_runRecord.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_runRecord.FailedAt = field.NewInt64(tableName, "failed_at")
	_runRecord.LastError = field.NewString(tableName, "last_error")
	_runRecord.CompletedAt = field.NewInt64(tableName, "completed_at")
	_runRecord.ChatRequest = field.NewString(tableName, "chat_request")
	_runRecord.Ext = field.NewString(tableName, "ext")
	_runRecord.Usage = field.NewField(tableName, "usage")

	_runRecord.fillFieldMap()

	return _runRecord
}

// runRecord 执行记录表
type runRecord struct {
	runRecordDo

	ALL            field.Asterisk
	ID             field.Int64  // 主键ID
	ConversationID field.Int64  // 会话 ID
	SectionID      field.Int64  // section ID
	AgentID        field.Int64  // agent_id
	UserID         field.String // user id
	Source         field.Int32  // 执行来源 0 API,
	Status         field.String // 状态,0 Unknown, 1-Created,2-InProgress,3-Completed,4-Failed,5-Expired,6-Cancelled,7-RequiresAction
	CreatorID      field.Int64  // 创建者标识
	CreatedAt      field.Int64  // 创建时间
	UpdatedAt      field.Int64  // 更新时间
	FailedAt       field.Int64  // 失败时间
	LastError      field.String // error message
	CompletedAt    field.Int64  // 结束时间
	ChatRequest    field.String // 保存原始请求的部分字段
	Ext            field.String // 扩展字段
	Usage          field.Field  // usage

	fieldMap map[string]field.Expr
}

func (r runRecord) Table(newTableName string) *runRecord {
	r.runRecordDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r runRecord) As(alias string) *runRecord {
	r.runRecordDo.DO = *(r.runRecordDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *runRecord) updateTableName(table string) *runRecord {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewInt64(table, "id")
	r.ConversationID = field.NewInt64(table, "conversation_id")
	r.SectionID = field.NewInt64(table, "section_id")
	r.AgentID = field.NewInt64(table, "agent_id")
	r.UserID = field.NewString(table, "user_id")
	r.Source = field.NewInt32(table, "source")
	r.Status = field.NewString(table, "status")
	r.CreatorID = field.NewInt64(table, "creator_id")
	r.CreatedAt = field.NewInt64(table, "created_at")
	r.UpdatedAt = field.NewInt64(table, "updated_at")
	r.FailedAt = field.NewInt64(table, "failed_at")
	r.LastError = field.NewString(table, "last_error")
	r.CompletedAt = field.NewInt64(table, "completed_at")
	r.ChatRequest = field.NewString(table, "chat_request")
	r.Ext = field.NewString(table, "ext")
	r.Usage = field.NewField(table, "usage")

	r.fillFieldMap()

	return r
}

func (r *runRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *runRecord) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 16)
	r.fieldMap["id"] = r.ID
	r.fieldMap["conversation_id"] = r.ConversationID
	r.fieldMap["section_id"] = r.SectionID
	r.fieldMap["agent_id"] = r.AgentID
	r.fieldMap["user_id"] = r.UserID
	r.fieldMap["source"] = r.Source
	r.fieldMap["status"] = r.Status
	r.fieldMap["creator_id"] = r.CreatorID
	r.fieldMap["created_at"] = r.CreatedAt
	r.fieldMap["updated_at"] = r.UpdatedAt
	r.fieldMap["failed_at"] = r.FailedAt
	r.fieldMap["last_error"] = r.LastError
	r.fieldMap["completed_at"] = r.CompletedAt
	r.fieldMap["chat_request"] = r.ChatRequest
	r.fieldMap["ext"] = r.Ext
	r.fieldMap["usage"] = r.Usage
}

func (r runRecord) clone(db *gorm.DB) runRecord {
	r.runRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r runRecord) replaceDB(db *gorm.DB) runRecord {
	r.runRecordDo.ReplaceDB(db)
	return r
}

type runRecordDo struct{ gen.DO }

type IRunRecordDo interface {
	gen.SubQuery
	Debug() IRunRecordDo
	WithContext(ctx context.Context) IRunRecordDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IRunRecordDo
	WriteDB() IRunRecordDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IRunRecordDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IRunRecordDo
	Not(conds ...gen.Condition) IRunRecordDo
	Or(conds ...gen.Condition) IRunRecordDo
	Select(conds ...field.Expr) IRunRecordDo
	Where(conds ...gen.Condition) IRunRecordDo
	Order(conds ...field.Expr) IRunRecordDo
	Distinct(cols ...field.Expr) IRunRecordDo
	Omit(cols ...field.Expr) IRunRecordDo
	Join(table schema.Tabler, on ...field.Expr) IRunRecordDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IRunRecordDo
	RightJoin(table schema.Tabler, on ...field.Expr) IRunRecordDo
	Group(cols ...field.Expr) IRunRecordDo
	Having(conds ...gen.Condition) IRunRecordDo
	Limit(limit int) IRunRecordDo
	Offset(offset int) IRunRecordDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IRunRecordDo
	Unscoped() IRunRecordDo
	Create(values ...*model.RunRecord) error
	CreateInBatches(values []*model.RunRecord, batchSize int) error
	Save(values ...*model.RunRecord) error
	First() (*model.RunRecord, error)
	Take() (*model.RunRecord, error)
	Last() (*model.RunRecord, error)
	Find() ([]*model.RunRecord, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.RunRecord, err error)
	FindInBatches(result *[]*model.RunRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.RunRecord) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IRunRecordDo
	Assign(attrs ...field.AssignExpr) IRunRecordDo
	Joins(fields ...field.RelationField) IRunRecordDo
	Preload(fields ...field.RelationField) IRunRecordDo
	FirstOrInit() (*model.RunRecord, error)
	FirstOrCreate() (*model.RunRecord, error)
	FindByPage(offset int, limit int) (result []*model.RunRecord, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IRunRecordDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (r runRecordDo) Debug() IRunRecordDo {
	return r.withDO(r.DO.Debug())
}

func (r runRecordDo) WithContext(ctx context.Context) IRunRecordDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r runRecordDo) ReadDB() IRunRecordDo {
	return r.Clauses(dbresolver.Read)
}

func (r runRecordDo) WriteDB() IRunRecordDo {
	return r.Clauses(dbresolver.Write)
}

func (r runRecordDo) Session(config *gorm.Session) IRunRecordDo {
	return r.withDO(r.DO.Session(config))
}

func (r runRecordDo) Clauses(conds ...clause.Expression) IRunRecordDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r runRecordDo) Returning(value interface{}, columns ...string) IRunRecordDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r runRecordDo) Not(conds ...gen.Condition) IRunRecordDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r runRecordDo) Or(conds ...gen.Condition) IRunRecordDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r runRecordDo) Select(conds ...field.Expr) IRunRecordDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r runRecordDo) Where(conds ...gen.Condition) IRunRecordDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r runRecordDo) Order(conds ...field.Expr) IRunRecordDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r runRecordDo) Distinct(cols ...field.Expr) IRunRecordDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r runRecordDo) Omit(cols ...field.Expr) IRunRecordDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r runRecordDo) Join(table schema.Tabler, on ...field.Expr) IRunRecordDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r runRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) IRunRecordDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r runRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) IRunRecordDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r runRecordDo) Group(cols ...field.Expr) IRunRecordDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r runRecordDo) Having(conds ...gen.Condition) IRunRecordDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r runRecordDo) Limit(limit int) IRunRecordDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r runRecordDo) Offset(offset int) IRunRecordDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r runRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IRunRecordDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r runRecordDo) Unscoped() IRunRecordDo {
	return r.withDO(r.DO.Unscoped())
}

func (r runRecordDo) Create(values ...*model.RunRecord) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r runRecordDo) CreateInBatches(values []*model.RunRecord, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r runRecordDo) Save(values ...*model.RunRecord) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r runRecordDo) First() (*model.RunRecord, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.RunRecord), nil
	}
}

func (r runRecordDo) Take() (*model.RunRecord, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.RunRecord), nil
	}
}

func (r runRecordDo) Last() (*model.RunRecord, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.RunRecord), nil
	}
}

func (r runRecordDo) Find() ([]*model.RunRecord, error) {
	result, err := r.DO.Find()
	return result.([]*model.RunRecord), err
}

func (r runRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.RunRecord, err error) {
	buf := make([]*model.RunRecord, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r runRecordDo) FindInBatches(result *[]*model.RunRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r runRecordDo) Attrs(attrs ...field.AssignExpr) IRunRecordDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r runRecordDo) Assign(attrs ...field.AssignExpr) IRunRecordDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r runRecordDo) Joins(fields ...field.RelationField) IRunRecordDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r runRecordDo) Preload(fields ...field.RelationField) IRunRecordDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r runRecordDo) FirstOrInit() (*model.RunRecord, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.RunRecord), nil
	}
}

func (r runRecordDo) FirstOrCreate() (*model.RunRecord, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.RunRecord), nil
	}
}

func (r runRecordDo) FindByPage(offset int, limit int) (result []*model.RunRecord, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r runRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r runRecordDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r runRecordDo) Delete(models ...*model.RunRecord) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *runRecordDo) withDO(do gen.Dao) *runRecordDo {
	r.DO = *do.(*gen.DO)
	return r
}
