// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameConversation = "conversation"

// Conversation 会话信息表
type Conversation struct {
	ID          int64  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                    // 主键ID
	ConnectorID int64  `gorm:"column:connector_id;not null;comment:业务线 ID" json:"connector_id"`                   // 业务线 ID
	AgentID     int64  `gorm:"column:agent_id;not null;comment:agent_id" json:"agent_id"`                         // agent_id
	Scene       int32  `gorm:"column:scene;not null;comment:会话场景" json:"scene"`                                   // 会话场景
	SectionID   int64  `gorm:"column:section_id;not null;comment:最新section_id" json:"section_id"`                 // 最新section_id
	CreatorID   int64  `gorm:"column:creator_id;comment:创建者id" json:"creator_id"`                                 // 创建者id
	Ext         string `gorm:"column:ext;comment:扩展字段" json:"ext"`                                                // 扩展字段
	Status      int32  `gorm:"column:status;not null;default:1;comment:status: 1-normal 2-deleted" json:"status"` // status: 1-normal 2-deleted
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"`    // 创建时间
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"`    // 更新时间
}

// TableName Conversation's table name
func (*Conversation) TableName() string {
	return TableNameConversation
}
