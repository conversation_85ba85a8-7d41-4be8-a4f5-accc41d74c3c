#!/usr/bin/env python3
"""
HM-RAG vs 原始LightRAG 对比实验
"""

import os
import sys
import time
import json
import asyncio
import numpy as np
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# 设置路径
sys.path.insert(0, './original_lightrag')  # 原始LightRAG路径
sys.path.append('/data/HMRAG-main')        # HM-RAG路径

# 导入原始LightRAG
from lightrag import LightRAG as OriginalLightRAG, QueryParam
from lightrag.llm.ollama import ollama_model_complete, ollama_embed
from lightrag.utils import EmbeddingFunc

# 导入HM-RAG组件
from retrieval.graph_retrieval import GraphRetrieval
from retrieval.vector_retrieval import VectorRetrieval
from test_dataset import TestDatasetGenerator
from rag_evaluation_framework import RAGEvaluationFramework

@dataclass
class ComparisonResult:
    """对比结果数据类"""
    system_name: str
    query: str
    expected_answer: str
    retrieved_answer: str
    response_time: float
    bleu_score: float
    rouge_score: float
    semantic_similarity: float
    memory_usage: float
    cpu_usage: float

class ComparisonExperiment:
    """对比实验主类"""
    
    def __init__(self, experiment_name: str = "HM-RAG_vs_Original_LightRAG"):
        self.experiment_name = experiment_name
        self.results_dir = f"./comparison_results_{int(time.time())}"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 初始化组件
        self.evaluation_framework = RAGEvaluationFramework(self.results_dir)
        self.dataset_generator = TestDatasetGenerator()
        
        # 系统实例
        self.original_lightrag = None
        self.hmrag_graph = None
        self.hmrag_vector = None
        
        # 结果存储
        self.results = []
        
        print(f"🔬 对比实验初始化: {experiment_name}")
        print(f"📁 结果目录: {self.results_dir}")
    
    async def setup_original_lightrag(self):
        """设置原始LightRAG系统"""
        print("\n🔧 设置原始LightRAG系统...")

        working_dir = "./original_lightrag_test"
        os.makedirs(working_dir, exist_ok=True)

        try:
            # 创建简化的embedding函数，避免网络问题
            def simple_embed(texts):
                # 使用本地BGE模型进行embedding
                from sentence_transformers import SentenceTransformer
                model = SentenceTransformer('./bge-large-zh-v1.5', trust_remote_code=True)
                embeddings = model.encode(texts, convert_to_tensor=True)
                return embeddings.cpu().numpy()

            # 创建简化的LLM函数
            def simple_llm_complete(prompt, model_name, **kwargs):
                import requests
                url = "http://localhost:11434/api/generate"
                data = {
                    "model": "qwen2.5vl:32b",
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": kwargs.get("temperature", 0.7),
                        "num_predict": kwargs.get("max_tokens", 4000)
                    }
                }
                try:
                    response = requests.post(url, json=data, timeout=120)
                    response.raise_for_status()
                    result = response.json()
                    return result["response"]
                except Exception as e:
                    return f"LLM调用失败: {e}"

            self.original_lightrag = OriginalLightRAG(
                working_dir=working_dir,
                llm_model_func=simple_llm_complete,
                llm_model_name="qwen2.5vl:32b",
                llm_model_max_token_size=8192,
                llm_model_kwargs={
                    "temperature": 0.7,
                    "max_tokens": 4000
                },
                embedding_func=EmbeddingFunc(
                    embedding_dim=1024,
                    max_token_size=8192,
                    func=simple_embed,
                ),
            )

            await self.original_lightrag.initialize_storages()
            print("✓ 原始LightRAG系统初始化成功")
            return True

        except Exception as e:
            print(f"❌ 原始LightRAG系统初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def setup_hmrag_systems(self):
        """设置HM-RAG系统"""
        print("\n🔧 设置HM-RAG系统...")
        
        try:
            # 创建配置
            class ComparisonConfig:
                def __init__(self, suffix):
                    self.working_dir = f"./hmrag_test_{suffix}"
                    self.mode = "mix"
                    self.top_k = 3
            
            # Graph检索系统
            graph_config = ComparisonConfig("graph")
            os.makedirs(graph_config.working_dir, exist_ok=True)
            self.hmrag_graph = GraphRetrieval(graph_config)
            print("✓ HM-RAG Graph检索系统初始化成功")
            
            # Vector检索系统
            vector_config = ComparisonConfig("vector")
            os.makedirs(vector_config.working_dir, exist_ok=True)
            self.hmrag_vector = VectorRetrieval(vector_config)
            print("✓ HM-RAG Vector检索系统初始化成功")
            
            return True
            
        except Exception as e:
            print(f"❌ HM-RAG系统初始化失败: {e}")
            return False
    
    async def build_knowledge_bases(self):
        """构建知识库"""
        print("\n📚 构建知识库...")
        
        knowledge_base = self.dataset_generator.get_knowledge_base()
        
        # 构建原始LightRAG知识库
        print("  构建原始LightRAG知识库...")
        for i, doc in enumerate(knowledge_base):
            try:
                await self.original_lightrag.ainsert(doc)
                if i % 5 == 0:
                    print(f"    已插入 {i+1}/{len(knowledge_base)} 个文档")
            except Exception as e:
                print(f"    文档 {i+1} 插入失败: {e}")
        
        # 构建HM-RAG知识库
        print("  构建HM-RAG知识库...")
        for system_name, system in [("Graph", self.hmrag_graph), ("Vector", self.hmrag_vector)]:
            print(f"    构建 {system_name} 知识库...")
            for i, doc in enumerate(knowledge_base):
                try:
                    system.client.insert(doc)
                    if i % 5 == 0:
                        print(f"      已插入 {i+1}/{len(knowledge_base)} 个文档")
                except Exception as e:
                    print(f"      文档 {i+1} 插入失败: {e}")
        
        print("✓ 所有知识库构建完成")
    
    async def evaluate_single_query(self, query: str, expected_answer: str) -> List[ComparisonResult]:
        """评估单个查询"""
        results = []
        
        # 测试原始LightRAG
        print(f"  测试原始LightRAG...")
        start_time = time.time()
        try:
            response = await self.original_lightrag.aquery(
                query, 
                param=QueryParam(mode="hybrid")
            )
            end_time = time.time()
            response_time = end_time - start_time
            
            # 计算评估指标
            bleu = self.evaluation_framework.calculate_bleu_score(expected_answer, response)
            rouge = self.evaluation_framework.calculate_rouge_score(expected_answer, response)
            semantic = self.evaluation_framework.calculate_semantic_similarity(expected_answer, response)
            
            result = ComparisonResult(
                system_name="原始LightRAG",
                query=query,
                expected_answer=expected_answer,
                retrieved_answer=response,
                response_time=response_time,
                bleu_score=bleu,
                rouge_score=rouge,
                semantic_similarity=semantic,
                memory_usage=0.0,  # 简化版本，不测量内存
                cpu_usage=0.0      # 简化版本，不测量CPU
            )
            results.append(result)
            print(f"    响应时间: {response_time:.2f}s, 语义相似度: {semantic:.3f}")
            
        except Exception as e:
            print(f"    原始LightRAG查询失败: {e}")
        
        # 测试HM-RAG系统
        for system_name, system in [("HM-RAG-Graph", self.hmrag_graph), ("HM-RAG-Vector", self.hmrag_vector)]:
            print(f"  测试{system_name}...")
            start_time = time.time()
            try:
                response = system.find_top_k(query)
                end_time = time.time()
                response_time = end_time - start_time
                
                # 计算评估指标
                bleu = self.evaluation_framework.calculate_bleu_score(expected_answer, response)
                rouge = self.evaluation_framework.calculate_rouge_score(expected_answer, response)
                semantic = self.evaluation_framework.calculate_semantic_similarity(expected_answer, response)
                
                result = ComparisonResult(
                    system_name=system_name,
                    query=query,
                    expected_answer=expected_answer,
                    retrieved_answer=response,
                    response_time=response_time,
                    bleu_score=bleu,
                    rouge_score=rouge,
                    semantic_similarity=semantic,
                    memory_usage=0.0,
                    cpu_usage=0.0
                )
                results.append(result)
                print(f"    响应时间: {response_time:.2f}s, 语义相似度: {semantic:.3f}")
                
            except Exception as e:
                print(f"    {system_name}查询失败: {e}")
        
        return results
    
    async def run_comparison_test(self):
        """运行对比测试"""
        print("\n🚀 开始对比测试...")
        
        test_cases = self.dataset_generator.get_test_cases()
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n进度: {i}/{len(test_cases)}")
            query = test_case['query']
            expected_answer = test_case['expected_answer']
            
            print(f"查询: {query[:50]}...")
            
            query_results = await self.evaluate_single_query(query, expected_answer)
            self.results.extend(query_results)
        
        print(f"\n✓ 对比测试完成，共收集 {len(self.results)} 个结果")
    
    def analyze_results(self):
        """分析对比结果"""
        print("\n📊 分析对比结果...")
        
        # 按系统分组
        systems = {}
        for result in self.results:
            system = result.system_name
            if system not in systems:
                systems[system] = []
            systems[system].append(result)
        
        # 生成统计报告
        comparison_report = {}
        
        for system_name, results in systems.items():
            response_times = [r.response_time for r in results]
            semantic_similarities = [r.semantic_similarity for r in results]
            bleu_scores = [r.bleu_score for r in results]
            rouge_scores = [r.rouge_score for r in results]
            
            comparison_report[system_name] = {
                "样本数量": len(results),
                "响应时间": {
                    "平均值": np.mean(response_times),
                    "中位数": np.median(response_times),
                    "标准差": np.std(response_times),
                    "最小值": np.min(response_times),
                    "最大值": np.max(response_times)
                },
                "语义相似度": {
                    "平均值": np.mean(semantic_similarities),
                    "中位数": np.median(semantic_similarities),
                    "标准差": np.std(semantic_similarities)
                },
                "BLEU分数": {
                    "平均值": np.mean(bleu_scores),
                    "标准差": np.std(bleu_scores)
                },
                "ROUGE分数": {
                    "平均值": np.mean(rouge_scores),
                    "标准差": np.std(rouge_scores)
                }
            }
        
        return comparison_report
    
    def create_comparison_visualizations(self):
        """创建对比可视化图表"""
        print("\n📈 创建对比图表...")
        
        # 准备数据
        df_data = []
        for result in self.results:
            df_data.append({
                'system': result.system_name,
                'response_time': result.response_time,
                'semantic_similarity': result.semantic_similarity,
                'bleu_score': result.bleu_score,
                'rouge_score': result.rouge_score
            })
        
        df = pd.DataFrame(df_data)
        
        # 创建对比图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('HM-RAG vs 原始LightRAG 性能对比', fontsize=16)
        
        # 响应时间对比
        sns.boxplot(data=df, x='system', y='response_time', ax=axes[0,0])
        axes[0,0].set_title('响应时间对比')
        axes[0,0].set_ylabel('响应时间 (秒)')
        axes[0,0].tick_params(axis='x', rotation=45)
        
        # 语义相似度对比
        sns.boxplot(data=df, x='system', y='semantic_similarity', ax=axes[0,1])
        axes[0,1].set_title('语义相似度对比')
        axes[0,1].set_ylabel('语义相似度')
        axes[0,1].tick_params(axis='x', rotation=45)
        
        # BLEU分数对比
        sns.boxplot(data=df, x='system', y='bleu_score', ax=axes[1,0])
        axes[1,0].set_title('BLEU分数对比')
        axes[1,0].set_ylabel('BLEU分数')
        axes[1,0].tick_params(axis='x', rotation=45)
        
        # ROUGE分数对比
        sns.boxplot(data=df, x='system', y='rouge_score', ax=axes[1,1])
        axes[1,1].set_title('ROUGE分数对比')
        axes[1,1].set_ylabel('ROUGE分数')
        axes[1,1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(self.results_dir, 'comparison_charts.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"✓ 对比图表已保存到: {chart_path}")
        
        plt.show()
    
    def save_results(self):
        """保存对比结果"""
        # 保存详细结果
        detailed_results = []
        for result in self.results:
            detailed_results.append({
                "system_name": result.system_name,
                "query": result.query,
                "expected_answer": result.expected_answer,
                "retrieved_answer": result.retrieved_answer,
                "response_time": result.response_time,
                "bleu_score": result.bleu_score,
                "rouge_score": result.rouge_score,
                "semantic_similarity": result.semantic_similarity
            })
        
        results_path = os.path.join(self.results_dir, "detailed_comparison_results.json")
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 详细结果已保存到: {results_path}")
    
    async def run_full_comparison(self):
        """运行完整对比实验"""
        print(f"🎬 开始完整对比实验: {self.experiment_name}")
        
        try:
            # 1. 设置系统
            if not await self.setup_original_lightrag():
                return False
            
            if not self.setup_hmrag_systems():
                return False
            
            # 2. 构建知识库
            await self.build_knowledge_bases()
            
            # 3. 运行对比测试
            await self.run_comparison_test()
            
            # 4. 分析结果
            report = self.analyze_results()
            
            # 5. 保存结果
            self.save_results()
            
            # 6. 创建可视化
            self.create_comparison_visualizations()
            
            # 7. 打印总结
            self.print_comparison_summary(report)
            
            print(f"\n🎉 对比实验完成！")
            print(f"📁 结果目录: {self.results_dir}")
            
            return True
            
        except Exception as e:
            print(f"❌ 对比实验失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # 清理资源
            if self.original_lightrag:
                try:
                    await self.original_lightrag.finalize_storages()
                except:
                    pass
    
    def print_comparison_summary(self, report: Dict):
        """打印对比总结"""
        print("\n" + "="*80)
        print("🏆 HM-RAG vs 原始LightRAG 对比结果总结")
        print("="*80)
        
        for system_name, data in report.items():
            print(f"\n📊 {system_name}:")
            print(f"  样本数量: {data['样本数量']}")
            print(f"  平均响应时间: {data['响应时间']['平均值']:.2f}秒")
            print(f"  平均语义相似度: {data['语义相似度']['平均值']:.3f}")
            print(f"  响应时间稳定性: ±{data['响应时间']['标准差']:.2f}秒")

async def main():
    """主函数"""
    experiment = ComparisonExperiment()
    success = await experiment.run_full_comparison()
    
    if success:
        print("\n✅ 对比实验成功完成！")
    else:
        print("\n❌ 对比实验失败！")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
