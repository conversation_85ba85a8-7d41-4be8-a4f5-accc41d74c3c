<?xml version="1.0"?>
<net name="Model36" version="11">
	<layers>
		<layer id="1" name="input_ids" type="Parameter" version="opset1">
			<data shape="?,?" element_type="i64" />
			<output>
				<port id="0" precision="I64" names="input_ids">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="0" name="attention_mask" type="Parameter" version="opset1">
			<data shape="?,?" element_type="i64" />
			<output>
				<port id="0" precision="I64" names="attention_mask">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="2" name="Constant_99370" type="Const" version="opset1">
			<data element_type="i64" shape="1, 1" offset="0" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="3" name="__module.transformer.layer.0.attention/aten::eq/Equal" type="Equal" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="BOOL" names="165,235,305,375,445,95">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="__module.embeddings.word_embeddings/aten::embedding/Convert" type="Convert" version="opset1">
			<data destination_type="i32" />
			<input>
				<port id="0" precision="I64">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I32">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="5" name="ShapeOf_99545" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="I32">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="Constant_99551" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="0" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="Constant_99552" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="0" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="8" name="Gather_99553" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="142,212,282,352,422,72">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="9" name="__module.transformer.layer.0.attention/prim::ListConstruct/Reshape_0" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="8" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="Constant_12464946" type="Const" version="opset1">
			<data element_type="i8" shape="119547, 768" offset="16" size="91812096" />
			<output>
				<port id="0" precision="I8">
					<dim>119547</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="Convert_12464947" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>119547</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>119547</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="Constant_12464948" type="Const" version="opset1">
			<data element_type="f32" shape="119547, 1" offset="91812112" size="478188" />
			<output>
				<port id="0" precision="FP32">
					<dim>119547</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="__module.embeddings.word_embeddings/aten::embedding/Gather/fq_weights_0" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>119547</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>119547</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>119547</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="__module.embeddings.word_embeddings/aten::embedding/Constant" type="Const" version="opset1">
			<data element_type="i32" shape="" offset="92290300" size="4" />
			<output>
				<port id="0" precision="I32" />
			</output>
		</layer>
		<layer id="15" name="__module.embeddings.word_embeddings/aten::embedding/Gather" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>119547</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="2" precision="I32" />
			</input>
			<output>
				<port id="3" precision="FP32" names="22,input_embeds">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="16" name="Constant_12464950" type="Const" version="opset1">
			<data element_type="i8" shape="512, 768" offset="92290304" size="393216" />
			<output>
				<port id="0" precision="I8">
					<dim>512</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="Convert_12464951" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>512</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>512</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="Constant_12464952" type="Const" version="opset1">
			<data element_type="f32" shape="512, 1" offset="92683520" size="2048" />
			<output>
				<port id="0" precision="FP32">
					<dim>512</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="__module.embeddings.position_embeddings/aten::embedding/Gather/fq_weights_0" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>512</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>512</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>512</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="__module.embeddings/aten::slice/Slice" type="Const" version="opset1">
			<data element_type="i64" shape="1, 512" offset="92685568" size="4096" />
			<output>
				<port id="0" precision="I64" names="24">
					<dim>1</dim>
					<dim>512</dim>
				</port>
			</output>
		</layer>
		<layer id="21" name="__module.embeddings/aten::slice/Reshape" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="0" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="Constant_99546" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="8" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="23" name="Constant_99547" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="0" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="24" name="Gather_99548" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="23">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="25" name="__module.embeddings/aten::slice/Reshape_2" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="8" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="__module.embeddings/aten::slice/Reshape_3" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="8" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="__module.embeddings/aten::slice/Slice_1" type="Slice" version="opset8">
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
					<dim>512</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I64">
					<dim>1</dim>
				</port>
				<port id="4" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="I64" names="25">
					<dim>1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="__module.embeddings.position_embeddings/aten::embedding/Convert" type="Convert" version="opset1">
			<data destination_type="i32" />
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I32">
					<dim>1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="__module.embeddings.position_embeddings/aten::embedding/Constant" type="Const" version="opset1">
			<data element_type="i32" shape="" offset="92290300" size="4" />
			<output>
				<port id="0" precision="I32" />
			</output>
		</layer>
		<layer id="30" name="__module.embeddings.position_embeddings/aten::embedding/Gather" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>512</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
					<dim>-1</dim>
				</port>
				<port id="2" precision="I32" />
			</input>
			<output>
				<port id="3" precision="FP32" names="27,position_embeddings.1">
					<dim>1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="__module.embeddings/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="28">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="32" name="__module.embeddings.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="__module.embeddings.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="Constant_99371" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="92689668" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="__module.embeddings.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="Constant_99372" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="92692740" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="37" name="__module.embeddings.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="32,input.1">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="38" name="ShapeOf_99555" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="39" name="Constant_99556" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="8" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="40" name="Constant_99557" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="0" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="41" name="Gather_99558" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="143,213,283,353,423,73">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="42" name="__module.transformer.layer.0.attention/prim::ListConstruct/Concat" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64" names="166,236,306,376,446,96">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="43" name="__module.transformer.layer.0.attention/aten::view/Reshape" type="Reshape" version="opset1">
			<data special_zero="false" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="BOOL" names="167,237,307,377,447,97">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="44" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="92695812" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="45" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="46" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="92698884" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="47" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="92698888" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="48" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="92698884" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="49" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="92698888" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="50" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="51" name="Constant_12464954" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="92698892" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="52" name="Convert_12464955" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="53" name="Constant_12464956" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="93288716" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="54" name="__module.transformer.layer.0.attention.q_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="55" name="__module.transformer.layer.0.attention.q_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="56" name="Constant_99373" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="93291788" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="57" name="__module.transformer.layer.0.attention.q_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="76,x.1">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="58" name="Constant_99560" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="59" name="__module.transformer.layer.0.attention/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="78">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="60" name="__module.transformer.layer.0.attention/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="61" name="__module.transformer.layer.0.attention/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="79,q.1">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="62" name="Constant_99374" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1" offset="93294908" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="63" name="__module.transformer.layer.0.attention/aten::div/Divide" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="92">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="64" name="__module.transformer.layer.0.attention/aten::div/Divide/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93294912" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="65" name="__module.transformer.layer.0.attention/aten::div/Divide/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93294916" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="66" name="__module.transformer.layer.0.attention/aten::div/Divide/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93294912" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="67" name="__module.transformer.layer.0.attention/aten::div/Divide/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93294916" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="68" name="__module.transformer.layer.0.attention/aten::div/Divide/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="69" name="Constant_12464958" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="93294920" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="70" name="Convert_12464959" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="71" name="Constant_12464960" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="93884744" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="72" name="__module.transformer.layer.0.attention.k_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="73" name="__module.transformer.layer.0.attention.k_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="74" name="Constant_99375" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="93887816" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="75" name="__module.transformer.layer.0.attention.k_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="82,x.3">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="76" name="__module.transformer.layer.0.attention.k_lin/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93890888" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="77" name="__module.transformer.layer.0.attention.k_lin/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93890892" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="78" name="__module.transformer.layer.0.attention.k_lin/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93890888" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="79" name="__module.transformer.layer.0.attention.k_lin/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93890892" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="80" name="__module.transformer.layer.0.attention.k_lin/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="81" name="Constant_99561" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="82" name="__module.transformer.layer.0.attention/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="84">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="83" name="Constant_99084" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93890896" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="84" name="__module.transformer.layer.0.attention/aten::transpose/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="93">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="85" name="__module.transformer.layer.0.attention/aten::matmul/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="94,scores.1">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="86" name="__module.transformer.layer.0.attention/aten::expand_as/ShapeOf" type="ShapeOf" version="opset3">
			<data output_type="i32" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="87" name="__module.transformer.layer.0.attention/aten::expand_as/Broadcast" type="Broadcast" version="opset3">
			<data mode="bidirectional" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="BOOL" names="98,mask.1">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="88" name="__module.transformer.layer.0.attention/aten::masked_fill/ConvertLike" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93890912" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="89" name="__module.transformer.layer.0.attention/aten::masked_fill/Select" type="Select" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="101,input.3">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="90" name="__module.transformer.layer.0.attention/aten::softmax/Softmax" type="SoftMax" version="opset8">
			<data axis="-1" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="102,input.5">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="91" name="Constant_12464962" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="93890916" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="92" name="Convert_12464963" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="93" name="Constant_12464964" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="94480740" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="94" name="__module.transformer.layer.0.attention.v_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="95" name="__module.transformer.layer.0.attention.v_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="96" name="Constant_99376" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="94483812" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="97" name="__module.transformer.layer.0.attention.v_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="88,x.5">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="98" name="Constant_99562" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="99" name="__module.transformer.layer.0.attention/aten::view/Reshape_3" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="90">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="100" name="__module.transformer.layer.0.attention/aten::transpose/ScatterElementsUpdate_3" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="101" name="__module.transformer.layer.0.attention/aten::transpose/Transpose_3" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="91">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="102" name="__module.transformer.layer.0.attention/aten::matmul/MatMul_1" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="104,x.7">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="103" name="__module.transformer.layer.0.attention/aten::transpose/ScatterElementsUpdate_4" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="104" name="__module.transformer.layer.0.attention/aten::transpose/Transpose_4" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="105">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="105" name="Constant_99563" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="94486884" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="106" name="__module.transformer.layer.0.attention/aten::view/Reshape_4" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="108">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="107" name="__module.transformer.layer.0.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="94486908" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="108" name="__module.transformer.layer.0.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="109" name="__module.transformer.layer.0.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="94489980" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="110" name="__module.transformer.layer.0.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="94489984" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="111" name="__module.transformer.layer.0.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="94489980" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="112" name="__module.transformer.layer.0.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="94489984" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="113" name="__module.transformer.layer.0.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="114" name="Constant_12464966" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="94489988" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="115" name="Convert_12464967" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="116" name="Constant_12464968" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="95079812" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="117" name="__module.transformer.layer.0.attention.out_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="118" name="__module.transformer.layer.0.attention.out_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="119" name="Constant_99377" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="95082884" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="120" name="__module.transformer.layer.0.attention.out_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="111,sa_output.1">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="121" name="__module.transformer.layer.0/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="112">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="122" name="__module.transformer.layer.0.sa_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="123" name="__module.transformer.layer.0.sa_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="124" name="Constant_99378" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="95085956" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="125" name="__module.transformer.layer.0.sa_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="126" name="Constant_99379" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="95089028" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="127" name="__module.transformer.layer.0.sa_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="116,sa_output.3">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="128" name="__module.transformer.layer.0.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="95092100" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="129" name="__module.transformer.layer.0.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="130" name="__module.transformer.layer.0.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="95095172" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="131" name="__module.transformer.layer.0.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="95095176" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="132" name="__module.transformer.layer.0.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="95095172" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="133" name="__module.transformer.layer.0.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="95095176" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="134" name="__module.transformer.layer.0.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="135" name="Constant_12464970" type="Const" version="opset1">
			<data element_type="i8" shape="3072, 768" offset="95095180" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="136" name="Convert_12464971" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="137" name="Constant_12464972" type="Const" version="opset1">
			<data element_type="f32" shape="3072, 1" offset="97454476" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="138" name="__module.transformer.layer.0.ffn.lin1/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="139" name="__module.transformer.layer.0.ffn.lin1/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="140" name="Constant_99380" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="97466764" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="141" name="__module.transformer.layer.0.ffn.lin1/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="123">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="142" name="__module.transformer.layer.0.ffn.activation/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="124">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="143" name="__module.transformer.layer.0.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="97479052" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="144" name="__module.transformer.layer.0.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="145" name="__module.transformer.layer.0.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="97491340" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="146" name="__module.transformer.layer.0.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="97491344" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="147" name="__module.transformer.layer.0.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="97491340" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="148" name="__module.transformer.layer.0.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="97491344" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="149" name="__module.transformer.layer.0.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="150" name="Constant_12464974" type="Const" version="opset1">
			<data element_type="i8" shape="768, 3072" offset="97491348" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="151" name="Convert_12464975" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="152" name="Constant_12464976" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="99850644" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="153" name="__module.transformer.layer.0.ffn.lin2/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="154" name="__module.transformer.layer.0.ffn.lin2/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="155" name="Constant_99381" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="99853716" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="156" name="__module.transformer.layer.0.ffn.lin2/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="127,input.7">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="157" name="__module.transformer.layer.0/aten::add/Add_1" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="129">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="158" name="__module.transformer.layer.0.output_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="159" name="__module.transformer.layer.0.output_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="160" name="Constant_99382" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="99856788" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="161" name="__module.transformer.layer.0.output_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="162" name="Constant_99383" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="99859860" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="163" name="__module.transformer.layer.0.output_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="133,query.3">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="164" name="__module.transformer.layer.0.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="99862932" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="165" name="__module.transformer.layer.0.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="166" name="__module.transformer.layer.0.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="99866004" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="167" name="__module.transformer.layer.0.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="99866008" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="168" name="__module.transformer.layer.0.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="99866004" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="169" name="__module.transformer.layer.0.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="99866008" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="170" name="__module.transformer.layer.0.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="171" name="Constant_12464978" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="99866012" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="172" name="Convert_12464979" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="173" name="Constant_12464980" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="100455836" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="174" name="__module.transformer.layer.1.attention.q_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="175" name="__module.transformer.layer.1.attention.q_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="176" name="Constant_99384" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="100458908" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="177" name="__module.transformer.layer.1.attention.q_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="146,x.9">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="178" name="Constant_99564" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="179" name="__module.transformer.layer.1.attention/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="148">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="180" name="__module.transformer.layer.1.attention/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="181" name="__module.transformer.layer.1.attention/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="149,q.3">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="182" name="Constant_99385" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1" offset="93294908" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="183" name="__module.transformer.layer.1.attention/aten::div/Divide" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="162">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="184" name="__module.transformer.layer.1.attention/aten::div/Divide/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="100461980" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="185" name="__module.transformer.layer.1.attention/aten::div/Divide/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="100461984" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="186" name="__module.transformer.layer.1.attention/aten::div/Divide/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="100461980" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="187" name="__module.transformer.layer.1.attention/aten::div/Divide/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="100461984" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="188" name="__module.transformer.layer.1.attention/aten::div/Divide/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="189" name="Constant_12464982" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="100461988" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="190" name="Convert_12464983" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="191" name="Constant_12464984" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="101051812" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="192" name="__module.transformer.layer.1.attention.k_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="193" name="__module.transformer.layer.1.attention.k_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="194" name="Constant_99386" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="101054884" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="195" name="__module.transformer.layer.1.attention.k_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="152,x.11">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="196" name="__module.transformer.layer.1.attention.k_lin/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="101057956" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="197" name="__module.transformer.layer.1.attention.k_lin/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="101057960" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="198" name="__module.transformer.layer.1.attention.k_lin/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="101057956" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="199" name="__module.transformer.layer.1.attention.k_lin/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="101057960" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="200" name="__module.transformer.layer.1.attention.k_lin/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="201" name="Constant_99565" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="202" name="__module.transformer.layer.1.attention/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="154">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="203" name="Constant_99086" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93890896" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="204" name="__module.transformer.layer.1.attention/aten::transpose/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="163">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="205" name="__module.transformer.layer.1.attention/aten::matmul/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="164,scores.3">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="206" name="__module.transformer.layer.1.attention/aten::expand_as/ShapeOf" type="ShapeOf" version="opset3">
			<data output_type="i32" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="207" name="__module.transformer.layer.1.attention/aten::expand_as/Broadcast" type="Broadcast" version="opset3">
			<data mode="bidirectional" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="BOOL" names="168,mask.3">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="208" name="__module.transformer.layer.1.attention/aten::masked_fill/ConvertLike" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93890912" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="209" name="__module.transformer.layer.1.attention/aten::masked_fill/Select" type="Select" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="171,input.9">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="210" name="__module.transformer.layer.1.attention/aten::softmax/Softmax" type="SoftMax" version="opset8">
			<data axis="-1" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="172,input.11">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="211" name="Constant_12464986" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="101057964" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="212" name="Convert_12464987" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="213" name="Constant_12464988" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="101647788" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="214" name="__module.transformer.layer.1.attention.v_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="215" name="__module.transformer.layer.1.attention.v_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="216" name="Constant_99387" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="101650860" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="217" name="__module.transformer.layer.1.attention.v_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="158,x.13">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="218" name="Constant_99566" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="219" name="__module.transformer.layer.1.attention/aten::view/Reshape_3" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="160">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="220" name="__module.transformer.layer.1.attention/aten::transpose/ScatterElementsUpdate_3" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="221" name="__module.transformer.layer.1.attention/aten::transpose/Transpose_3" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="161">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="222" name="__module.transformer.layer.1.attention/aten::matmul/MatMul_1" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="174,x.15">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="223" name="__module.transformer.layer.1.attention/aten::transpose/ScatterElementsUpdate_4" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="224" name="__module.transformer.layer.1.attention/aten::transpose/Transpose_4" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="175">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="225" name="Constant_99567" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="94486884" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="226" name="__module.transformer.layer.1.attention/aten::view/Reshape_4" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="178">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="227" name="__module.transformer.layer.1.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="101653932" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="228" name="__module.transformer.layer.1.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="229" name="__module.transformer.layer.1.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="101657004" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="230" name="__module.transformer.layer.1.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="101657008" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="231" name="__module.transformer.layer.1.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="101657004" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="232" name="__module.transformer.layer.1.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="101657008" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="233" name="__module.transformer.layer.1.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="234" name="Constant_12464990" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="101657012" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="235" name="Convert_12464991" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="236" name="Constant_12464992" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="102246836" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="237" name="__module.transformer.layer.1.attention.out_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="238" name="__module.transformer.layer.1.attention.out_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="239" name="Constant_99388" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="102249908" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="240" name="__module.transformer.layer.1.attention.out_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="181,sa_output.5">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="241" name="__module.transformer.layer.1/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="182">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="242" name="__module.transformer.layer.1.sa_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="243" name="__module.transformer.layer.1.sa_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="244" name="Constant_99389" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="102252980" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="245" name="__module.transformer.layer.1.sa_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="246" name="Constant_99390" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="102256052" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="247" name="__module.transformer.layer.1.sa_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="186,sa_output.7">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="248" name="__module.transformer.layer.1.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="102259124" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="249" name="__module.transformer.layer.1.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="250" name="__module.transformer.layer.1.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="102262196" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="251" name="__module.transformer.layer.1.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="102262200" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="252" name="__module.transformer.layer.1.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="102262196" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="253" name="__module.transformer.layer.1.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="102262200" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="254" name="__module.transformer.layer.1.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="255" name="Constant_12464994" type="Const" version="opset1">
			<data element_type="i8" shape="3072, 768" offset="102262204" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="256" name="Convert_12464995" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="257" name="Constant_12464996" type="Const" version="opset1">
			<data element_type="f32" shape="3072, 1" offset="104621500" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="258" name="__module.transformer.layer.1.ffn.lin1/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="259" name="__module.transformer.layer.1.ffn.lin1/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="260" name="Constant_99391" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="104633788" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="261" name="__module.transformer.layer.1.ffn.lin1/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="193">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="262" name="__module.transformer.layer.1.ffn.activation/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="194">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="263" name="__module.transformer.layer.1.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="104646076" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="264" name="__module.transformer.layer.1.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="265" name="__module.transformer.layer.1.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="104658364" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="266" name="__module.transformer.layer.1.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="104658368" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="267" name="__module.transformer.layer.1.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="104658364" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="268" name="__module.transformer.layer.1.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="104658368" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="269" name="__module.transformer.layer.1.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="270" name="Constant_12464998" type="Const" version="opset1">
			<data element_type="i8" shape="768, 3072" offset="104658372" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="271" name="Convert_12464999" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="272" name="Constant_12465000" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="107017668" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="273" name="__module.transformer.layer.1.ffn.lin2/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="274" name="__module.transformer.layer.1.ffn.lin2/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="275" name="Constant_99392" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="107020740" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="276" name="__module.transformer.layer.1.ffn.lin2/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="197,input.13">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="277" name="__module.transformer.layer.1/aten::add/Add_1" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="199">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="278" name="__module.transformer.layer.1.output_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="279" name="__module.transformer.layer.1.output_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="280" name="Constant_99393" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="107023812" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="281" name="__module.transformer.layer.1.output_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="282" name="Constant_99394" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="107026884" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="283" name="__module.transformer.layer.1.output_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="203,query.5">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="284" name="__module.transformer.layer.1.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="107029956" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="285" name="__module.transformer.layer.1.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="286" name="__module.transformer.layer.1.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="107033028" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="287" name="__module.transformer.layer.1.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="107033032" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="288" name="__module.transformer.layer.1.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="107033028" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="289" name="__module.transformer.layer.1.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="107033032" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="290" name="__module.transformer.layer.1.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="291" name="Constant_12465002" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="107033036" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="292" name="Convert_12465003" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="293" name="Constant_12465004" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="107622860" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="294" name="__module.transformer.layer.2.attention.q_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="295" name="__module.transformer.layer.2.attention.q_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="296" name="Constant_99395" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="107625932" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="297" name="__module.transformer.layer.2.attention.q_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="216,x.17">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="298" name="Constant_99568" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="299" name="__module.transformer.layer.2.attention/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="218">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="300" name="__module.transformer.layer.2.attention/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="301" name="__module.transformer.layer.2.attention/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="219,q.5">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="302" name="Constant_99396" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1" offset="93294908" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="303" name="__module.transformer.layer.2.attention/aten::div/Divide" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="232">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="304" name="__module.transformer.layer.2.attention/aten::div/Divide/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="107629004" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="305" name="__module.transformer.layer.2.attention/aten::div/Divide/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="107629008" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="306" name="__module.transformer.layer.2.attention/aten::div/Divide/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="107629004" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="307" name="__module.transformer.layer.2.attention/aten::div/Divide/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="107629008" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="308" name="__module.transformer.layer.2.attention/aten::div/Divide/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="309" name="Constant_12465006" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="107629012" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="310" name="Convert_12465007" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="311" name="Constant_12465008" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="108218836" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="312" name="__module.transformer.layer.2.attention.k_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="313" name="__module.transformer.layer.2.attention.k_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="314" name="Constant_99397" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="108221908" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="315" name="__module.transformer.layer.2.attention.k_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="222,x.19">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="316" name="__module.transformer.layer.2.attention.k_lin/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="108224980" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="317" name="__module.transformer.layer.2.attention.k_lin/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="108224984" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="318" name="__module.transformer.layer.2.attention.k_lin/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="108224980" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="319" name="__module.transformer.layer.2.attention.k_lin/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="108224984" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="320" name="__module.transformer.layer.2.attention.k_lin/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="321" name="Constant_99569" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="322" name="__module.transformer.layer.2.attention/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="224">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="323" name="Constant_99088" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93890896" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="324" name="__module.transformer.layer.2.attention/aten::transpose/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="233">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="325" name="__module.transformer.layer.2.attention/aten::matmul/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="234,scores.5">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="326" name="__module.transformer.layer.2.attention/aten::expand_as/ShapeOf" type="ShapeOf" version="opset3">
			<data output_type="i32" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="327" name="__module.transformer.layer.2.attention/aten::expand_as/Broadcast" type="Broadcast" version="opset3">
			<data mode="bidirectional" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="BOOL" names="238,mask.5">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="328" name="__module.transformer.layer.2.attention/aten::masked_fill/ConvertLike" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93890912" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="329" name="__module.transformer.layer.2.attention/aten::masked_fill/Select" type="Select" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="241,input.15">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="330" name="__module.transformer.layer.2.attention/aten::softmax/Softmax" type="SoftMax" version="opset8">
			<data axis="-1" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="242,input.17">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="331" name="Constant_12465010" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="108224988" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="332" name="Convert_12465011" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="333" name="Constant_12465012" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="108814812" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="334" name="__module.transformer.layer.2.attention.v_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="335" name="__module.transformer.layer.2.attention.v_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="336" name="Constant_99398" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="108817884" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="337" name="__module.transformer.layer.2.attention.v_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="228,x.21">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="338" name="Constant_99570" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="339" name="__module.transformer.layer.2.attention/aten::view/Reshape_3" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="230">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="340" name="__module.transformer.layer.2.attention/aten::transpose/ScatterElementsUpdate_3" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="341" name="__module.transformer.layer.2.attention/aten::transpose/Transpose_3" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="231">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="342" name="__module.transformer.layer.2.attention/aten::matmul/MatMul_1" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="244,x.23">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="343" name="__module.transformer.layer.2.attention/aten::transpose/ScatterElementsUpdate_4" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="344" name="__module.transformer.layer.2.attention/aten::transpose/Transpose_4" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="245">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="345" name="Constant_99571" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="94486884" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="346" name="__module.transformer.layer.2.attention/aten::view/Reshape_4" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="248">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="347" name="__module.transformer.layer.2.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="108820956" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="348" name="__module.transformer.layer.2.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="349" name="__module.transformer.layer.2.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="108824028" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="350" name="__module.transformer.layer.2.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="108824032" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="351" name="__module.transformer.layer.2.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="108824028" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="352" name="__module.transformer.layer.2.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="108824032" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="353" name="__module.transformer.layer.2.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="354" name="Constant_12465014" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="108824036" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="355" name="Convert_12465015" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="356" name="Constant_12465016" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="109413860" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="357" name="__module.transformer.layer.2.attention.out_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="358" name="__module.transformer.layer.2.attention.out_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="359" name="Constant_99399" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="109416932" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="360" name="__module.transformer.layer.2.attention.out_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="251,sa_output.9">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="361" name="__module.transformer.layer.2/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="252">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="362" name="__module.transformer.layer.2.sa_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="363" name="__module.transformer.layer.2.sa_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="364" name="Constant_99400" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="109420004" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="365" name="__module.transformer.layer.2.sa_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="366" name="Constant_99401" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="109423076" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="367" name="__module.transformer.layer.2.sa_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="256,sa_output.11">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="368" name="__module.transformer.layer.2.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="109426148" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="369" name="__module.transformer.layer.2.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="370" name="__module.transformer.layer.2.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="109429220" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="371" name="__module.transformer.layer.2.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="109429224" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="372" name="__module.transformer.layer.2.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="109429220" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="373" name="__module.transformer.layer.2.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="109429224" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="374" name="__module.transformer.layer.2.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="375" name="Constant_12465018" type="Const" version="opset1">
			<data element_type="i8" shape="3072, 768" offset="109429228" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="376" name="Convert_12465019" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="377" name="Constant_12465020" type="Const" version="opset1">
			<data element_type="f32" shape="3072, 1" offset="111788524" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="378" name="__module.transformer.layer.2.ffn.lin1/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="379" name="__module.transformer.layer.2.ffn.lin1/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="380" name="Constant_99402" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="111800812" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="381" name="__module.transformer.layer.2.ffn.lin1/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="263">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="382" name="__module.transformer.layer.2.ffn.activation/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="264">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="383" name="__module.transformer.layer.2.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="111813100" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="384" name="__module.transformer.layer.2.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="385" name="__module.transformer.layer.2.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="111825388" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="386" name="__module.transformer.layer.2.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="111825392" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="387" name="__module.transformer.layer.2.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="111825388" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="388" name="__module.transformer.layer.2.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="111825392" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="389" name="__module.transformer.layer.2.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="390" name="Constant_12465022" type="Const" version="opset1">
			<data element_type="i8" shape="768, 3072" offset="111825396" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="391" name="Convert_12465023" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="392" name="Constant_12465024" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="114184692" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="393" name="__module.transformer.layer.2.ffn.lin2/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="394" name="__module.transformer.layer.2.ffn.lin2/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="395" name="Constant_99403" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="114187764" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="396" name="__module.transformer.layer.2.ffn.lin2/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="267,input.19">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="397" name="__module.transformer.layer.2/aten::add/Add_1" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="269">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="398" name="__module.transformer.layer.2.output_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="399" name="__module.transformer.layer.2.output_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="400" name="Constant_99404" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="114190836" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="401" name="__module.transformer.layer.2.output_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="402" name="Constant_99405" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="114193908" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="403" name="__module.transformer.layer.2.output_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="273,query.7">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="404" name="__module.transformer.layer.2.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="114196980" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="405" name="__module.transformer.layer.2.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="406" name="__module.transformer.layer.2.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="114200052" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="407" name="__module.transformer.layer.2.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="114200056" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="408" name="__module.transformer.layer.2.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="114200052" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="409" name="__module.transformer.layer.2.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="114200056" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="410" name="__module.transformer.layer.2.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="411" name="Constant_12465026" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="114200060" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="412" name="Convert_12465027" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="413" name="Constant_12465028" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="114789884" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="414" name="__module.transformer.layer.3.attention.q_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="415" name="__module.transformer.layer.3.attention.q_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="416" name="Constant_99406" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="114792956" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="417" name="__module.transformer.layer.3.attention.q_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="286,x.25">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="418" name="Constant_99572" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="419" name="__module.transformer.layer.3.attention/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="288">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="420" name="__module.transformer.layer.3.attention/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="421" name="__module.transformer.layer.3.attention/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="289,q.7">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="422" name="Constant_99407" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1" offset="93294908" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="423" name="__module.transformer.layer.3.attention/aten::div/Divide" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="302">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="424" name="__module.transformer.layer.3.attention/aten::div/Divide/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="114796028" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="425" name="__module.transformer.layer.3.attention/aten::div/Divide/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="114796032" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="426" name="__module.transformer.layer.3.attention/aten::div/Divide/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="114796028" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="427" name="__module.transformer.layer.3.attention/aten::div/Divide/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="114796032" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="428" name="__module.transformer.layer.3.attention/aten::div/Divide/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="429" name="Constant_12465030" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="114796036" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="430" name="Convert_12465031" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="431" name="Constant_12465032" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="115385860" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="432" name="__module.transformer.layer.3.attention.k_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="433" name="__module.transformer.layer.3.attention.k_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="434" name="Constant_99408" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="115388932" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="435" name="__module.transformer.layer.3.attention.k_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="292,x.27">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="436" name="__module.transformer.layer.3.attention.k_lin/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="115392004" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="437" name="__module.transformer.layer.3.attention.k_lin/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="115392008" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="438" name="__module.transformer.layer.3.attention.k_lin/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="115392004" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="439" name="__module.transformer.layer.3.attention.k_lin/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="115392008" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="440" name="__module.transformer.layer.3.attention.k_lin/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="441" name="Constant_99573" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="442" name="__module.transformer.layer.3.attention/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="294">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="443" name="Constant_99090" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93890896" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="444" name="__module.transformer.layer.3.attention/aten::transpose/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="303">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="445" name="__module.transformer.layer.3.attention/aten::matmul/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="304,scores.7">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="446" name="__module.transformer.layer.3.attention/aten::expand_as/ShapeOf" type="ShapeOf" version="opset3">
			<data output_type="i32" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="447" name="__module.transformer.layer.3.attention/aten::expand_as/Broadcast" type="Broadcast" version="opset3">
			<data mode="bidirectional" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="BOOL" names="308,mask.7">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="448" name="__module.transformer.layer.3.attention/aten::masked_fill/ConvertLike" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93890912" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="449" name="__module.transformer.layer.3.attention/aten::masked_fill/Select" type="Select" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="311,input.21">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="450" name="__module.transformer.layer.3.attention/aten::softmax/Softmax" type="SoftMax" version="opset8">
			<data axis="-1" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="312,input.23">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="451" name="Constant_12465034" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="115392012" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="452" name="Convert_12465035" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="453" name="Constant_12465036" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="115981836" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="454" name="__module.transformer.layer.3.attention.v_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="455" name="__module.transformer.layer.3.attention.v_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="456" name="Constant_99409" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="115984908" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="457" name="__module.transformer.layer.3.attention.v_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="298,x.29">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="458" name="Constant_99574" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="459" name="__module.transformer.layer.3.attention/aten::view/Reshape_3" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="300">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="460" name="__module.transformer.layer.3.attention/aten::transpose/ScatterElementsUpdate_3" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="461" name="__module.transformer.layer.3.attention/aten::transpose/Transpose_3" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="301">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="462" name="__module.transformer.layer.3.attention/aten::matmul/MatMul_1" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="314,x.31">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="463" name="__module.transformer.layer.3.attention/aten::transpose/ScatterElementsUpdate_4" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="464" name="__module.transformer.layer.3.attention/aten::transpose/Transpose_4" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="315">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="465" name="Constant_99575" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="94486884" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="466" name="__module.transformer.layer.3.attention/aten::view/Reshape_4" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="318">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="467" name="__module.transformer.layer.3.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="115987980" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="468" name="__module.transformer.layer.3.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="469" name="__module.transformer.layer.3.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="115991052" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="470" name="__module.transformer.layer.3.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="115991056" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="471" name="__module.transformer.layer.3.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="115991052" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="472" name="__module.transformer.layer.3.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="115991056" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="473" name="__module.transformer.layer.3.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="474" name="Constant_12465038" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="115991060" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="475" name="Convert_12465039" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="476" name="Constant_12465040" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="116580884" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="477" name="__module.transformer.layer.3.attention.out_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="478" name="__module.transformer.layer.3.attention.out_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="479" name="Constant_99410" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="116583956" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="480" name="__module.transformer.layer.3.attention.out_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="321,sa_output.13">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="481" name="__module.transformer.layer.3/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="322">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="482" name="__module.transformer.layer.3.sa_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="483" name="__module.transformer.layer.3.sa_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="484" name="Constant_99411" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="116587028" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="485" name="__module.transformer.layer.3.sa_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="486" name="Constant_99412" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="116590100" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="487" name="__module.transformer.layer.3.sa_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="326,sa_output.15">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="488" name="__module.transformer.layer.3.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="116593172" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="489" name="__module.transformer.layer.3.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="490" name="__module.transformer.layer.3.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="116596244" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="491" name="__module.transformer.layer.3.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="116596248" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="492" name="__module.transformer.layer.3.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="116596244" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="493" name="__module.transformer.layer.3.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="116596248" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="494" name="__module.transformer.layer.3.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="495" name="Constant_12465042" type="Const" version="opset1">
			<data element_type="i8" shape="3072, 768" offset="116596252" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="496" name="Convert_12465043" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="497" name="Constant_12465044" type="Const" version="opset1">
			<data element_type="f32" shape="3072, 1" offset="118955548" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="498" name="__module.transformer.layer.3.ffn.lin1/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="499" name="__module.transformer.layer.3.ffn.lin1/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="500" name="Constant_99413" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="118967836" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="501" name="__module.transformer.layer.3.ffn.lin1/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="333">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="502" name="__module.transformer.layer.3.ffn.activation/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="334">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="503" name="__module.transformer.layer.3.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="118980124" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="504" name="__module.transformer.layer.3.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="505" name="__module.transformer.layer.3.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="118992412" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="506" name="__module.transformer.layer.3.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="118992416" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="507" name="__module.transformer.layer.3.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="118992412" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="508" name="__module.transformer.layer.3.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="118992416" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="509" name="__module.transformer.layer.3.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="510" name="Constant_12465046" type="Const" version="opset1">
			<data element_type="i8" shape="768, 3072" offset="118992420" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="511" name="Convert_12465047" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="512" name="Constant_12465048" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="121351716" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="513" name="__module.transformer.layer.3.ffn.lin2/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="514" name="__module.transformer.layer.3.ffn.lin2/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="515" name="Constant_99414" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="121354788" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="516" name="__module.transformer.layer.3.ffn.lin2/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="337,input.25">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="517" name="__module.transformer.layer.3/aten::add/Add_1" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="339">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="518" name="__module.transformer.layer.3.output_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="519" name="__module.transformer.layer.3.output_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="520" name="Constant_99415" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="121357860" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="521" name="__module.transformer.layer.3.output_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="522" name="Constant_99416" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="121360932" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="523" name="__module.transformer.layer.3.output_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="343,query.9">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="524" name="__module.transformer.layer.3.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="121364004" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="525" name="__module.transformer.layer.3.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="526" name="__module.transformer.layer.3.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="121367076" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="527" name="__module.transformer.layer.3.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="121367080" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="528" name="__module.transformer.layer.3.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="121367076" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="529" name="__module.transformer.layer.3.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="121367080" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="530" name="__module.transformer.layer.3.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="531" name="Constant_12465050" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="121367084" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="532" name="Convert_12465051" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="533" name="Constant_12465052" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="121956908" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="534" name="__module.transformer.layer.4.attention.q_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="535" name="__module.transformer.layer.4.attention.q_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="536" name="Constant_99417" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="121959980" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="537" name="__module.transformer.layer.4.attention.q_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="356,x.33">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="538" name="Constant_99576" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="539" name="__module.transformer.layer.4.attention/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="358">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="540" name="__module.transformer.layer.4.attention/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="541" name="__module.transformer.layer.4.attention/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="359,q.9">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="542" name="Constant_99418" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1" offset="93294908" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="543" name="__module.transformer.layer.4.attention/aten::div/Divide" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="372">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="544" name="__module.transformer.layer.4.attention/aten::div/Divide/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="121963052" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="545" name="__module.transformer.layer.4.attention/aten::div/Divide/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="121963056" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="546" name="__module.transformer.layer.4.attention/aten::div/Divide/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="121963052" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="547" name="__module.transformer.layer.4.attention/aten::div/Divide/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="121963056" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="548" name="__module.transformer.layer.4.attention/aten::div/Divide/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="549" name="Constant_12465054" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="121963060" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="550" name="Convert_12465055" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="551" name="Constant_12465056" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="122552884" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="552" name="__module.transformer.layer.4.attention.k_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="553" name="__module.transformer.layer.4.attention.k_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="554" name="Constant_99419" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="122555956" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="555" name="__module.transformer.layer.4.attention.k_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="362,x.35">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="556" name="__module.transformer.layer.4.attention.k_lin/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="122559028" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="557" name="__module.transformer.layer.4.attention.k_lin/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="122559032" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="558" name="__module.transformer.layer.4.attention.k_lin/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="122559028" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="559" name="__module.transformer.layer.4.attention.k_lin/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="122559032" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="560" name="__module.transformer.layer.4.attention.k_lin/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="561" name="Constant_99577" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="562" name="__module.transformer.layer.4.attention/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="364">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="563" name="Constant_99092" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93890896" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="564" name="__module.transformer.layer.4.attention/aten::transpose/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="373">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="565" name="__module.transformer.layer.4.attention/aten::matmul/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="374,scores.9">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="566" name="__module.transformer.layer.4.attention/aten::expand_as/ShapeOf" type="ShapeOf" version="opset3">
			<data output_type="i32" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="567" name="__module.transformer.layer.4.attention/aten::expand_as/Broadcast" type="Broadcast" version="opset3">
			<data mode="bidirectional" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="BOOL" names="378,mask.9">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="568" name="__module.transformer.layer.4.attention/aten::masked_fill/ConvertLike" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93890912" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="569" name="__module.transformer.layer.4.attention/aten::masked_fill/Select" type="Select" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="381,input.27">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="570" name="__module.transformer.layer.4.attention/aten::softmax/Softmax" type="SoftMax" version="opset8">
			<data axis="-1" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="382,input.29">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="571" name="Constant_12465058" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="122559036" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="572" name="Convert_12465059" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="573" name="Constant_12465060" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="123148860" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="574" name="__module.transformer.layer.4.attention.v_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="575" name="__module.transformer.layer.4.attention.v_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="576" name="Constant_99420" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="123151932" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="577" name="__module.transformer.layer.4.attention.v_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="368,x.37">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="578" name="Constant_99578" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="579" name="__module.transformer.layer.4.attention/aten::view/Reshape_3" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="370">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="580" name="__module.transformer.layer.4.attention/aten::transpose/ScatterElementsUpdate_3" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="581" name="__module.transformer.layer.4.attention/aten::transpose/Transpose_3" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="371">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="582" name="__module.transformer.layer.4.attention/aten::matmul/MatMul_1" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="384,x.39">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="583" name="__module.transformer.layer.4.attention/aten::transpose/ScatterElementsUpdate_4" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="584" name="__module.transformer.layer.4.attention/aten::transpose/Transpose_4" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="385">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="585" name="Constant_99579" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="94486884" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="586" name="__module.transformer.layer.4.attention/aten::view/Reshape_4" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="388">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="587" name="__module.transformer.layer.4.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="123155004" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="588" name="__module.transformer.layer.4.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="589" name="__module.transformer.layer.4.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="123158076" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="590" name="__module.transformer.layer.4.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="123158080" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="591" name="__module.transformer.layer.4.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="123158076" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="592" name="__module.transformer.layer.4.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="123158080" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="593" name="__module.transformer.layer.4.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="594" name="Constant_12465062" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="123158084" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="595" name="Convert_12465063" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="596" name="Constant_12465064" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="123747908" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="597" name="__module.transformer.layer.4.attention.out_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="598" name="__module.transformer.layer.4.attention.out_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="599" name="Constant_99421" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="123750980" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="600" name="__module.transformer.layer.4.attention.out_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="391,sa_output.17">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="601" name="__module.transformer.layer.4/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="392">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="602" name="__module.transformer.layer.4.sa_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="603" name="__module.transformer.layer.4.sa_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="604" name="Constant_99422" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="123754052" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="605" name="__module.transformer.layer.4.sa_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="606" name="Constant_99423" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="123757124" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="607" name="__module.transformer.layer.4.sa_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="396,sa_output.19">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="608" name="__module.transformer.layer.4.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="123760196" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="609" name="__module.transformer.layer.4.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="610" name="__module.transformer.layer.4.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="123763268" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="611" name="__module.transformer.layer.4.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="123763272" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="612" name="__module.transformer.layer.4.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="123763268" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="613" name="__module.transformer.layer.4.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="123763272" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="614" name="__module.transformer.layer.4.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="615" name="Constant_12465066" type="Const" version="opset1">
			<data element_type="i8" shape="3072, 768" offset="123763276" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="616" name="Convert_12465067" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="617" name="Constant_12465068" type="Const" version="opset1">
			<data element_type="f32" shape="3072, 1" offset="126122572" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="618" name="__module.transformer.layer.4.ffn.lin1/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="619" name="__module.transformer.layer.4.ffn.lin1/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="620" name="Constant_99424" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="126134860" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="621" name="__module.transformer.layer.4.ffn.lin1/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="403">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="622" name="__module.transformer.layer.4.ffn.activation/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="404">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="623" name="__module.transformer.layer.4.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="126147148" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="624" name="__module.transformer.layer.4.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="625" name="__module.transformer.layer.4.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="126159436" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="626" name="__module.transformer.layer.4.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="126159440" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="627" name="__module.transformer.layer.4.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="126159436" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="628" name="__module.transformer.layer.4.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="126159440" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="629" name="__module.transformer.layer.4.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="630" name="Constant_12465070" type="Const" version="opset1">
			<data element_type="i8" shape="768, 3072" offset="126159444" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="631" name="Convert_12465071" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="632" name="Constant_12465072" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="128518740" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="633" name="__module.transformer.layer.4.ffn.lin2/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="634" name="__module.transformer.layer.4.ffn.lin2/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="635" name="Constant_99425" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="128521812" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="636" name="__module.transformer.layer.4.ffn.lin2/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="407,input.31">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="637" name="__module.transformer.layer.4/aten::add/Add_1" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="409">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="638" name="__module.transformer.layer.4.output_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="639" name="__module.transformer.layer.4.output_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="640" name="Constant_99426" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="128524884" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="641" name="__module.transformer.layer.4.output_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="642" name="Constant_99427" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="128527956" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="643" name="__module.transformer.layer.4.output_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="413,query">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="644" name="__module.transformer.layer.4.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="128531028" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="645" name="__module.transformer.layer.4.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="646" name="__module.transformer.layer.4.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="128534100" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="647" name="__module.transformer.layer.4.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="128534104" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="648" name="__module.transformer.layer.4.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="128534100" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="649" name="__module.transformer.layer.4.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="128534104" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="650" name="__module.transformer.layer.4.output_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="651" name="Constant_12465074" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="128534108" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="652" name="Convert_12465075" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="653" name="Constant_12465076" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="129123932" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="654" name="__module.transformer.layer.5.attention.q_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="655" name="__module.transformer.layer.5.attention.q_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="656" name="Constant_99428" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="129127004" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="657" name="__module.transformer.layer.5.attention.q_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="426,x.41">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="658" name="Constant_99580" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="659" name="__module.transformer.layer.5.attention/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="428">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="660" name="__module.transformer.layer.5.attention/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="661" name="__module.transformer.layer.5.attention/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="429,q">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="662" name="Constant_99429" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1" offset="93294908" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="663" name="__module.transformer.layer.5.attention/aten::div/Divide" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="442">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="664" name="__module.transformer.layer.5.attention/aten::div/Divide/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="129130076" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="665" name="__module.transformer.layer.5.attention/aten::div/Divide/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="129130080" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="666" name="__module.transformer.layer.5.attention/aten::div/Divide/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="129130076" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="667" name="__module.transformer.layer.5.attention/aten::div/Divide/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="129130080" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="668" name="__module.transformer.layer.5.attention/aten::div/Divide/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="669" name="Constant_12465078" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="129130084" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="670" name="Convert_12465079" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="671" name="Constant_12465080" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="129719908" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="672" name="__module.transformer.layer.5.attention.k_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="673" name="__module.transformer.layer.5.attention.k_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="674" name="Constant_99430" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="129722980" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="675" name="__module.transformer.layer.5.attention.k_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="432,x.43">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="676" name="__module.transformer.layer.5.attention.k_lin/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="129726052" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="677" name="__module.transformer.layer.5.attention.k_lin/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="129726056" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="678" name="__module.transformer.layer.5.attention.k_lin/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="129726052" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="679" name="__module.transformer.layer.5.attention.k_lin/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="129726056" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="680" name="__module.transformer.layer.5.attention.k_lin/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="681" name="Constant_99581" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="682" name="__module.transformer.layer.5.attention/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="434">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="683" name="Constant_99094" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93890896" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="684" name="__module.transformer.layer.5.attention/aten::transpose/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="443">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="685" name="__module.transformer.layer.5.attention/aten::matmul/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="444,scores">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="686" name="__module.transformer.layer.5.attention/aten::expand_as/ShapeOf" type="ShapeOf" version="opset3">
			<data output_type="i32" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="687" name="__module.transformer.layer.5.attention/aten::expand_as/Broadcast" type="Broadcast" version="opset3">
			<data mode="bidirectional" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="BOOL" names="448,mask">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="688" name="__module.transformer.layer.5.attention/aten::masked_fill/ConvertLike" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="93890912" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="689" name="__module.transformer.layer.5.attention/aten::masked_fill/Select" type="Select" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="451,input.33">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="690" name="__module.transformer.layer.5.attention/aten::softmax/Softmax" type="SoftMax" version="opset8">
			<data axis="-1" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="452,input.35">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="691" name="Constant_12465082" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="129726060" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="692" name="Convert_12465083" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="693" name="Constant_12465084" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="130315884" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="694" name="__module.transformer.layer.5.attention.v_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="695" name="__module.transformer.layer.5.attention.v_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="696" name="Constant_99431" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="130318956" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="697" name="__module.transformer.layer.5.attention.v_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="438,x.45">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="698" name="Constant_99582" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="93294860" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="699" name="__module.transformer.layer.5.attention/aten::view/Reshape_3" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="440">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="700" name="__module.transformer.layer.5.attention/aten::transpose/ScatterElementsUpdate_3" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="701" name="__module.transformer.layer.5.attention/aten::transpose/Transpose_3" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="441">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="702" name="__module.transformer.layer.5.attention/aten::matmul/MatMul_1" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="454,x">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="703" name="__module.transformer.layer.5.attention/aten::transpose/ScatterElementsUpdate_4" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="93294892" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="704" name="__module.transformer.layer.5.attention/aten::transpose/Transpose_4" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="455">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
			</output>
		</layer>
		<layer id="705" name="Constant_99583" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="94486884" size="24" />
			<output>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="706" name="__module.transformer.layer.5.attention/aten::view/Reshape_4" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>64</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="458">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="707" name="__module.transformer.layer.5.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="130322028" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="708" name="__module.transformer.layer.5.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="709" name="__module.transformer.layer.5.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="130325100" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="710" name="__module.transformer.layer.5.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="130325104" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="711" name="__module.transformer.layer.5.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="130325100" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="712" name="__module.transformer.layer.5.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="130325104" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="713" name="__module.transformer.layer.5.attention/aten::view/Reshape_4_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="714" name="Constant_12465086" type="Const" version="opset1">
			<data element_type="i8" shape="768, 768" offset="130325108" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="715" name="Convert_12465087" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="716" name="Constant_12465088" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="130914932" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="717" name="__module.transformer.layer.5.attention.out_lin/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="718" name="__module.transformer.layer.5.attention.out_lin/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="719" name="Constant_99432" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="130918004" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="720" name="__module.transformer.layer.5.attention.out_lin/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="461,sa_output.21">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="721" name="__module.transformer.layer.5/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="462">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="722" name="__module.transformer.layer.5.sa_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="723" name="__module.transformer.layer.5.sa_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="724" name="Constant_99433" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="130921076" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="725" name="__module.transformer.layer.5.sa_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="726" name="Constant_99434" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="130924148" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="727" name="__module.transformer.layer.5.sa_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="466,sa_output">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="728" name="__module.transformer.layer.5.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="130927220" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="729" name="__module.transformer.layer.5.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="730" name="__module.transformer.layer.5.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="130930292" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="731" name="__module.transformer.layer.5.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="130930296" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="732" name="__module.transformer.layer.5.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="130930292" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="733" name="__module.transformer.layer.5.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="130930296" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="734" name="__module.transformer.layer.5.sa_layer_norm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="735" name="Constant_12465090" type="Const" version="opset1">
			<data element_type="i8" shape="3072, 768" offset="130930300" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="736" name="Convert_12465091" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="737" name="Constant_12465092" type="Const" version="opset1">
			<data element_type="f32" shape="3072, 1" offset="133289596" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="738" name="__module.transformer.layer.5.ffn.lin1/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="739" name="__module.transformer.layer.5.ffn.lin1/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>3072</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="740" name="Constant_99435" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="133301884" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="741" name="__module.transformer.layer.5.ffn.lin1/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="473">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="742" name="__module.transformer.layer.5.ffn.activation/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="474">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="743" name="__module.transformer.layer.5.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 3072" offset="133314172" size="12288" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="744" name="__module.transformer.layer.5.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="745" name="__module.transformer.layer.5.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="133326460" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="746" name="__module.transformer.layer.5.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="133326464" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="747" name="__module.transformer.layer.5.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="133326460" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="748" name="__module.transformer.layer.5.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="133326464" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="749" name="__module.transformer.layer.5.ffn.activation/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="750" name="Constant_12465094" type="Const" version="opset1">
			<data element_type="i8" shape="768, 3072" offset="133326468" size="2359296" />
			<output>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="751" name="Convert_12465095" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="752" name="Constant_12465096" type="Const" version="opset1">
			<data element_type="f32" shape="768, 1" offset="135685764" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="753" name="__module.transformer.layer.5.ffn.lin2/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</output>
		</layer>
		<layer id="754" name="__module.transformer.layer.5.ffn.lin2/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>3072</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>768</dim>
					<dim>3072</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="755" name="Constant_99436" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="135688836" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="756" name="__module.transformer.layer.5.ffn.lin2/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="477,input">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="757" name="__module.transformer.layer.5/aten::add/Add_1" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="479">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="758" name="__module.transformer.layer.5.output_layer_norm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="92689664" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="759" name="__module.transformer.layer.5.output_layer_norm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="760" name="Constant_99437" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="135691908" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="761" name="__module.transformer.layer.5.output_layer_norm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="762" name="Constant_99438" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 768" offset="135694980" size="3072" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="763" name="__module.transformer.layer.5.output_layer_norm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>768</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="last_hidden_state">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</output>
		</layer>
		<layer id="764" name="Result_90149" type="Result" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>768</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="3" to-port="0" />
		<edge from-layer="1" from-port="0" to-layer="4" to-port="0" />
		<edge from-layer="2" from-port="0" to-layer="3" to-port="1" />
		<edge from-layer="3" from-port="2" to-layer="43" to-port="0" />
		<edge from-layer="4" from-port="1" to-layer="5" to-port="0" />
		<edge from-layer="4" from-port="1" to-layer="15" to-port="1" />
		<edge from-layer="5" from-port="1" to-layer="8" to-port="0" />
		<edge from-layer="5" from-port="1" to-layer="24" to-port="0" />
		<edge from-layer="6" from-port="0" to-layer="8" to-port="1" />
		<edge from-layer="7" from-port="0" to-layer="8" to-port="2" />
		<edge from-layer="8" from-port="3" to-layer="42" to-port="0" />
		<edge from-layer="9" from-port="0" to-layer="42" to-port="1" />
		<edge from-layer="9" from-port="0" to-layer="42" to-port="2" />
		<edge from-layer="10" from-port="0" to-layer="11" to-port="0" />
		<edge from-layer="11" from-port="1" to-layer="13" to-port="0" />
		<edge from-layer="12" from-port="0" to-layer="13" to-port="1" />
		<edge from-layer="13" from-port="2" to-layer="15" to-port="0" />
		<edge from-layer="14" from-port="0" to-layer="15" to-port="2" />
		<edge from-layer="15" from-port="3" to-layer="31" to-port="0" />
		<edge from-layer="16" from-port="0" to-layer="17" to-port="0" />
		<edge from-layer="17" from-port="1" to-layer="19" to-port="0" />
		<edge from-layer="18" from-port="0" to-layer="19" to-port="1" />
		<edge from-layer="19" from-port="2" to-layer="30" to-port="0" />
		<edge from-layer="20" from-port="0" to-layer="27" to-port="0" />
		<edge from-layer="21" from-port="0" to-layer="27" to-port="1" />
		<edge from-layer="22" from-port="0" to-layer="24" to-port="1" />
		<edge from-layer="23" from-port="0" to-layer="24" to-port="2" />
		<edge from-layer="24" from-port="3" to-layer="27" to-port="2" />
		<edge from-layer="25" from-port="0" to-layer="27" to-port="3" />
		<edge from-layer="26" from-port="0" to-layer="27" to-port="4" />
		<edge from-layer="27" from-port="5" to-layer="28" to-port="0" />
		<edge from-layer="28" from-port="1" to-layer="30" to-port="1" />
		<edge from-layer="29" from-port="0" to-layer="30" to-port="2" />
		<edge from-layer="30" from-port="3" to-layer="31" to-port="1" />
		<edge from-layer="31" from-port="2" to-layer="33" to-port="0" />
		<edge from-layer="32" from-port="0" to-layer="33" to-port="1" />
		<edge from-layer="33" from-port="2" to-layer="35" to-port="0" />
		<edge from-layer="34" from-port="0" to-layer="35" to-port="1" />
		<edge from-layer="35" from-port="2" to-layer="37" to-port="0" />
		<edge from-layer="36" from-port="0" to-layer="37" to-port="1" />
		<edge from-layer="37" from-port="2" to-layer="38" to-port="0" />
		<edge from-layer="37" from-port="2" to-layer="45" to-port="0" />
		<edge from-layer="37" from-port="2" to-layer="121" to-port="1" />
		<edge from-layer="38" from-port="1" to-layer="41" to-port="0" />
		<edge from-layer="39" from-port="0" to-layer="41" to-port="1" />
		<edge from-layer="40" from-port="0" to-layer="41" to-port="2" />
		<edge from-layer="41" from-port="3" to-layer="42" to-port="3" />
		<edge from-layer="42" from-port="4" to-layer="43" to-port="1" />
		<edge from-layer="43" from-port="2" to-layer="327" to-port="0" />
		<edge from-layer="43" from-port="2" to-layer="87" to-port="0" />
		<edge from-layer="43" from-port="2" to-layer="447" to-port="0" />
		<edge from-layer="43" from-port="2" to-layer="567" to-port="0" />
		<edge from-layer="43" from-port="2" to-layer="207" to-port="0" />
		<edge from-layer="43" from-port="2" to-layer="687" to-port="0" />
		<edge from-layer="44" from-port="0" to-layer="45" to-port="1" />
		<edge from-layer="45" from-port="2" to-layer="50" to-port="0" />
		<edge from-layer="46" from-port="0" to-layer="50" to-port="1" />
		<edge from-layer="47" from-port="0" to-layer="50" to-port="2" />
		<edge from-layer="48" from-port="0" to-layer="50" to-port="3" />
		<edge from-layer="49" from-port="0" to-layer="50" to-port="4" />
		<edge from-layer="50" from-port="5" to-layer="73" to-port="0" />
		<edge from-layer="50" from-port="5" to-layer="95" to-port="0" />
		<edge from-layer="50" from-port="5" to-layer="55" to-port="0" />
		<edge from-layer="51" from-port="0" to-layer="52" to-port="0" />
		<edge from-layer="52" from-port="1" to-layer="54" to-port="0" />
		<edge from-layer="53" from-port="0" to-layer="54" to-port="1" />
		<edge from-layer="54" from-port="2" to-layer="55" to-port="1" />
		<edge from-layer="55" from-port="2" to-layer="57" to-port="0" />
		<edge from-layer="56" from-port="0" to-layer="57" to-port="1" />
		<edge from-layer="57" from-port="2" to-layer="59" to-port="0" />
		<edge from-layer="58" from-port="0" to-layer="59" to-port="1" />
		<edge from-layer="59" from-port="2" to-layer="61" to-port="0" />
		<edge from-layer="60" from-port="0" to-layer="61" to-port="1" />
		<edge from-layer="61" from-port="2" to-layer="63" to-port="0" />
		<edge from-layer="62" from-port="0" to-layer="63" to-port="1" />
		<edge from-layer="63" from-port="2" to-layer="68" to-port="0" />
		<edge from-layer="64" from-port="0" to-layer="68" to-port="1" />
		<edge from-layer="65" from-port="0" to-layer="68" to-port="2" />
		<edge from-layer="66" from-port="0" to-layer="68" to-port="3" />
		<edge from-layer="67" from-port="0" to-layer="68" to-port="4" />
		<edge from-layer="68" from-port="5" to-layer="85" to-port="0" />
		<edge from-layer="69" from-port="0" to-layer="70" to-port="0" />
		<edge from-layer="70" from-port="1" to-layer="72" to-port="0" />
		<edge from-layer="71" from-port="0" to-layer="72" to-port="1" />
		<edge from-layer="72" from-port="2" to-layer="73" to-port="1" />
		<edge from-layer="73" from-port="2" to-layer="75" to-port="0" />
		<edge from-layer="74" from-port="0" to-layer="75" to-port="1" />
		<edge from-layer="75" from-port="2" to-layer="80" to-port="0" />
		<edge from-layer="76" from-port="0" to-layer="80" to-port="1" />
		<edge from-layer="77" from-port="0" to-layer="80" to-port="2" />
		<edge from-layer="78" from-port="0" to-layer="80" to-port="3" />
		<edge from-layer="79" from-port="0" to-layer="80" to-port="4" />
		<edge from-layer="80" from-port="5" to-layer="82" to-port="0" />
		<edge from-layer="81" from-port="0" to-layer="82" to-port="1" />
		<edge from-layer="82" from-port="2" to-layer="84" to-port="0" />
		<edge from-layer="83" from-port="0" to-layer="84" to-port="1" />
		<edge from-layer="84" from-port="2" to-layer="85" to-port="1" />
		<edge from-layer="85" from-port="2" to-layer="89" to-port="2" />
		<edge from-layer="85" from-port="2" to-layer="86" to-port="0" />
		<edge from-layer="86" from-port="1" to-layer="87" to-port="1" />
		<edge from-layer="87" from-port="2" to-layer="89" to-port="0" />
		<edge from-layer="88" from-port="0" to-layer="89" to-port="1" />
		<edge from-layer="89" from-port="3" to-layer="90" to-port="0" />
		<edge from-layer="90" from-port="1" to-layer="102" to-port="0" />
		<edge from-layer="91" from-port="0" to-layer="92" to-port="0" />
		<edge from-layer="92" from-port="1" to-layer="94" to-port="0" />
		<edge from-layer="93" from-port="0" to-layer="94" to-port="1" />
		<edge from-layer="94" from-port="2" to-layer="95" to-port="1" />
		<edge from-layer="95" from-port="2" to-layer="97" to-port="0" />
		<edge from-layer="96" from-port="0" to-layer="97" to-port="1" />
		<edge from-layer="97" from-port="2" to-layer="99" to-port="0" />
		<edge from-layer="98" from-port="0" to-layer="99" to-port="1" />
		<edge from-layer="99" from-port="2" to-layer="101" to-port="0" />
		<edge from-layer="100" from-port="0" to-layer="101" to-port="1" />
		<edge from-layer="101" from-port="2" to-layer="102" to-port="1" />
		<edge from-layer="102" from-port="2" to-layer="104" to-port="0" />
		<edge from-layer="103" from-port="0" to-layer="104" to-port="1" />
		<edge from-layer="104" from-port="2" to-layer="106" to-port="0" />
		<edge from-layer="105" from-port="0" to-layer="106" to-port="1" />
		<edge from-layer="106" from-port="2" to-layer="108" to-port="0" />
		<edge from-layer="107" from-port="0" to-layer="108" to-port="1" />
		<edge from-layer="108" from-port="2" to-layer="113" to-port="0" />
		<edge from-layer="109" from-port="0" to-layer="113" to-port="1" />
		<edge from-layer="110" from-port="0" to-layer="113" to-port="2" />
		<edge from-layer="111" from-port="0" to-layer="113" to-port="3" />
		<edge from-layer="112" from-port="0" to-layer="113" to-port="4" />
		<edge from-layer="113" from-port="5" to-layer="118" to-port="0" />
		<edge from-layer="114" from-port="0" to-layer="115" to-port="0" />
		<edge from-layer="115" from-port="1" to-layer="117" to-port="0" />
		<edge from-layer="116" from-port="0" to-layer="117" to-port="1" />
		<edge from-layer="117" from-port="2" to-layer="118" to-port="1" />
		<edge from-layer="118" from-port="2" to-layer="120" to-port="0" />
		<edge from-layer="119" from-port="0" to-layer="120" to-port="1" />
		<edge from-layer="120" from-port="2" to-layer="121" to-port="0" />
		<edge from-layer="121" from-port="2" to-layer="123" to-port="0" />
		<edge from-layer="122" from-port="0" to-layer="123" to-port="1" />
		<edge from-layer="123" from-port="2" to-layer="125" to-port="0" />
		<edge from-layer="124" from-port="0" to-layer="125" to-port="1" />
		<edge from-layer="125" from-port="2" to-layer="127" to-port="0" />
		<edge from-layer="126" from-port="0" to-layer="127" to-port="1" />
		<edge from-layer="127" from-port="2" to-layer="129" to-port="0" />
		<edge from-layer="127" from-port="2" to-layer="157" to-port="1" />
		<edge from-layer="128" from-port="0" to-layer="129" to-port="1" />
		<edge from-layer="129" from-port="2" to-layer="134" to-port="0" />
		<edge from-layer="130" from-port="0" to-layer="134" to-port="1" />
		<edge from-layer="131" from-port="0" to-layer="134" to-port="2" />
		<edge from-layer="132" from-port="0" to-layer="134" to-port="3" />
		<edge from-layer="133" from-port="0" to-layer="134" to-port="4" />
		<edge from-layer="134" from-port="5" to-layer="139" to-port="0" />
		<edge from-layer="135" from-port="0" to-layer="136" to-port="0" />
		<edge from-layer="136" from-port="1" to-layer="138" to-port="0" />
		<edge from-layer="137" from-port="0" to-layer="138" to-port="1" />
		<edge from-layer="138" from-port="2" to-layer="139" to-port="1" />
		<edge from-layer="139" from-port="2" to-layer="141" to-port="0" />
		<edge from-layer="140" from-port="0" to-layer="141" to-port="1" />
		<edge from-layer="141" from-port="2" to-layer="142" to-port="0" />
		<edge from-layer="142" from-port="1" to-layer="144" to-port="0" />
		<edge from-layer="143" from-port="0" to-layer="144" to-port="1" />
		<edge from-layer="144" from-port="2" to-layer="149" to-port="0" />
		<edge from-layer="145" from-port="0" to-layer="149" to-port="1" />
		<edge from-layer="146" from-port="0" to-layer="149" to-port="2" />
		<edge from-layer="147" from-port="0" to-layer="149" to-port="3" />
		<edge from-layer="148" from-port="0" to-layer="149" to-port="4" />
		<edge from-layer="149" from-port="5" to-layer="154" to-port="0" />
		<edge from-layer="150" from-port="0" to-layer="151" to-port="0" />
		<edge from-layer="151" from-port="1" to-layer="153" to-port="0" />
		<edge from-layer="152" from-port="0" to-layer="153" to-port="1" />
		<edge from-layer="153" from-port="2" to-layer="154" to-port="1" />
		<edge from-layer="154" from-port="2" to-layer="156" to-port="0" />
		<edge from-layer="155" from-port="0" to-layer="156" to-port="1" />
		<edge from-layer="156" from-port="2" to-layer="157" to-port="0" />
		<edge from-layer="157" from-port="2" to-layer="159" to-port="0" />
		<edge from-layer="158" from-port="0" to-layer="159" to-port="1" />
		<edge from-layer="159" from-port="2" to-layer="161" to-port="0" />
		<edge from-layer="160" from-port="0" to-layer="161" to-port="1" />
		<edge from-layer="161" from-port="2" to-layer="163" to-port="0" />
		<edge from-layer="162" from-port="0" to-layer="163" to-port="1" />
		<edge from-layer="163" from-port="2" to-layer="165" to-port="0" />
		<edge from-layer="163" from-port="2" to-layer="241" to-port="1" />
		<edge from-layer="164" from-port="0" to-layer="165" to-port="1" />
		<edge from-layer="165" from-port="2" to-layer="170" to-port="0" />
		<edge from-layer="166" from-port="0" to-layer="170" to-port="1" />
		<edge from-layer="167" from-port="0" to-layer="170" to-port="2" />
		<edge from-layer="168" from-port="0" to-layer="170" to-port="3" />
		<edge from-layer="169" from-port="0" to-layer="170" to-port="4" />
		<edge from-layer="170" from-port="5" to-layer="175" to-port="0" />
		<edge from-layer="170" from-port="5" to-layer="215" to-port="0" />
		<edge from-layer="170" from-port="5" to-layer="193" to-port="0" />
		<edge from-layer="171" from-port="0" to-layer="172" to-port="0" />
		<edge from-layer="172" from-port="1" to-layer="174" to-port="0" />
		<edge from-layer="173" from-port="0" to-layer="174" to-port="1" />
		<edge from-layer="174" from-port="2" to-layer="175" to-port="1" />
		<edge from-layer="175" from-port="2" to-layer="177" to-port="0" />
		<edge from-layer="176" from-port="0" to-layer="177" to-port="1" />
		<edge from-layer="177" from-port="2" to-layer="179" to-port="0" />
		<edge from-layer="178" from-port="0" to-layer="179" to-port="1" />
		<edge from-layer="179" from-port="2" to-layer="181" to-port="0" />
		<edge from-layer="180" from-port="0" to-layer="181" to-port="1" />
		<edge from-layer="181" from-port="2" to-layer="183" to-port="0" />
		<edge from-layer="182" from-port="0" to-layer="183" to-port="1" />
		<edge from-layer="183" from-port="2" to-layer="188" to-port="0" />
		<edge from-layer="184" from-port="0" to-layer="188" to-port="1" />
		<edge from-layer="185" from-port="0" to-layer="188" to-port="2" />
		<edge from-layer="186" from-port="0" to-layer="188" to-port="3" />
		<edge from-layer="187" from-port="0" to-layer="188" to-port="4" />
		<edge from-layer="188" from-port="5" to-layer="205" to-port="0" />
		<edge from-layer="189" from-port="0" to-layer="190" to-port="0" />
		<edge from-layer="190" from-port="1" to-layer="192" to-port="0" />
		<edge from-layer="191" from-port="0" to-layer="192" to-port="1" />
		<edge from-layer="192" from-port="2" to-layer="193" to-port="1" />
		<edge from-layer="193" from-port="2" to-layer="195" to-port="0" />
		<edge from-layer="194" from-port="0" to-layer="195" to-port="1" />
		<edge from-layer="195" from-port="2" to-layer="200" to-port="0" />
		<edge from-layer="196" from-port="0" to-layer="200" to-port="1" />
		<edge from-layer="197" from-port="0" to-layer="200" to-port="2" />
		<edge from-layer="198" from-port="0" to-layer="200" to-port="3" />
		<edge from-layer="199" from-port="0" to-layer="200" to-port="4" />
		<edge from-layer="200" from-port="5" to-layer="202" to-port="0" />
		<edge from-layer="201" from-port="0" to-layer="202" to-port="1" />
		<edge from-layer="202" from-port="2" to-layer="204" to-port="0" />
		<edge from-layer="203" from-port="0" to-layer="204" to-port="1" />
		<edge from-layer="204" from-port="2" to-layer="205" to-port="1" />
		<edge from-layer="205" from-port="2" to-layer="206" to-port="0" />
		<edge from-layer="205" from-port="2" to-layer="209" to-port="2" />
		<edge from-layer="206" from-port="1" to-layer="207" to-port="1" />
		<edge from-layer="207" from-port="2" to-layer="209" to-port="0" />
		<edge from-layer="208" from-port="0" to-layer="209" to-port="1" />
		<edge from-layer="209" from-port="3" to-layer="210" to-port="0" />
		<edge from-layer="210" from-port="1" to-layer="222" to-port="0" />
		<edge from-layer="211" from-port="0" to-layer="212" to-port="0" />
		<edge from-layer="212" from-port="1" to-layer="214" to-port="0" />
		<edge from-layer="213" from-port="0" to-layer="214" to-port="1" />
		<edge from-layer="214" from-port="2" to-layer="215" to-port="1" />
		<edge from-layer="215" from-port="2" to-layer="217" to-port="0" />
		<edge from-layer="216" from-port="0" to-layer="217" to-port="1" />
		<edge from-layer="217" from-port="2" to-layer="219" to-port="0" />
		<edge from-layer="218" from-port="0" to-layer="219" to-port="1" />
		<edge from-layer="219" from-port="2" to-layer="221" to-port="0" />
		<edge from-layer="220" from-port="0" to-layer="221" to-port="1" />
		<edge from-layer="221" from-port="2" to-layer="222" to-port="1" />
		<edge from-layer="222" from-port="2" to-layer="224" to-port="0" />
		<edge from-layer="223" from-port="0" to-layer="224" to-port="1" />
		<edge from-layer="224" from-port="2" to-layer="226" to-port="0" />
		<edge from-layer="225" from-port="0" to-layer="226" to-port="1" />
		<edge from-layer="226" from-port="2" to-layer="228" to-port="0" />
		<edge from-layer="227" from-port="0" to-layer="228" to-port="1" />
		<edge from-layer="228" from-port="2" to-layer="233" to-port="0" />
		<edge from-layer="229" from-port="0" to-layer="233" to-port="1" />
		<edge from-layer="230" from-port="0" to-layer="233" to-port="2" />
		<edge from-layer="231" from-port="0" to-layer="233" to-port="3" />
		<edge from-layer="232" from-port="0" to-layer="233" to-port="4" />
		<edge from-layer="233" from-port="5" to-layer="238" to-port="0" />
		<edge from-layer="234" from-port="0" to-layer="235" to-port="0" />
		<edge from-layer="235" from-port="1" to-layer="237" to-port="0" />
		<edge from-layer="236" from-port="0" to-layer="237" to-port="1" />
		<edge from-layer="237" from-port="2" to-layer="238" to-port="1" />
		<edge from-layer="238" from-port="2" to-layer="240" to-port="0" />
		<edge from-layer="239" from-port="0" to-layer="240" to-port="1" />
		<edge from-layer="240" from-port="2" to-layer="241" to-port="0" />
		<edge from-layer="241" from-port="2" to-layer="243" to-port="0" />
		<edge from-layer="242" from-port="0" to-layer="243" to-port="1" />
		<edge from-layer="243" from-port="2" to-layer="245" to-port="0" />
		<edge from-layer="244" from-port="0" to-layer="245" to-port="1" />
		<edge from-layer="245" from-port="2" to-layer="247" to-port="0" />
		<edge from-layer="246" from-port="0" to-layer="247" to-port="1" />
		<edge from-layer="247" from-port="2" to-layer="249" to-port="0" />
		<edge from-layer="247" from-port="2" to-layer="277" to-port="1" />
		<edge from-layer="248" from-port="0" to-layer="249" to-port="1" />
		<edge from-layer="249" from-port="2" to-layer="254" to-port="0" />
		<edge from-layer="250" from-port="0" to-layer="254" to-port="1" />
		<edge from-layer="251" from-port="0" to-layer="254" to-port="2" />
		<edge from-layer="252" from-port="0" to-layer="254" to-port="3" />
		<edge from-layer="253" from-port="0" to-layer="254" to-port="4" />
		<edge from-layer="254" from-port="5" to-layer="259" to-port="0" />
		<edge from-layer="255" from-port="0" to-layer="256" to-port="0" />
		<edge from-layer="256" from-port="1" to-layer="258" to-port="0" />
		<edge from-layer="257" from-port="0" to-layer="258" to-port="1" />
		<edge from-layer="258" from-port="2" to-layer="259" to-port="1" />
		<edge from-layer="259" from-port="2" to-layer="261" to-port="0" />
		<edge from-layer="260" from-port="0" to-layer="261" to-port="1" />
		<edge from-layer="261" from-port="2" to-layer="262" to-port="0" />
		<edge from-layer="262" from-port="1" to-layer="264" to-port="0" />
		<edge from-layer="263" from-port="0" to-layer="264" to-port="1" />
		<edge from-layer="264" from-port="2" to-layer="269" to-port="0" />
		<edge from-layer="265" from-port="0" to-layer="269" to-port="1" />
		<edge from-layer="266" from-port="0" to-layer="269" to-port="2" />
		<edge from-layer="267" from-port="0" to-layer="269" to-port="3" />
		<edge from-layer="268" from-port="0" to-layer="269" to-port="4" />
		<edge from-layer="269" from-port="5" to-layer="274" to-port="0" />
		<edge from-layer="270" from-port="0" to-layer="271" to-port="0" />
		<edge from-layer="271" from-port="1" to-layer="273" to-port="0" />
		<edge from-layer="272" from-port="0" to-layer="273" to-port="1" />
		<edge from-layer="273" from-port="2" to-layer="274" to-port="1" />
		<edge from-layer="274" from-port="2" to-layer="276" to-port="0" />
		<edge from-layer="275" from-port="0" to-layer="276" to-port="1" />
		<edge from-layer="276" from-port="2" to-layer="277" to-port="0" />
		<edge from-layer="277" from-port="2" to-layer="279" to-port="0" />
		<edge from-layer="278" from-port="0" to-layer="279" to-port="1" />
		<edge from-layer="279" from-port="2" to-layer="281" to-port="0" />
		<edge from-layer="280" from-port="0" to-layer="281" to-port="1" />
		<edge from-layer="281" from-port="2" to-layer="283" to-port="0" />
		<edge from-layer="282" from-port="0" to-layer="283" to-port="1" />
		<edge from-layer="283" from-port="2" to-layer="285" to-port="0" />
		<edge from-layer="283" from-port="2" to-layer="361" to-port="1" />
		<edge from-layer="284" from-port="0" to-layer="285" to-port="1" />
		<edge from-layer="285" from-port="2" to-layer="290" to-port="0" />
		<edge from-layer="286" from-port="0" to-layer="290" to-port="1" />
		<edge from-layer="287" from-port="0" to-layer="290" to-port="2" />
		<edge from-layer="288" from-port="0" to-layer="290" to-port="3" />
		<edge from-layer="289" from-port="0" to-layer="290" to-port="4" />
		<edge from-layer="290" from-port="5" to-layer="335" to-port="0" />
		<edge from-layer="290" from-port="5" to-layer="295" to-port="0" />
		<edge from-layer="290" from-port="5" to-layer="313" to-port="0" />
		<edge from-layer="291" from-port="0" to-layer="292" to-port="0" />
		<edge from-layer="292" from-port="1" to-layer="294" to-port="0" />
		<edge from-layer="293" from-port="0" to-layer="294" to-port="1" />
		<edge from-layer="294" from-port="2" to-layer="295" to-port="1" />
		<edge from-layer="295" from-port="2" to-layer="297" to-port="0" />
		<edge from-layer="296" from-port="0" to-layer="297" to-port="1" />
		<edge from-layer="297" from-port="2" to-layer="299" to-port="0" />
		<edge from-layer="298" from-port="0" to-layer="299" to-port="1" />
		<edge from-layer="299" from-port="2" to-layer="301" to-port="0" />
		<edge from-layer="300" from-port="0" to-layer="301" to-port="1" />
		<edge from-layer="301" from-port="2" to-layer="303" to-port="0" />
		<edge from-layer="302" from-port="0" to-layer="303" to-port="1" />
		<edge from-layer="303" from-port="2" to-layer="308" to-port="0" />
		<edge from-layer="304" from-port="0" to-layer="308" to-port="1" />
		<edge from-layer="305" from-port="0" to-layer="308" to-port="2" />
		<edge from-layer="306" from-port="0" to-layer="308" to-port="3" />
		<edge from-layer="307" from-port="0" to-layer="308" to-port="4" />
		<edge from-layer="308" from-port="5" to-layer="325" to-port="0" />
		<edge from-layer="309" from-port="0" to-layer="310" to-port="0" />
		<edge from-layer="310" from-port="1" to-layer="312" to-port="0" />
		<edge from-layer="311" from-port="0" to-layer="312" to-port="1" />
		<edge from-layer="312" from-port="2" to-layer="313" to-port="1" />
		<edge from-layer="313" from-port="2" to-layer="315" to-port="0" />
		<edge from-layer="314" from-port="0" to-layer="315" to-port="1" />
		<edge from-layer="315" from-port="2" to-layer="320" to-port="0" />
		<edge from-layer="316" from-port="0" to-layer="320" to-port="1" />
		<edge from-layer="317" from-port="0" to-layer="320" to-port="2" />
		<edge from-layer="318" from-port="0" to-layer="320" to-port="3" />
		<edge from-layer="319" from-port="0" to-layer="320" to-port="4" />
		<edge from-layer="320" from-port="5" to-layer="322" to-port="0" />
		<edge from-layer="321" from-port="0" to-layer="322" to-port="1" />
		<edge from-layer="322" from-port="2" to-layer="324" to-port="0" />
		<edge from-layer="323" from-port="0" to-layer="324" to-port="1" />
		<edge from-layer="324" from-port="2" to-layer="325" to-port="1" />
		<edge from-layer="325" from-port="2" to-layer="326" to-port="0" />
		<edge from-layer="325" from-port="2" to-layer="329" to-port="2" />
		<edge from-layer="326" from-port="1" to-layer="327" to-port="1" />
		<edge from-layer="327" from-port="2" to-layer="329" to-port="0" />
		<edge from-layer="328" from-port="0" to-layer="329" to-port="1" />
		<edge from-layer="329" from-port="3" to-layer="330" to-port="0" />
		<edge from-layer="330" from-port="1" to-layer="342" to-port="0" />
		<edge from-layer="331" from-port="0" to-layer="332" to-port="0" />
		<edge from-layer="332" from-port="1" to-layer="334" to-port="0" />
		<edge from-layer="333" from-port="0" to-layer="334" to-port="1" />
		<edge from-layer="334" from-port="2" to-layer="335" to-port="1" />
		<edge from-layer="335" from-port="2" to-layer="337" to-port="0" />
		<edge from-layer="336" from-port="0" to-layer="337" to-port="1" />
		<edge from-layer="337" from-port="2" to-layer="339" to-port="0" />
		<edge from-layer="338" from-port="0" to-layer="339" to-port="1" />
		<edge from-layer="339" from-port="2" to-layer="341" to-port="0" />
		<edge from-layer="340" from-port="0" to-layer="341" to-port="1" />
		<edge from-layer="341" from-port="2" to-layer="342" to-port="1" />
		<edge from-layer="342" from-port="2" to-layer="344" to-port="0" />
		<edge from-layer="343" from-port="0" to-layer="344" to-port="1" />
		<edge from-layer="344" from-port="2" to-layer="346" to-port="0" />
		<edge from-layer="345" from-port="0" to-layer="346" to-port="1" />
		<edge from-layer="346" from-port="2" to-layer="348" to-port="0" />
		<edge from-layer="347" from-port="0" to-layer="348" to-port="1" />
		<edge from-layer="348" from-port="2" to-layer="353" to-port="0" />
		<edge from-layer="349" from-port="0" to-layer="353" to-port="1" />
		<edge from-layer="350" from-port="0" to-layer="353" to-port="2" />
		<edge from-layer="351" from-port="0" to-layer="353" to-port="3" />
		<edge from-layer="352" from-port="0" to-layer="353" to-port="4" />
		<edge from-layer="353" from-port="5" to-layer="358" to-port="0" />
		<edge from-layer="354" from-port="0" to-layer="355" to-port="0" />
		<edge from-layer="355" from-port="1" to-layer="357" to-port="0" />
		<edge from-layer="356" from-port="0" to-layer="357" to-port="1" />
		<edge from-layer="357" from-port="2" to-layer="358" to-port="1" />
		<edge from-layer="358" from-port="2" to-layer="360" to-port="0" />
		<edge from-layer="359" from-port="0" to-layer="360" to-port="1" />
		<edge from-layer="360" from-port="2" to-layer="361" to-port="0" />
		<edge from-layer="361" from-port="2" to-layer="363" to-port="0" />
		<edge from-layer="362" from-port="0" to-layer="363" to-port="1" />
		<edge from-layer="363" from-port="2" to-layer="365" to-port="0" />
		<edge from-layer="364" from-port="0" to-layer="365" to-port="1" />
		<edge from-layer="365" from-port="2" to-layer="367" to-port="0" />
		<edge from-layer="366" from-port="0" to-layer="367" to-port="1" />
		<edge from-layer="367" from-port="2" to-layer="369" to-port="0" />
		<edge from-layer="367" from-port="2" to-layer="397" to-port="1" />
		<edge from-layer="368" from-port="0" to-layer="369" to-port="1" />
		<edge from-layer="369" from-port="2" to-layer="374" to-port="0" />
		<edge from-layer="370" from-port="0" to-layer="374" to-port="1" />
		<edge from-layer="371" from-port="0" to-layer="374" to-port="2" />
		<edge from-layer="372" from-port="0" to-layer="374" to-port="3" />
		<edge from-layer="373" from-port="0" to-layer="374" to-port="4" />
		<edge from-layer="374" from-port="5" to-layer="379" to-port="0" />
		<edge from-layer="375" from-port="0" to-layer="376" to-port="0" />
		<edge from-layer="376" from-port="1" to-layer="378" to-port="0" />
		<edge from-layer="377" from-port="0" to-layer="378" to-port="1" />
		<edge from-layer="378" from-port="2" to-layer="379" to-port="1" />
		<edge from-layer="379" from-port="2" to-layer="381" to-port="0" />
		<edge from-layer="380" from-port="0" to-layer="381" to-port="1" />
		<edge from-layer="381" from-port="2" to-layer="382" to-port="0" />
		<edge from-layer="382" from-port="1" to-layer="384" to-port="0" />
		<edge from-layer="383" from-port="0" to-layer="384" to-port="1" />
		<edge from-layer="384" from-port="2" to-layer="389" to-port="0" />
		<edge from-layer="385" from-port="0" to-layer="389" to-port="1" />
		<edge from-layer="386" from-port="0" to-layer="389" to-port="2" />
		<edge from-layer="387" from-port="0" to-layer="389" to-port="3" />
		<edge from-layer="388" from-port="0" to-layer="389" to-port="4" />
		<edge from-layer="389" from-port="5" to-layer="394" to-port="0" />
		<edge from-layer="390" from-port="0" to-layer="391" to-port="0" />
		<edge from-layer="391" from-port="1" to-layer="393" to-port="0" />
		<edge from-layer="392" from-port="0" to-layer="393" to-port="1" />
		<edge from-layer="393" from-port="2" to-layer="394" to-port="1" />
		<edge from-layer="394" from-port="2" to-layer="396" to-port="0" />
		<edge from-layer="395" from-port="0" to-layer="396" to-port="1" />
		<edge from-layer="396" from-port="2" to-layer="397" to-port="0" />
		<edge from-layer="397" from-port="2" to-layer="399" to-port="0" />
		<edge from-layer="398" from-port="0" to-layer="399" to-port="1" />
		<edge from-layer="399" from-port="2" to-layer="401" to-port="0" />
		<edge from-layer="400" from-port="0" to-layer="401" to-port="1" />
		<edge from-layer="401" from-port="2" to-layer="403" to-port="0" />
		<edge from-layer="402" from-port="0" to-layer="403" to-port="1" />
		<edge from-layer="403" from-port="2" to-layer="405" to-port="0" />
		<edge from-layer="403" from-port="2" to-layer="481" to-port="1" />
		<edge from-layer="404" from-port="0" to-layer="405" to-port="1" />
		<edge from-layer="405" from-port="2" to-layer="410" to-port="0" />
		<edge from-layer="406" from-port="0" to-layer="410" to-port="1" />
		<edge from-layer="407" from-port="0" to-layer="410" to-port="2" />
		<edge from-layer="408" from-port="0" to-layer="410" to-port="3" />
		<edge from-layer="409" from-port="0" to-layer="410" to-port="4" />
		<edge from-layer="410" from-port="5" to-layer="415" to-port="0" />
		<edge from-layer="410" from-port="5" to-layer="433" to-port="0" />
		<edge from-layer="410" from-port="5" to-layer="455" to-port="0" />
		<edge from-layer="411" from-port="0" to-layer="412" to-port="0" />
		<edge from-layer="412" from-port="1" to-layer="414" to-port="0" />
		<edge from-layer="413" from-port="0" to-layer="414" to-port="1" />
		<edge from-layer="414" from-port="2" to-layer="415" to-port="1" />
		<edge from-layer="415" from-port="2" to-layer="417" to-port="0" />
		<edge from-layer="416" from-port="0" to-layer="417" to-port="1" />
		<edge from-layer="417" from-port="2" to-layer="419" to-port="0" />
		<edge from-layer="418" from-port="0" to-layer="419" to-port="1" />
		<edge from-layer="419" from-port="2" to-layer="421" to-port="0" />
		<edge from-layer="420" from-port="0" to-layer="421" to-port="1" />
		<edge from-layer="421" from-port="2" to-layer="423" to-port="0" />
		<edge from-layer="422" from-port="0" to-layer="423" to-port="1" />
		<edge from-layer="423" from-port="2" to-layer="428" to-port="0" />
		<edge from-layer="424" from-port="0" to-layer="428" to-port="1" />
		<edge from-layer="425" from-port="0" to-layer="428" to-port="2" />
		<edge from-layer="426" from-port="0" to-layer="428" to-port="3" />
		<edge from-layer="427" from-port="0" to-layer="428" to-port="4" />
		<edge from-layer="428" from-port="5" to-layer="445" to-port="0" />
		<edge from-layer="429" from-port="0" to-layer="430" to-port="0" />
		<edge from-layer="430" from-port="1" to-layer="432" to-port="0" />
		<edge from-layer="431" from-port="0" to-layer="432" to-port="1" />
		<edge from-layer="432" from-port="2" to-layer="433" to-port="1" />
		<edge from-layer="433" from-port="2" to-layer="435" to-port="0" />
		<edge from-layer="434" from-port="0" to-layer="435" to-port="1" />
		<edge from-layer="435" from-port="2" to-layer="440" to-port="0" />
		<edge from-layer="436" from-port="0" to-layer="440" to-port="1" />
		<edge from-layer="437" from-port="0" to-layer="440" to-port="2" />
		<edge from-layer="438" from-port="0" to-layer="440" to-port="3" />
		<edge from-layer="439" from-port="0" to-layer="440" to-port="4" />
		<edge from-layer="440" from-port="5" to-layer="442" to-port="0" />
		<edge from-layer="441" from-port="0" to-layer="442" to-port="1" />
		<edge from-layer="442" from-port="2" to-layer="444" to-port="0" />
		<edge from-layer="443" from-port="0" to-layer="444" to-port="1" />
		<edge from-layer="444" from-port="2" to-layer="445" to-port="1" />
		<edge from-layer="445" from-port="2" to-layer="446" to-port="0" />
		<edge from-layer="445" from-port="2" to-layer="449" to-port="2" />
		<edge from-layer="446" from-port="1" to-layer="447" to-port="1" />
		<edge from-layer="447" from-port="2" to-layer="449" to-port="0" />
		<edge from-layer="448" from-port="0" to-layer="449" to-port="1" />
		<edge from-layer="449" from-port="3" to-layer="450" to-port="0" />
		<edge from-layer="450" from-port="1" to-layer="462" to-port="0" />
		<edge from-layer="451" from-port="0" to-layer="452" to-port="0" />
		<edge from-layer="452" from-port="1" to-layer="454" to-port="0" />
		<edge from-layer="453" from-port="0" to-layer="454" to-port="1" />
		<edge from-layer="454" from-port="2" to-layer="455" to-port="1" />
		<edge from-layer="455" from-port="2" to-layer="457" to-port="0" />
		<edge from-layer="456" from-port="0" to-layer="457" to-port="1" />
		<edge from-layer="457" from-port="2" to-layer="459" to-port="0" />
		<edge from-layer="458" from-port="0" to-layer="459" to-port="1" />
		<edge from-layer="459" from-port="2" to-layer="461" to-port="0" />
		<edge from-layer="460" from-port="0" to-layer="461" to-port="1" />
		<edge from-layer="461" from-port="2" to-layer="462" to-port="1" />
		<edge from-layer="462" from-port="2" to-layer="464" to-port="0" />
		<edge from-layer="463" from-port="0" to-layer="464" to-port="1" />
		<edge from-layer="464" from-port="2" to-layer="466" to-port="0" />
		<edge from-layer="465" from-port="0" to-layer="466" to-port="1" />
		<edge from-layer="466" from-port="2" to-layer="468" to-port="0" />
		<edge from-layer="467" from-port="0" to-layer="468" to-port="1" />
		<edge from-layer="468" from-port="2" to-layer="473" to-port="0" />
		<edge from-layer="469" from-port="0" to-layer="473" to-port="1" />
		<edge from-layer="470" from-port="0" to-layer="473" to-port="2" />
		<edge from-layer="471" from-port="0" to-layer="473" to-port="3" />
		<edge from-layer="472" from-port="0" to-layer="473" to-port="4" />
		<edge from-layer="473" from-port="5" to-layer="478" to-port="0" />
		<edge from-layer="474" from-port="0" to-layer="475" to-port="0" />
		<edge from-layer="475" from-port="1" to-layer="477" to-port="0" />
		<edge from-layer="476" from-port="0" to-layer="477" to-port="1" />
		<edge from-layer="477" from-port="2" to-layer="478" to-port="1" />
		<edge from-layer="478" from-port="2" to-layer="480" to-port="0" />
		<edge from-layer="479" from-port="0" to-layer="480" to-port="1" />
		<edge from-layer="480" from-port="2" to-layer="481" to-port="0" />
		<edge from-layer="481" from-port="2" to-layer="483" to-port="0" />
		<edge from-layer="482" from-port="0" to-layer="483" to-port="1" />
		<edge from-layer="483" from-port="2" to-layer="485" to-port="0" />
		<edge from-layer="484" from-port="0" to-layer="485" to-port="1" />
		<edge from-layer="485" from-port="2" to-layer="487" to-port="0" />
		<edge from-layer="486" from-port="0" to-layer="487" to-port="1" />
		<edge from-layer="487" from-port="2" to-layer="489" to-port="0" />
		<edge from-layer="487" from-port="2" to-layer="517" to-port="1" />
		<edge from-layer="488" from-port="0" to-layer="489" to-port="1" />
		<edge from-layer="489" from-port="2" to-layer="494" to-port="0" />
		<edge from-layer="490" from-port="0" to-layer="494" to-port="1" />
		<edge from-layer="491" from-port="0" to-layer="494" to-port="2" />
		<edge from-layer="492" from-port="0" to-layer="494" to-port="3" />
		<edge from-layer="493" from-port="0" to-layer="494" to-port="4" />
		<edge from-layer="494" from-port="5" to-layer="499" to-port="0" />
		<edge from-layer="495" from-port="0" to-layer="496" to-port="0" />
		<edge from-layer="496" from-port="1" to-layer="498" to-port="0" />
		<edge from-layer="497" from-port="0" to-layer="498" to-port="1" />
		<edge from-layer="498" from-port="2" to-layer="499" to-port="1" />
		<edge from-layer="499" from-port="2" to-layer="501" to-port="0" />
		<edge from-layer="500" from-port="0" to-layer="501" to-port="1" />
		<edge from-layer="501" from-port="2" to-layer="502" to-port="0" />
		<edge from-layer="502" from-port="1" to-layer="504" to-port="0" />
		<edge from-layer="503" from-port="0" to-layer="504" to-port="1" />
		<edge from-layer="504" from-port="2" to-layer="509" to-port="0" />
		<edge from-layer="505" from-port="0" to-layer="509" to-port="1" />
		<edge from-layer="506" from-port="0" to-layer="509" to-port="2" />
		<edge from-layer="507" from-port="0" to-layer="509" to-port="3" />
		<edge from-layer="508" from-port="0" to-layer="509" to-port="4" />
		<edge from-layer="509" from-port="5" to-layer="514" to-port="0" />
		<edge from-layer="510" from-port="0" to-layer="511" to-port="0" />
		<edge from-layer="511" from-port="1" to-layer="513" to-port="0" />
		<edge from-layer="512" from-port="0" to-layer="513" to-port="1" />
		<edge from-layer="513" from-port="2" to-layer="514" to-port="1" />
		<edge from-layer="514" from-port="2" to-layer="516" to-port="0" />
		<edge from-layer="515" from-port="0" to-layer="516" to-port="1" />
		<edge from-layer="516" from-port="2" to-layer="517" to-port="0" />
		<edge from-layer="517" from-port="2" to-layer="519" to-port="0" />
		<edge from-layer="518" from-port="0" to-layer="519" to-port="1" />
		<edge from-layer="519" from-port="2" to-layer="521" to-port="0" />
		<edge from-layer="520" from-port="0" to-layer="521" to-port="1" />
		<edge from-layer="521" from-port="2" to-layer="523" to-port="0" />
		<edge from-layer="522" from-port="0" to-layer="523" to-port="1" />
		<edge from-layer="523" from-port="2" to-layer="525" to-port="0" />
		<edge from-layer="523" from-port="2" to-layer="601" to-port="1" />
		<edge from-layer="524" from-port="0" to-layer="525" to-port="1" />
		<edge from-layer="525" from-port="2" to-layer="530" to-port="0" />
		<edge from-layer="526" from-port="0" to-layer="530" to-port="1" />
		<edge from-layer="527" from-port="0" to-layer="530" to-port="2" />
		<edge from-layer="528" from-port="0" to-layer="530" to-port="3" />
		<edge from-layer="529" from-port="0" to-layer="530" to-port="4" />
		<edge from-layer="530" from-port="5" to-layer="535" to-port="0" />
		<edge from-layer="530" from-port="5" to-layer="553" to-port="0" />
		<edge from-layer="530" from-port="5" to-layer="575" to-port="0" />
		<edge from-layer="531" from-port="0" to-layer="532" to-port="0" />
		<edge from-layer="532" from-port="1" to-layer="534" to-port="0" />
		<edge from-layer="533" from-port="0" to-layer="534" to-port="1" />
		<edge from-layer="534" from-port="2" to-layer="535" to-port="1" />
		<edge from-layer="535" from-port="2" to-layer="537" to-port="0" />
		<edge from-layer="536" from-port="0" to-layer="537" to-port="1" />
		<edge from-layer="537" from-port="2" to-layer="539" to-port="0" />
		<edge from-layer="538" from-port="0" to-layer="539" to-port="1" />
		<edge from-layer="539" from-port="2" to-layer="541" to-port="0" />
		<edge from-layer="540" from-port="0" to-layer="541" to-port="1" />
		<edge from-layer="541" from-port="2" to-layer="543" to-port="0" />
		<edge from-layer="542" from-port="0" to-layer="543" to-port="1" />
		<edge from-layer="543" from-port="2" to-layer="548" to-port="0" />
		<edge from-layer="544" from-port="0" to-layer="548" to-port="1" />
		<edge from-layer="545" from-port="0" to-layer="548" to-port="2" />
		<edge from-layer="546" from-port="0" to-layer="548" to-port="3" />
		<edge from-layer="547" from-port="0" to-layer="548" to-port="4" />
		<edge from-layer="548" from-port="5" to-layer="565" to-port="0" />
		<edge from-layer="549" from-port="0" to-layer="550" to-port="0" />
		<edge from-layer="550" from-port="1" to-layer="552" to-port="0" />
		<edge from-layer="551" from-port="0" to-layer="552" to-port="1" />
		<edge from-layer="552" from-port="2" to-layer="553" to-port="1" />
		<edge from-layer="553" from-port="2" to-layer="555" to-port="0" />
		<edge from-layer="554" from-port="0" to-layer="555" to-port="1" />
		<edge from-layer="555" from-port="2" to-layer="560" to-port="0" />
		<edge from-layer="556" from-port="0" to-layer="560" to-port="1" />
		<edge from-layer="557" from-port="0" to-layer="560" to-port="2" />
		<edge from-layer="558" from-port="0" to-layer="560" to-port="3" />
		<edge from-layer="559" from-port="0" to-layer="560" to-port="4" />
		<edge from-layer="560" from-port="5" to-layer="562" to-port="0" />
		<edge from-layer="561" from-port="0" to-layer="562" to-port="1" />
		<edge from-layer="562" from-port="2" to-layer="564" to-port="0" />
		<edge from-layer="563" from-port="0" to-layer="564" to-port="1" />
		<edge from-layer="564" from-port="2" to-layer="565" to-port="1" />
		<edge from-layer="565" from-port="2" to-layer="566" to-port="0" />
		<edge from-layer="565" from-port="2" to-layer="569" to-port="2" />
		<edge from-layer="566" from-port="1" to-layer="567" to-port="1" />
		<edge from-layer="567" from-port="2" to-layer="569" to-port="0" />
		<edge from-layer="568" from-port="0" to-layer="569" to-port="1" />
		<edge from-layer="569" from-port="3" to-layer="570" to-port="0" />
		<edge from-layer="570" from-port="1" to-layer="582" to-port="0" />
		<edge from-layer="571" from-port="0" to-layer="572" to-port="0" />
		<edge from-layer="572" from-port="1" to-layer="574" to-port="0" />
		<edge from-layer="573" from-port="0" to-layer="574" to-port="1" />
		<edge from-layer="574" from-port="2" to-layer="575" to-port="1" />
		<edge from-layer="575" from-port="2" to-layer="577" to-port="0" />
		<edge from-layer="576" from-port="0" to-layer="577" to-port="1" />
		<edge from-layer="577" from-port="2" to-layer="579" to-port="0" />
		<edge from-layer="578" from-port="0" to-layer="579" to-port="1" />
		<edge from-layer="579" from-port="2" to-layer="581" to-port="0" />
		<edge from-layer="580" from-port="0" to-layer="581" to-port="1" />
		<edge from-layer="581" from-port="2" to-layer="582" to-port="1" />
		<edge from-layer="582" from-port="2" to-layer="584" to-port="0" />
		<edge from-layer="583" from-port="0" to-layer="584" to-port="1" />
		<edge from-layer="584" from-port="2" to-layer="586" to-port="0" />
		<edge from-layer="585" from-port="0" to-layer="586" to-port="1" />
		<edge from-layer="586" from-port="2" to-layer="588" to-port="0" />
		<edge from-layer="587" from-port="0" to-layer="588" to-port="1" />
		<edge from-layer="588" from-port="2" to-layer="593" to-port="0" />
		<edge from-layer="589" from-port="0" to-layer="593" to-port="1" />
		<edge from-layer="590" from-port="0" to-layer="593" to-port="2" />
		<edge from-layer="591" from-port="0" to-layer="593" to-port="3" />
		<edge from-layer="592" from-port="0" to-layer="593" to-port="4" />
		<edge from-layer="593" from-port="5" to-layer="598" to-port="0" />
		<edge from-layer="594" from-port="0" to-layer="595" to-port="0" />
		<edge from-layer="595" from-port="1" to-layer="597" to-port="0" />
		<edge from-layer="596" from-port="0" to-layer="597" to-port="1" />
		<edge from-layer="597" from-port="2" to-layer="598" to-port="1" />
		<edge from-layer="598" from-port="2" to-layer="600" to-port="0" />
		<edge from-layer="599" from-port="0" to-layer="600" to-port="1" />
		<edge from-layer="600" from-port="2" to-layer="601" to-port="0" />
		<edge from-layer="601" from-port="2" to-layer="603" to-port="0" />
		<edge from-layer="602" from-port="0" to-layer="603" to-port="1" />
		<edge from-layer="603" from-port="2" to-layer="605" to-port="0" />
		<edge from-layer="604" from-port="0" to-layer="605" to-port="1" />
		<edge from-layer="605" from-port="2" to-layer="607" to-port="0" />
		<edge from-layer="606" from-port="0" to-layer="607" to-port="1" />
		<edge from-layer="607" from-port="2" to-layer="609" to-port="0" />
		<edge from-layer="607" from-port="2" to-layer="637" to-port="1" />
		<edge from-layer="608" from-port="0" to-layer="609" to-port="1" />
		<edge from-layer="609" from-port="2" to-layer="614" to-port="0" />
		<edge from-layer="610" from-port="0" to-layer="614" to-port="1" />
		<edge from-layer="611" from-port="0" to-layer="614" to-port="2" />
		<edge from-layer="612" from-port="0" to-layer="614" to-port="3" />
		<edge from-layer="613" from-port="0" to-layer="614" to-port="4" />
		<edge from-layer="614" from-port="5" to-layer="619" to-port="0" />
		<edge from-layer="615" from-port="0" to-layer="616" to-port="0" />
		<edge from-layer="616" from-port="1" to-layer="618" to-port="0" />
		<edge from-layer="617" from-port="0" to-layer="618" to-port="1" />
		<edge from-layer="618" from-port="2" to-layer="619" to-port="1" />
		<edge from-layer="619" from-port="2" to-layer="621" to-port="0" />
		<edge from-layer="620" from-port="0" to-layer="621" to-port="1" />
		<edge from-layer="621" from-port="2" to-layer="622" to-port="0" />
		<edge from-layer="622" from-port="1" to-layer="624" to-port="0" />
		<edge from-layer="623" from-port="0" to-layer="624" to-port="1" />
		<edge from-layer="624" from-port="2" to-layer="629" to-port="0" />
		<edge from-layer="625" from-port="0" to-layer="629" to-port="1" />
		<edge from-layer="626" from-port="0" to-layer="629" to-port="2" />
		<edge from-layer="627" from-port="0" to-layer="629" to-port="3" />
		<edge from-layer="628" from-port="0" to-layer="629" to-port="4" />
		<edge from-layer="629" from-port="5" to-layer="634" to-port="0" />
		<edge from-layer="630" from-port="0" to-layer="631" to-port="0" />
		<edge from-layer="631" from-port="1" to-layer="633" to-port="0" />
		<edge from-layer="632" from-port="0" to-layer="633" to-port="1" />
		<edge from-layer="633" from-port="2" to-layer="634" to-port="1" />
		<edge from-layer="634" from-port="2" to-layer="636" to-port="0" />
		<edge from-layer="635" from-port="0" to-layer="636" to-port="1" />
		<edge from-layer="636" from-port="2" to-layer="637" to-port="0" />
		<edge from-layer="637" from-port="2" to-layer="639" to-port="0" />
		<edge from-layer="638" from-port="0" to-layer="639" to-port="1" />
		<edge from-layer="639" from-port="2" to-layer="641" to-port="0" />
		<edge from-layer="640" from-port="0" to-layer="641" to-port="1" />
		<edge from-layer="641" from-port="2" to-layer="643" to-port="0" />
		<edge from-layer="642" from-port="0" to-layer="643" to-port="1" />
		<edge from-layer="643" from-port="2" to-layer="645" to-port="0" />
		<edge from-layer="643" from-port="2" to-layer="721" to-port="1" />
		<edge from-layer="644" from-port="0" to-layer="645" to-port="1" />
		<edge from-layer="645" from-port="2" to-layer="650" to-port="0" />
		<edge from-layer="646" from-port="0" to-layer="650" to-port="1" />
		<edge from-layer="647" from-port="0" to-layer="650" to-port="2" />
		<edge from-layer="648" from-port="0" to-layer="650" to-port="3" />
		<edge from-layer="649" from-port="0" to-layer="650" to-port="4" />
		<edge from-layer="650" from-port="5" to-layer="655" to-port="0" />
		<edge from-layer="650" from-port="5" to-layer="695" to-port="0" />
		<edge from-layer="650" from-port="5" to-layer="673" to-port="0" />
		<edge from-layer="651" from-port="0" to-layer="652" to-port="0" />
		<edge from-layer="652" from-port="1" to-layer="654" to-port="0" />
		<edge from-layer="653" from-port="0" to-layer="654" to-port="1" />
		<edge from-layer="654" from-port="2" to-layer="655" to-port="1" />
		<edge from-layer="655" from-port="2" to-layer="657" to-port="0" />
		<edge from-layer="656" from-port="0" to-layer="657" to-port="1" />
		<edge from-layer="657" from-port="2" to-layer="659" to-port="0" />
		<edge from-layer="658" from-port="0" to-layer="659" to-port="1" />
		<edge from-layer="659" from-port="2" to-layer="661" to-port="0" />
		<edge from-layer="660" from-port="0" to-layer="661" to-port="1" />
		<edge from-layer="661" from-port="2" to-layer="663" to-port="0" />
		<edge from-layer="662" from-port="0" to-layer="663" to-port="1" />
		<edge from-layer="663" from-port="2" to-layer="668" to-port="0" />
		<edge from-layer="664" from-port="0" to-layer="668" to-port="1" />
		<edge from-layer="665" from-port="0" to-layer="668" to-port="2" />
		<edge from-layer="666" from-port="0" to-layer="668" to-port="3" />
		<edge from-layer="667" from-port="0" to-layer="668" to-port="4" />
		<edge from-layer="668" from-port="5" to-layer="685" to-port="0" />
		<edge from-layer="669" from-port="0" to-layer="670" to-port="0" />
		<edge from-layer="670" from-port="1" to-layer="672" to-port="0" />
		<edge from-layer="671" from-port="0" to-layer="672" to-port="1" />
		<edge from-layer="672" from-port="2" to-layer="673" to-port="1" />
		<edge from-layer="673" from-port="2" to-layer="675" to-port="0" />
		<edge from-layer="674" from-port="0" to-layer="675" to-port="1" />
		<edge from-layer="675" from-port="2" to-layer="680" to-port="0" />
		<edge from-layer="676" from-port="0" to-layer="680" to-port="1" />
		<edge from-layer="677" from-port="0" to-layer="680" to-port="2" />
		<edge from-layer="678" from-port="0" to-layer="680" to-port="3" />
		<edge from-layer="679" from-port="0" to-layer="680" to-port="4" />
		<edge from-layer="680" from-port="5" to-layer="682" to-port="0" />
		<edge from-layer="681" from-port="0" to-layer="682" to-port="1" />
		<edge from-layer="682" from-port="2" to-layer="684" to-port="0" />
		<edge from-layer="683" from-port="0" to-layer="684" to-port="1" />
		<edge from-layer="684" from-port="2" to-layer="685" to-port="1" />
		<edge from-layer="685" from-port="2" to-layer="686" to-port="0" />
		<edge from-layer="685" from-port="2" to-layer="689" to-port="2" />
		<edge from-layer="686" from-port="1" to-layer="687" to-port="1" />
		<edge from-layer="687" from-port="2" to-layer="689" to-port="0" />
		<edge from-layer="688" from-port="0" to-layer="689" to-port="1" />
		<edge from-layer="689" from-port="3" to-layer="690" to-port="0" />
		<edge from-layer="690" from-port="1" to-layer="702" to-port="0" />
		<edge from-layer="691" from-port="0" to-layer="692" to-port="0" />
		<edge from-layer="692" from-port="1" to-layer="694" to-port="0" />
		<edge from-layer="693" from-port="0" to-layer="694" to-port="1" />
		<edge from-layer="694" from-port="2" to-layer="695" to-port="1" />
		<edge from-layer="695" from-port="2" to-layer="697" to-port="0" />
		<edge from-layer="696" from-port="0" to-layer="697" to-port="1" />
		<edge from-layer="697" from-port="2" to-layer="699" to-port="0" />
		<edge from-layer="698" from-port="0" to-layer="699" to-port="1" />
		<edge from-layer="699" from-port="2" to-layer="701" to-port="0" />
		<edge from-layer="700" from-port="0" to-layer="701" to-port="1" />
		<edge from-layer="701" from-port="2" to-layer="702" to-port="1" />
		<edge from-layer="702" from-port="2" to-layer="704" to-port="0" />
		<edge from-layer="703" from-port="0" to-layer="704" to-port="1" />
		<edge from-layer="704" from-port="2" to-layer="706" to-port="0" />
		<edge from-layer="705" from-port="0" to-layer="706" to-port="1" />
		<edge from-layer="706" from-port="2" to-layer="708" to-port="0" />
		<edge from-layer="707" from-port="0" to-layer="708" to-port="1" />
		<edge from-layer="708" from-port="2" to-layer="713" to-port="0" />
		<edge from-layer="709" from-port="0" to-layer="713" to-port="1" />
		<edge from-layer="710" from-port="0" to-layer="713" to-port="2" />
		<edge from-layer="711" from-port="0" to-layer="713" to-port="3" />
		<edge from-layer="712" from-port="0" to-layer="713" to-port="4" />
		<edge from-layer="713" from-port="5" to-layer="718" to-port="0" />
		<edge from-layer="714" from-port="0" to-layer="715" to-port="0" />
		<edge from-layer="715" from-port="1" to-layer="717" to-port="0" />
		<edge from-layer="716" from-port="0" to-layer="717" to-port="1" />
		<edge from-layer="717" from-port="2" to-layer="718" to-port="1" />
		<edge from-layer="718" from-port="2" to-layer="720" to-port="0" />
		<edge from-layer="719" from-port="0" to-layer="720" to-port="1" />
		<edge from-layer="720" from-port="2" to-layer="721" to-port="0" />
		<edge from-layer="721" from-port="2" to-layer="723" to-port="0" />
		<edge from-layer="722" from-port="0" to-layer="723" to-port="1" />
		<edge from-layer="723" from-port="2" to-layer="725" to-port="0" />
		<edge from-layer="724" from-port="0" to-layer="725" to-port="1" />
		<edge from-layer="725" from-port="2" to-layer="727" to-port="0" />
		<edge from-layer="726" from-port="0" to-layer="727" to-port="1" />
		<edge from-layer="727" from-port="2" to-layer="729" to-port="0" />
		<edge from-layer="727" from-port="2" to-layer="757" to-port="1" />
		<edge from-layer="728" from-port="0" to-layer="729" to-port="1" />
		<edge from-layer="729" from-port="2" to-layer="734" to-port="0" />
		<edge from-layer="730" from-port="0" to-layer="734" to-port="1" />
		<edge from-layer="731" from-port="0" to-layer="734" to-port="2" />
		<edge from-layer="732" from-port="0" to-layer="734" to-port="3" />
		<edge from-layer="733" from-port="0" to-layer="734" to-port="4" />
		<edge from-layer="734" from-port="5" to-layer="739" to-port="0" />
		<edge from-layer="735" from-port="0" to-layer="736" to-port="0" />
		<edge from-layer="736" from-port="1" to-layer="738" to-port="0" />
		<edge from-layer="737" from-port="0" to-layer="738" to-port="1" />
		<edge from-layer="738" from-port="2" to-layer="739" to-port="1" />
		<edge from-layer="739" from-port="2" to-layer="741" to-port="0" />
		<edge from-layer="740" from-port="0" to-layer="741" to-port="1" />
		<edge from-layer="741" from-port="2" to-layer="742" to-port="0" />
		<edge from-layer="742" from-port="1" to-layer="744" to-port="0" />
		<edge from-layer="743" from-port="0" to-layer="744" to-port="1" />
		<edge from-layer="744" from-port="2" to-layer="749" to-port="0" />
		<edge from-layer="745" from-port="0" to-layer="749" to-port="1" />
		<edge from-layer="746" from-port="0" to-layer="749" to-port="2" />
		<edge from-layer="747" from-port="0" to-layer="749" to-port="3" />
		<edge from-layer="748" from-port="0" to-layer="749" to-port="4" />
		<edge from-layer="749" from-port="5" to-layer="754" to-port="0" />
		<edge from-layer="750" from-port="0" to-layer="751" to-port="0" />
		<edge from-layer="751" from-port="1" to-layer="753" to-port="0" />
		<edge from-layer="752" from-port="0" to-layer="753" to-port="1" />
		<edge from-layer="753" from-port="2" to-layer="754" to-port="1" />
		<edge from-layer="754" from-port="2" to-layer="756" to-port="0" />
		<edge from-layer="755" from-port="0" to-layer="756" to-port="1" />
		<edge from-layer="756" from-port="2" to-layer="757" to-port="0" />
		<edge from-layer="757" from-port="2" to-layer="759" to-port="0" />
		<edge from-layer="758" from-port="0" to-layer="759" to-port="1" />
		<edge from-layer="759" from-port="2" to-layer="761" to-port="0" />
		<edge from-layer="760" from-port="0" to-layer="761" to-port="1" />
		<edge from-layer="761" from-port="2" to-layer="763" to-port="0" />
		<edge from-layer="762" from-port="0" to-layer="763" to-port="1" />
		<edge from-layer="763" from-port="2" to-layer="764" to-port="0" />
	</edges>
	<rt_info>
		<Runtime_version value="2024.4.1-16618-643f23d1318-releases/2024/4" />
		<conversion_parameters>
			<framework value="pytorch" />
			<is_python_object value="True" />
		</conversion_parameters>
		<nncf>
			<friendly_names_were_updated value="True" />
			<quantization>
				<advanced_parameters value="{'overflow_fix': 'disable', 'quantize_outputs': False, 'inplace_statistics': True, 'disable_channel_alignment': True, 'disable_bias_correction': False, 'batchwise_statistics': None, 'activations_quantization_params': None, 'weights_quantization_params': None, 'activations_range_estimator_params': {'min': {'statistics_type': None, 'aggregator_type': None, 'clipping_value': None, 'quantile_outlier_prob': 0.0001}, 'max': {'statistics_type': None, 'aggregator_type': None, 'clipping_value': None, 'quantile_outlier_prob': 0.0001}}, 'weights_range_estimator_params': {'min': {'statistics_type': None, 'aggregator_type': None, 'clipping_value': None, 'quantile_outlier_prob': 0.0001}, 'max': {'statistics_type': None, 'aggregator_type': None, 'clipping_value': None, 'quantile_outlier_prob': 0.0001}}, 'bias_correction_params': {'apply_for_all_nodes': False, 'threshold': None}, 'smooth_quant_alphas': {'convolution': -1, 'matmul': 0.95}, 'smooth_quant_alpha': None, 'backend_params': {}}" />
				<fast_bias_correction value="True" />
				<ignored_scope>
					<types value="['GroupNormalization']" />
				</ignored_scope>
				<model_type value="transformer" />
				<preset value="mixed" />
				<subset_size value="300" />
				<target_device value="ANY" />
			</quantization>
		</nncf>
		<optimum>
			<optimum_intel_version value="1.20.0.dev0+b31524c" />
			<optimum_version value="1.23.0" />
			<pytorch_version value="2.5.0.dev20240807+cu121" />
			<transformers_version value="4.43.4" />
		</optimum>
	</rt_info>
</net>
