#!/usr/bin/env python3
"""
RAG检索系统性能评估框架
评估准确率和响应速度
"""

import os
import sys
import time
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Any, Tu<PERSON>
from dataclasses import dataclass
from sentence_transformers import SentenceTransformer
import torch
from sklearn.metrics.pairwise import cosine_similarity
import re

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class EvaluationResult:
    """评估结果数据类"""
    query: str
    expected_answer: str
    retrieved_answer: str
    response_time: float
    bleu_score: float
    rouge_score: float
    semantic_similarity: float
    retrieval_method: str

class RAGEvaluationFramework:
    """RAG评估框架"""
    
    def __init__(self, working_dir: str = "./evaluation_results"):
        self.working_dir = working_dir
        os.makedirs(working_dir, exist_ok=True)
        
        # 初始化评估模型
        self.evaluation_model = None
        self._load_evaluation_model()
        
        # 存储结果
        self.results = []
        
    def _load_evaluation_model(self):
        """加载用于语义相似度评估的模型"""
        try:
            # 使用本地BGE模型进行语义相似度评估
            model_path = './bge-large-zh-v1.5'
            self.evaluation_model = SentenceTransformer(model_path, trust_remote_code=True)
            print(f"✓ 评估模型加载成功: {model_path}")
        except Exception as e:
            print(f"⚠ 评估模型加载失败: {e}")
            self.evaluation_model = None
    
    def calculate_bleu_score(self, reference: str, candidate: str) -> float:
        """计算BLEU分数（简化版本）"""
        try:
            # 简化的BLEU计算，基于n-gram重叠
            ref_words = set(reference.lower().split())
            cand_words = set(candidate.lower().split())
            
            if len(cand_words) == 0:
                return 0.0
            
            # 计算1-gram精确度
            overlap = len(ref_words.intersection(cand_words))
            precision = overlap / len(cand_words)
            
            # 计算简化的BLEU分数
            bleu = precision * min(1.0, len(candidate.split()) / len(reference.split()))
            return bleu
            
        except Exception as e:
            print(f"BLEU计算错误: {e}")
            return 0.0
    
    def calculate_rouge_score(self, reference: str, candidate: str) -> float:
        """计算ROUGE分数（简化版本）"""
        try:
            ref_words = reference.lower().split()
            cand_words = candidate.lower().split()
            
            if len(ref_words) == 0:
                return 0.0
            
            # ROUGE-1 召回率
            ref_set = set(ref_words)
            cand_set = set(cand_words)
            
            overlap = len(ref_set.intersection(cand_set))
            rouge = overlap / len(ref_set)
            
            return rouge
            
        except Exception as e:
            print(f"ROUGE计算错误: {e}")
            return 0.0
    
    def calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """计算语义相似度"""
        try:
            if self.evaluation_model is None:
                return 0.0
            
            embeddings = self.evaluation_model.encode([text1, text2])
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
            return float(similarity)
            
        except Exception as e:
            print(f"语义相似度计算错误: {e}")
            return 0.0
    
    def evaluate_single_query(self, 
                            query: str, 
                            expected_answer: str, 
                            retrieval_system, 
                            method_name: str) -> EvaluationResult:
        """评估单个查询"""
        print(f"评估查询: {query[:50]}...")
        
        # 测量响应时间
        start_time = time.time()
        try:
            retrieved_answer = retrieval_system.find_top_k(query)
        except Exception as e:
            print(f"检索失败: {e}")
            retrieved_answer = "检索失败"
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # 计算评估指标
        bleu_score = self.calculate_bleu_score(expected_answer, retrieved_answer)
        rouge_score = self.calculate_rouge_score(expected_answer, retrieved_answer)
        semantic_similarity = self.calculate_semantic_similarity(expected_answer, retrieved_answer)
        
        result = EvaluationResult(
            query=query,
            expected_answer=expected_answer,
            retrieved_answer=retrieved_answer,
            response_time=response_time,
            bleu_score=bleu_score,
            rouge_score=rouge_score,
            semantic_similarity=semantic_similarity,
            retrieval_method=method_name
        )
        
        self.results.append(result)
        return result
    
    def run_batch_evaluation(self, 
                           test_dataset: List[Dict], 
                           retrieval_systems: Dict) -> List[EvaluationResult]:
        """批量评估"""
        print(f"开始批量评估，共 {len(test_dataset)} 个测试用例")
        
        all_results = []
        
        for i, test_case in enumerate(test_dataset, 1):
            print(f"\n进度: {i}/{len(test_dataset)}")
            query = test_case['query']
            expected_answer = test_case['expected_answer']
            
            # 对每个检索系统进行测试
            for method_name, retrieval_system in retrieval_systems.items():
                try:
                    result = self.evaluate_single_query(
                        query, expected_answer, retrieval_system, method_name
                    )
                    all_results.append(result)
                    
                    print(f"  {method_name}: 响应时间={result.response_time:.2f}s, "
                          f"语义相似度={result.semantic_similarity:.3f}")
                          
                except Exception as e:
                    print(f"  {method_name} 评估失败: {e}")
        
        return all_results
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        if not self.results:
            return {"error": "没有评估结果"}
        
        # 按检索方法分组
        methods = {}
        for result in self.results:
            method = result.retrieval_method
            if method not in methods:
                methods[method] = []
            methods[method].append(result)
        
        report = {}
        
        for method_name, results in methods.items():
            # 计算统计指标
            response_times = [r.response_time for r in results]
            bleu_scores = [r.bleu_score for r in results]
            rouge_scores = [r.rouge_score for r in results]
            semantic_similarities = [r.semantic_similarity for r in results]
            
            method_report = {
                "样本数量": len(results),
                "响应时间": {
                    "平均值": np.mean(response_times),
                    "中位数": np.median(response_times),
                    "标准差": np.std(response_times),
                    "最小值": np.min(response_times),
                    "最大值": np.max(response_times)
                },
                "BLEU分数": {
                    "平均值": np.mean(bleu_scores),
                    "中位数": np.median(bleu_scores),
                    "标准差": np.std(bleu_scores)
                },
                "ROUGE分数": {
                    "平均值": np.mean(rouge_scores),
                    "中位数": np.median(rouge_scores),
                    "标准差": np.std(rouge_scores)
                },
                "语义相似度": {
                    "平均值": np.mean(semantic_similarities),
                    "中位数": np.median(semantic_similarities),
                    "标准差": np.std(semantic_similarities)
                }
            }
            
            report[method_name] = method_report
        
        return report
    
    def save_results(self, filename: str = "evaluation_results.json"):
        """保存评估结果"""
        filepath = os.path.join(self.working_dir, filename)
        
        # 转换为可序列化的格式
        serializable_results = []
        for result in self.results:
            serializable_results.append({
                "query": result.query,
                "expected_answer": result.expected_answer,
                "retrieved_answer": result.retrieved_answer,
                "response_time": result.response_time,
                "bleu_score": result.bleu_score,
                "rouge_score": result.rouge_score,
                "semantic_similarity": result.semantic_similarity,
                "retrieval_method": result.retrieval_method
            })
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 评估结果已保存到: {filepath}")
    
    def create_visualizations(self):
        """创建可视化图表"""
        if not self.results:
            print("没有结果可以可视化")
            return
        
        # 创建DataFrame
        df_data = []
        for result in self.results:
            df_data.append({
                'method': result.retrieval_method,
                'response_time': result.response_time,
                'bleu_score': result.bleu_score,
                'rouge_score': result.rouge_score,
                'semantic_similarity': result.semantic_similarity
            })
        
        df = pd.DataFrame(df_data)
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('RAG检索系统性能评估结果', fontsize=16)
        
        # 响应时间分布
        sns.boxplot(data=df, x='method', y='response_time', ax=axes[0,0])
        axes[0,0].set_title('响应时间分布')
        axes[0,0].set_ylabel('响应时间 (秒)')
        
        # BLEU分数分布
        sns.boxplot(data=df, x='method', y='bleu_score', ax=axes[0,1])
        axes[0,1].set_title('BLEU分数分布')
        axes[0,1].set_ylabel('BLEU分数')
        
        # ROUGE分数分布
        sns.boxplot(data=df, x='method', y='rouge_score', ax=axes[1,0])
        axes[1,0].set_title('ROUGE分数分布')
        axes[1,0].set_ylabel('ROUGE分数')
        
        # 语义相似度分布
        sns.boxplot(data=df, x='method', y='semantic_similarity', ax=axes[1,1])
        axes[1,1].set_title('语义相似度分布')
        axes[1,1].set_ylabel('语义相似度')
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(self.working_dir, 'performance_charts.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"✓ 性能图表已保存到: {chart_path}")
        
        plt.show()

def test_framework():
    """测试评估框架"""
    framework = RAGEvaluationFramework()
    
    # 测试评估指标计算
    ref = "人工智能是计算机科学的一个分支"
    cand = "AI是计算机科学领域的重要分支"
    
    bleu = framework.calculate_bleu_score(ref, cand)
    rouge = framework.calculate_rouge_score(ref, cand)
    semantic = framework.calculate_semantic_similarity(ref, cand)
    
    print(f"BLEU分数: {bleu:.3f}")
    print(f"ROUGE分数: {rouge:.3f}")
    print(f"语义相似度: {semantic:.3f}")

if __name__ == "__main__":
    test_framework()
