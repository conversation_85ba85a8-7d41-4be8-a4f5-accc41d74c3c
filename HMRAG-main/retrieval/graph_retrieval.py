try:
    from lightrag import LightRAG, QueryParam
    from lightrag.utils import EmbeddingFunc
except ImportError:
    # 使用兼容性包装器
    from lightrag_wrapper import LightRAG, QueryParam, EmbeddingFunc
import requests
import json
import numpy as np
from sentence_transformers import SentenceTransformer
import torch

from retrieval.base_retrieval import BaseRetrieval

class GraphRetrieval(BaseRetrieval):
    def __init__(self, config):
        self.config = config

        # 初始化CLIP模型用于embedding - 使用本地模型
        clip_model_path = './clip-ViT-B-32-multilingual-v1'
        try:
            self.clip_model = SentenceTransformer(clip_model_path, trust_remote_code=True)
            print(f"✓ CLIP模型加载成功: {clip_model_path}")
        except Exception as e:
            print(f"⚠ CLIP模型加载失败: {e}")
            self.clip_model = None

        # 自定义Ollama Qwen2.5VL LLM函数
        def qwen_ollama_complete(prompt, model_name, **kwargs):
            url = "http://localhost:11434/api/generate"
            data = {
                "model": "qwen2.5vl:32b",
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", 0.7),
                    "num_predict": kwargs.get("max_tokens", 4000)
                }
            }

            try:
                response = requests.post(url, json=data, timeout=120)
                response.raise_for_status()
                result = response.json()
                return result["response"]
            except Exception as e:
                print(f"Ollama Qwen2.5VL API调用失败: {e}")
                return "API调用失败"

        # 自定义CLIP embedding函数 - 直接使用本地模型
        def clip_embed(texts):
            try:
                if self.clip_model is not None:
                    embeddings = self.clip_model.encode(texts, convert_to_tensor=True)
                    return embeddings.cpu().numpy().tolist()
                else:
                    print("本地CLIP模型不可用")
                    return [[0.0] * 512 for _ in texts]
            except Exception as e:
                print(f"CLIP embedding失败: {e}")
                # 返回默认维度的零向量
                return [[0.0] * 512 for _ in texts]

        self.client = LightRAG(
        working_dir=self.config.working_dir,
        llm_model_func=qwen_ollama_complete,
        llm_model_name="qwen2.5vl:32b",
        llm_model_max_async=160,
        llm_model_max_token_size=65536,
        llm_model_kwargs={
            "max_tokens": 4000,
            "temperature": 0.7
        },
        embedding_func=EmbeddingFunc(
            embedding_dim=512,  # CLIP-ViT-B/32的维度
            max_token_size=8192,
            func=clip_embed,
        ),
        )
        self.results = []

    
    def find_top_k(self, query):
        # self.results = self.client.query(query, 
        #                                  param=QueryParam(mode=self.config.mode, 
        #                                                   top_k=self.config.top_k))
        prompt = "Context: N/A\nQuestion: Which figure of speech is used in this text?\nSing, O goddess, the anger of Achilles son of Peleus, that brought countless ills upon the Achaeans.\n—Homer, The Iliad\nOptions: (A) chiasmus (B) apostrophe\nAnswer:\nSummary the output with format 'Answer: The answer is A, B, C, D, E or FAILED. \n BECAUSE: '"
        self.results = self.client.query(prompt, 
                                        #  param=QueryParam(mode=self.config.mode))
                                                        #  , top_k=self.config.top_k))
                                         param=QueryParam(mode="mix"))
        return self.results
    