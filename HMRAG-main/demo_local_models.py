#!/usr/bin/env python3
"""
演示本地模型集成的脚本
展示CLIP、BGE模型和Ollama Qwen2.5VL模型的实际使用
"""

import os
import sys
import torch
import requests
import json
from sentence_transformers import SentenceTransformer
import time
import numpy as np

def demo_clip_model():
    """演示CLIP模型的使用"""
    print("=" * 60)
    print("CLIP模型演示 - 多语言文本embedding")
    print("=" * 60)
    
    # 加载CLIP模型
    clip_model_path = './clip-ViT-B-32-multilingual-v1'
    print(f"加载CLIP模型: {clip_model_path}")
    clip_model = SentenceTransformer(clip_model_path, trust_remote_code=True)
    
    # 测试多语言文本
    test_texts = [
        "一只可爱的小猫在阳光下睡觉",
        "A cute cat sleeping in the sunshine",
        "Un chat mignon qui dort au soleil",
        "Eine süße Katz<PERSON>, die in der Sonne schläft",
        "太阳下睡觉的猫咪图片",
        "Photo of a cat sleeping under the sun"
    ]
    
    print("\n测试文本:")
    for i, text in enumerate(test_texts, 1):
        print(f"{i}. {text}")
    
    print("\n生成embeddings...")
    embeddings = clip_model.encode(test_texts, convert_to_tensor=True)
    embeddings_np = embeddings.cpu().numpy()
    
    print(f"✓ 生成完成，embedding维度: {embeddings_np.shape[1]}")
    
    # 计算相似度矩阵
    print("\n计算文本相似度:")
    similarities = np.dot(embeddings_np, embeddings_np.T)
    
    for i in range(len(test_texts)):
        for j in range(i+1, len(test_texts)):
            similarity = similarities[i][j]
            print(f"文本{i+1} vs 文本{j+1}: {similarity:.3f}")
    
    return embeddings_np

def demo_bge_model():
    """演示BGE模型的使用"""
    print("\n" + "=" * 60)
    print("BGE模型演示 - 中文文本检索embedding")
    print("=" * 60)
    
    # 加载BGE模型
    bge_model_path = './bge-large-zh-v1.5'
    print(f"加载BGE模型: {bge_model_path}")
    bge_model = SentenceTransformer(bge_model_path, trust_remote_code=True)
    
    # 测试中文检索场景
    query = "什么是人工智能？"
    documents = [
        "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        "机器学习是人工智能的一个子领域，专注于开发能够从数据中学习的算法。",
        "深度学习使用神经网络来模拟人脑的工作方式，是机器学习的一个重要分支。",
        "自然语言处理是人工智能的一个领域，专注于计算机与人类语言之间的交互。",
        "今天天气很好，适合出去散步。",
        "我喜欢吃苹果和香蕉。"
    ]
    
    print(f"\n查询: {query}")
    print("\n文档库:")
    for i, doc in enumerate(documents, 1):
        print(f"{i}. {doc}")
    
    print("\n生成embeddings...")
    query_embedding = bge_model.encode([query], convert_to_tensor=True)
    doc_embeddings = bge_model.encode(documents, convert_to_tensor=True)
    
    # 计算相似度
    similarities = torch.cosine_similarity(query_embedding, doc_embeddings)
    
    print("\n检索结果 (按相似度排序):")
    sorted_indices = torch.argsort(similarities, descending=True)
    
    for rank, idx in enumerate(sorted_indices[:3], 1):
        similarity = similarities[idx].item()
        print(f"{rank}. 相似度: {similarity:.3f} - {documents[idx]}")
    
    return query_embedding.cpu().numpy(), doc_embeddings.cpu().numpy()

def demo_ollama_qwen():
    """演示Ollama Qwen2.5VL模型的使用"""
    print("\n" + "=" * 60)
    print("Ollama Qwen2.5VL模型演示 - 智能问答")
    print("=" * 60)
    
    def qwen_chat(prompt, max_tokens=200):
        url = "http://localhost:11434/api/generate"
        data = {
            "model": "qwen2.5vl:32b",
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "num_predict": max_tokens
            }
        }
        
        try:
            response = requests.post(url, json=data, timeout=60)
            response.raise_for_status()
            result = response.json()
            return result["response"]
        except Exception as e:
            return f"API调用失败: {e}"
    
    # 测试不同类型的问题
    test_questions = [
        "请简单解释什么是机器学习？",
        "写一首关于春天的短诗。",
        "如何用Python计算斐波那契数列？",
        "请分析一下人工智能的发展趋势。"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n问题 {i}: {question}")
        print("-" * 40)
        
        start_time = time.time()
        answer = qwen_chat(question)
        end_time = time.time()
        
        print(f"回答 (耗时: {end_time - start_time:.2f}秒):")
        print(answer)
        print()

def demo_integrated_rag():
    """演示集成的RAG系统"""
    print("\n" + "=" * 60)
    print("集成RAG系统演示")
    print("=" * 60)
    
    # 加载模型
    print("加载模型...")
    clip_model = SentenceTransformer('./clip-ViT-B-32-multilingual-v1', trust_remote_code=True)
    bge_model = SentenceTransformer('./bge-large-zh-v1.5', trust_remote_code=True)
    
    # 模拟知识库
    knowledge_base = [
        "人工智能（AI）是指由机器展现出的智能，与人类和动物展现的自然智能形成对比。",
        "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习。",
        "深度学习是机器学习的一个子集，它使用具有多个层的神经网络来模拟人脑的工作方式。",
        "自然语言处理（NLP）是人工智能的一个分支，专注于计算机与人类语言之间的交互。",
        "计算机视觉是人工智能的一个领域，致力于让计算机能够理解和解释视觉信息。"
    ]
    
    def rag_query(question, top_k=2):
        print(f"\n用户问题: {question}")
        
        # 1. 使用BGE模型检索相关文档
        print("1. 检索相关文档...")
        question_embedding = bge_model.encode([question], convert_to_tensor=True)
        doc_embeddings = bge_model.encode(knowledge_base, convert_to_tensor=True)
        
        similarities = torch.cosine_similarity(question_embedding, doc_embeddings)
        top_indices = torch.argsort(similarities, descending=True)[:top_k]
        
        relevant_docs = [knowledge_base[idx] for idx in top_indices]
        print(f"检索到 {len(relevant_docs)} 个相关文档:")
        for i, doc in enumerate(relevant_docs, 1):
            print(f"  {i}. {doc}")
        
        # 2. 构建增强提示
        context = "\n".join(relevant_docs)
        enhanced_prompt = f"""基于以下背景信息回答问题：

背景信息：
{context}

问题：{question}

请基于背景信息给出准确、详细的回答："""
        
        # 3. 使用Qwen模型生成回答
        print("\n2. 生成回答...")
        url = "http://localhost:11434/api/generate"
        data = {
            "model": "qwen2.5vl:32b",
            "prompt": enhanced_prompt,
            "stream": False,
            "options": {
                "temperature": 0.3,
                "num_predict": 300
            }
        }
        
        try:
            response = requests.post(url, json=data, timeout=60)
            response.raise_for_status()
            result = response.json()
            return result["response"]
        except Exception as e:
            return f"生成回答失败: {e}"
    
    # 测试RAG系统
    test_questions = [
        "什么是深度学习？",
        "机器学习和人工智能有什么区别？",
        "自然语言处理的应用有哪些？"
    ]
    
    for question in test_questions:
        answer = rag_query(question)
        print(f"\nRAG系统回答:")
        print(answer)
        print("\n" + "="*60)

def main():
    """主演示函数"""
    print("🚀 本地模型集成演示")
    print("演示时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 切换到正确的目录
    os.chdir('/data/HMRAG-main')
    
    try:
        # 运行各个演示
        demo_clip_model()
        demo_bge_model()
        demo_ollama_qwen()
        demo_integrated_rag()
        
        print("\n🎉 演示完成！所有本地模型都运行正常！")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
