# HM-RAG 中国大陆适配版使用指南

本文档说明如何在中国大陆环境下使用HM-RAG，已替换为Elasticsearch和DeepSeek模型。

## 主要修改

### 1. 替换的组件
- **搜索引擎**: Google Serper API → Elasticsearch
- **语言模型**: OpenAI GPT → 本地部署的DeepSeek模型
- **API密钥**: 使用您提供的Elasticsearch API密钥

### 2. 新增依赖
```bash
pip install elasticsearch
```

## 环境准备

### 1. 安装Ollama并部署DeepSeek模型
```bash
# 安装Ollama
curl -fsSL https://ollama.com/install.sh | sh

# 启动Ollama服务
ollama serve

# 下载DeepSeek模型
ollama pull deepseek-chat
```

### 2. 安装Elasticsearch（可选，用于网络检索）
```bash
# 使用Docker安装Elasticsearch
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -p 9300:9300 \
  -e "discovery.type=single-node" \
  -e "ES_JAVA_OPTS=-Xms512m -Xmx512m" \
  elasticsearch:8.11.0
```

### 3. 安装Python依赖
```bash
conda create --name hmrag python=3.10
conda activate hmrag
pip install -r requirements.txt
```

## 使用方法

### 方法1: 使用提供的运行脚本（推荐）
```bash
python run_example.py
```

### 方法2: 手动运行
```bash
python main.py \
  --working_dir ./working_dir \
  --elasticsearch_api_key WjNaVEFwZ0JXdkp1N2ZFcy00ZE86Z2pqbDZwS2ZTMDRxLTYzZGxvQnJjQQ== \
  --local_model_name deepseek-chat \
  --local_model_base_url http://localhost:11434 \
  --llm_model_name deepseek-chat \
  --top_k 4 \
  --temperature 0.0 \
  --max_tokens 512 \
  --test_number 10 \
  --debug \
  --save_every 5 \
  --seed 42 \
  --label hmrag_deepseek_test \
  --shot_number 3
```

## 参数说明

### 必需参数
- `--working_dir`: 工作目录路径
- `--elasticsearch_api_key`: Elasticsearch API密钥（已设置默认值）

### 模型相关参数
- `--local_model_name`: 本地模型名称（默认: deepseek-chat）
- `--local_model_base_url`: 本地模型服务地址（默认: http://localhost:11434）
- `--llm_model_name`: LLM模型名称（默认: deepseek-chat）

### 检索参数
- `--top_k`: 检索返回的top-k结果数量（默认: 4）
- `--temperature`: 模型温度参数（默认: 0.0）
- `--max_tokens`: 最大token数（默认: 512）

### 实验参数
- `--test_number`: 测试问题数量，-1表示全部（默认: -1）
- `--debug`: 启用调试模式
- `--save_every`: 每隔多少个问题保存一次结果（默认: 10）
- `--seed`: 随机种子（默认: 42）
- `--label`: 实验标签（默认: hmrag_exp）
- `--shot_number`: few-shot示例数量（默认: 3）

### 数据相关参数（如果使用ScienceQA数据集）
- `--data_root`: 数据根目录
- `--image_root`: 图像根目录
- `--output_root`: 输出根目录
- `--caption_file`: 图像描述文件

## 注意事项

1. **命令行参数格式**: 保留`--`前缀，这是标准的命令行参数格式
2. **模型部署**: 确保DeepSeek模型已在Ollama中正确部署
3. **服务状态**: 运行前确保Ollama服务正在运行
4. **网络检索**: 如果不使用Elasticsearch，网络检索功能将受限
5. **内存要求**: DeepSeek模型需要足够的GPU/CPU内存

## 故障排除

### 1. Ollama连接失败
```bash
# 检查Ollama服务状态
curl http://localhost:11434/api/tags

# 重启Ollama服务
ollama serve
```

### 2. 模型未找到
```bash
# 列出已安装的模型
ollama list

# 重新下载DeepSeek模型
ollama pull deepseek-chat
```

### 3. Elasticsearch连接失败
```bash
# 检查Elasticsearch状态
curl http://localhost:9200

# 重启Elasticsearch容器
docker restart elasticsearch
```

## 输出结果

运行完成后，结果将保存在指定的输出目录中：
- 预测结果: `{output_root}/{label}_{test_split}.json`
- 运行日志: 控制台输出

## 性能优化建议

1. **GPU加速**: 如有GPU，确保CUDA环境正确配置
2. **内存管理**: 根据可用内存调整模型参数
3. **并发设置**: 调整`llm_model_max_async`参数优化并发性能
4. **批处理**: 增加`save_every`参数减少I/O操作频率
