#!/usr/bin/env python3
"""
测试本地模型集成的脚本
验证CLIP、BGE模型和Ollama Qwen2.5VL模型是否正常工作
"""

import os
import sys
import torch
import requests
import json
from sentence_transformers import SentenceTransformer
import time

def test_clip_model():
    """测试CLIP模型加载和embedding生成"""
    print("=" * 50)
    print("测试CLIP模型")
    print("=" * 50)
    
    try:
        clip_model_path = './clip-ViT-B-32-multilingual-v1'
        print(f"正在加载CLIP模型: {clip_model_path}")
        
        clip_model = SentenceTransformer(clip_model_path, trust_remote_code=True)
        print("✓ CLIP模型加载成功")
        
        # 测试embedding生成
        test_texts = ["这是一个测试文本", "This is a test text", "测试图像描述"]
        print(f"正在生成embedding，输入文本: {test_texts}")
        
        embeddings = clip_model.encode(test_texts, convert_to_tensor=True)
        embeddings_list = embeddings.cpu().numpy().tolist()
        
        print(f"✓ Embedding生成成功")
        print(f"  - Embedding维度: {len(embeddings_list[0])}")
        print(f"  - 生成的embedding数量: {len(embeddings_list)}")
        print(f"  - 第一个embedding前5个值: {embeddings_list[0][:5]}")
        
        return True
        
    except Exception as e:
        print(f"✗ CLIP模型测试失败: {e}")
        return False

def test_bge_model():
    """测试BGE模型加载和embedding生成"""
    print("\n" + "=" * 50)
    print("测试BGE模型")
    print("=" * 50)
    
    try:
        bge_model_path = './bge-large-zh-v1.5'
        print(f"正在加载BGE模型: {bge_model_path}")
        
        bge_model = SentenceTransformer(bge_model_path, trust_remote_code=True)
        print("✓ BGE模型加载成功")
        
        # 测试embedding生成
        test_texts = ["这是一个中文测试文本", "用于测试BGE模型的文本", "检索系统测试"]
        print(f"正在生成embedding，输入文本: {test_texts}")
        
        embeddings = bge_model.encode(test_texts, convert_to_tensor=True)
        embeddings_list = embeddings.cpu().numpy().tolist()
        
        print(f"✓ Embedding生成成功")
        print(f"  - Embedding维度: {len(embeddings_list[0])}")
        print(f"  - 生成的embedding数量: {len(embeddings_list)}")
        print(f"  - 第一个embedding前5个值: {embeddings_list[0][:5]}")
        
        return True
        
    except Exception as e:
        print(f"✗ BGE模型测试失败: {e}")
        return False

def test_ollama_qwen():
    """测试Ollama Qwen2.5VL模型"""
    print("\n" + "=" * 50)
    print("测试Ollama Qwen2.5VL模型")
    print("=" * 50)
    
    try:
        # 首先检查ollama服务是否运行
        print("检查Ollama服务状态...")
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✓ Ollama服务正在运行")
            models = response.json()
            print(f"  - 可用模型: {[model['name'] for model in models.get('models', [])]}")
        else:
            print("✗ Ollama服务不可用")
            return False
            
    except Exception as e:
        print(f"✗ 无法连接到Ollama服务: {e}")
        return False
    
    try:
        # 测试模型生成
        print("测试Qwen2.5VL模型生成...")
        url = "http://localhost:11434/api/generate"
        data = {
            "model": "qwen2.5vl:32b",
            "prompt": "你好，请简单介绍一下你自己。",
            "stream": False,
            "options": {
                "temperature": 0.7,
                "num_predict": 100
            }
        }
        
        print("正在发送请求到Ollama...")
        start_time = time.time()
        response = requests.post(url, json=data, timeout=60)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            print("✓ Qwen2.5VL模型响应成功")
            print(f"  - 响应时间: {end_time - start_time:.2f}秒")
            print(f"  - 模型响应: {result.get('response', '无响应')[:200]}...")
            return True
        else:
            print(f"✗ Qwen2.5VL模型请求失败: {response.status_code}")
            print(f"  - 错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Qwen2.5VL模型测试失败: {e}")
        return False

def test_retrieval_integration():
    """测试检索系统集成"""
    print("\n" + "=" * 50)
    print("测试检索系统集成")
    print("=" * 50)

    try:
        # 测试embedding函数
        print("测试CLIP embedding函数...")
        clip_model_path = './clip-ViT-B-32-multilingual-v1'
        clip_model = SentenceTransformer(clip_model_path, trust_remote_code=True)

        def clip_embed(texts):
            try:
                if clip_model is not None:
                    embeddings = clip_model.encode(texts, convert_to_tensor=True)
                    return embeddings.cpu().numpy().tolist()
                else:
                    print("本地CLIP模型不可用")
                    return [[0.0] * 512 for _ in texts]
            except Exception as e:
                print(f"CLIP embedding失败: {e}")
                return [[0.0] * 512 for _ in texts]

        test_texts = ["测试文本1", "测试文本2"]
        embeddings = clip_embed(test_texts)
        print(f"✓ CLIP embedding函数测试成功，维度: {len(embeddings[0])}")

        # 测试BGE embedding函数
        print("测试BGE embedding函数...")
        bge_model_path = './bge-large-zh-v1.5'
        bge_model = SentenceTransformer(bge_model_path, trust_remote_code=True)

        def bge_embed(texts):
            try:
                if bge_model is not None:
                    embeddings = bge_model.encode(texts, convert_to_tensor=True)
                    return embeddings.cpu().numpy().tolist()
                else:
                    print("本地BGE模型不可用")
                    return [[0.0] * 1024 for _ in texts]
            except Exception as e:
                print(f"BGE embedding失败: {e}")
                return [[0.0] * 1024 for _ in texts]

        embeddings = bge_embed(test_texts)
        print(f"✓ BGE embedding函数测试成功，维度: {len(embeddings[0])}")

        # 测试Ollama LLM函数
        print("测试Ollama LLM函数...")
        def qwen_ollama_complete(prompt, model_name, **kwargs):
            url = "http://localhost:11434/api/generate"
            data = {
                "model": "qwen2.5vl:32b",
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", 0.7),
                    "num_predict": kwargs.get("max_tokens", 100)
                }
            }

            try:
                response = requests.post(url, json=data, timeout=30)
                response.raise_for_status()
                result = response.json()
                return result["response"]
            except Exception as e:
                print(f"Ollama Qwen2.5VL API调用失败: {e}")
                return "API调用失败"

        test_response = qwen_ollama_complete("简单回答：1+1等于多少？", "qwen2.5vl:32b", max_tokens=50)
        print(f"✓ Ollama LLM函数测试成功，响应: {test_response[:50]}...")

        print("✓ 检索系统核心功能测试成功")
        return True

    except Exception as e:
        print(f"✗ 检索系统集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试本地模型集成")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 切换到正确的目录
    os.chdir('/data/HMRAG-main')
    
    results = []
    
    # 运行所有测试
    results.append(("CLIP模型", test_clip_model()))
    results.append(("BGE模型", test_bge_model()))
    results.append(("Ollama Qwen2.5VL", test_ollama_qwen()))
    results.append(("检索系统核心功能", test_retrieval_integration()))
    
    # 输出测试结果总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！本地模型集成成功！")
    else:
        print("⚠️  部分测试失败，请检查相关配置和模型文件")
    print("=" * 50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
