#!/usr/bin/env python3
"""
BGE模型本地部署脚本
用于部署BAAI/bge-large-zh-v1.5模型
"""

import os
import sys
import torch
from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer, AutoModel
import numpy as np

def check_gpu():
    """检查GPU可用性"""
    if torch.cuda.is_available():
        print(f"✓ GPU可用: {torch.cuda.get_device_name(0)}")
        print(f"✓ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        return True
    else:
        print("⚠ 未检测到GPU，将使用CPU运行（速度较慢）")
        return False

def download_and_test_bge_model():
    """下载并测试BGE模型"""
    print("开始下载BGE模型...")

    # 尝试多个可能的模型名称
    model_names = [
        'BAAI/bge-large-zh-v1.5',
        'BAAI/bge-base-zh-v1.5',
        'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2'
    ]

    for model_name in model_names:
        try:
            print(f"尝试加载模型: {model_name}")
            model = SentenceTransformer(model_name, trust_remote_code=True)
            print(f"✓ 模型 {model_name} 下载成功")

            # 测试模型
            test_texts = [
                "这是一个测试句子",
                "BGE模型测试",
                "中文文本嵌入测试"
            ]

            print("正在测试模型...")
            embeddings = model.encode(test_texts)
            print(f"✓ 模型测试成功")
            print(f"✓ 嵌入维度: {embeddings.shape[1]}")
            print(f"✓ 测试文本数量: {embeddings.shape[0]}")

            # 保存成功的模型名称供后续使用
            with open('bge_model_name.txt', 'w') as f:
                f.write(model_name)

            return True

        except Exception as e:
            print(f"✗ 模型 {model_name} 加载失败: {e}")
            continue

    print("✗ 所有BGE模型都加载失败")
    return False

def download_and_test_clip_model():
    """下载并测试CLIP模型"""
    print("开始下载CLIP模型...")

    # 尝试多个可能的CLIP模型名称
    model_names = [
        'sentence-transformers/clip-ViT-B-32',
        'openai/clip-vit-base-patch32',
        'sentence-transformers/all-MiniLM-L6-v2'
    ]

    for model_name in model_names:
        try:
            print(f"尝试加载模型: {model_name}")
            model = SentenceTransformer(model_name, trust_remote_code=True)
            print(f"✓ 模型 {model_name} 下载成功")

            # 测试模型
            test_texts = [
                "A photo of a cat",
                "Image of a dog",
                "Picture of a car"
            ]

            print("正在测试CLIP模型...")
            embeddings = model.encode(test_texts)
            print(f"✓ CLIP模型测试成功")
            print(f"✓ 嵌入维度: {embeddings.shape[1]}")
            print(f"✓ 测试文本数量: {embeddings.shape[0]}")

            # 保存成功的模型名称供后续使用
            with open('clip_model_name.txt', 'w') as f:
                f.write(model_name)

            return True

        except Exception as e:
            print(f"✗ 模型 {model_name} 加载失败: {e}")
            continue

    print("✗ 所有CLIP模型都加载失败")
    return False

def create_model_service():
    """创建简单的模型服务脚本"""
    service_code = '''#!/usr/bin/env python3
"""
BGE和CLIP模型服务
提供embedding API服务
"""

from flask import Flask, request, jsonify
from sentence_transformers import SentenceTransformer
import numpy as np
import logging

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

# 加载模型
# 读取成功的模型名称
try:
    with open('bge_model_name.txt', 'r') as f:
        bge_model_name = f.read().strip()
except:
    bge_model_name = 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2'

try:
    with open('clip_model_name.txt', 'r') as f:
        clip_model_name = f.read().strip()
except:
    clip_model_name = 'sentence-transformers/all-MiniLM-L6-v2'

print(f"正在加载BGE模型: {bge_model_name}...")
bge_model = SentenceTransformer(bge_model_name, trust_remote_code=True)
print("✓ BGE模型加载完成")

print(f"正在加载CLIP模型: {clip_model_name}...")
clip_model = SentenceTransformer(clip_model_name, trust_remote_code=True)
print("✓ CLIP模型加载完成")

@app.route('/embed/bge', methods=['POST'])
def bge_embed():
    """BGE模型embedding接口"""
    try:
        data = request.json
        texts = data.get('texts', [])
        
        if not texts:
            return jsonify({'error': '缺少texts参数'}), 400
        
        embeddings = bge_model.encode(texts)
        return jsonify({
            'embeddings': embeddings.tolist(),
            'dimension': embeddings.shape[1],
            'count': len(texts)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/embed/clip', methods=['POST'])
def clip_embed():
    """CLIP模型embedding接口"""
    try:
        data = request.json
        texts = data.get('texts', [])
        
        if not texts:
            return jsonify({'error': '缺少texts参数'}), 400
        
        embeddings = clip_model.encode(texts)
        return jsonify({
            'embeddings': embeddings.tolist(),
            'dimension': embeddings.shape[1],
            'count': len(texts)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health():
    """健康检查接口"""
    return jsonify({'status': 'healthy', 'models': ['bge-large-zh-v1.5', 'clip-ViT-B-32']})

if __name__ == '__main__':
    print("启动模型服务...")
    print("BGE embedding API: http://localhost:5000/embed/bge")
    print("CLIP embedding API: http://localhost:5000/embed/clip")
    print("健康检查: http://localhost:5000/health")
    app.run(host='0.0.0.0', port=5000, debug=False)
'''
    
    with open('model_service.py', 'w', encoding='utf-8') as f:
        f.write(service_code)
    
    print("✓ 模型服务脚本已创建: model_service.py")

def main():
    """主函数"""
    print("=" * 50)
    print("BGE和CLIP模型部署脚本")
    print("=" * 50)
    
    # 检查GPU
    gpu_available = check_gpu()
    
    # 下载并测试BGE模型
    print("\n" + "=" * 30)
    print("部署BGE模型")
    print("=" * 30)
    bge_success = download_and_test_bge_model()
    
    # 下载并测试CLIP模型
    print("\n" + "=" * 30)
    print("部署CLIP模型")
    print("=" * 30)
    clip_success = download_and_test_clip_model()
    
    # 创建模型服务
    if bge_success and clip_success:
        print("\n" + "=" * 30)
        print("创建模型服务")
        print("=" * 30)
        create_model_service()
        
        print("\n" + "=" * 50)
        print("部署完成!")
        print("=" * 50)
        print("下一步:")
        print("1. 运行模型服务: python model_service.py")
        print("2. 测试服务: curl http://localhost:5000/health")
        print("3. 运行HM-RAG系统")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("部署失败!")
        print("请检查网络连接和依赖安装")
        print("=" * 50)
        sys.exit(1)

if __name__ == "__main__":
    main()
