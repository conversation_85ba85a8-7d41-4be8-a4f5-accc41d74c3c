# HM-RAG Graph vs Vector 检索系统对比报告

## 实验概述
- **实验名称**: HM-RAG_Graph_vs_Vector_Comparison
- **实验时间**: 2025-07-15 20:04:04
- **测试样本**: 30 个
- **对比系统**: HM-RAG Graph检索 vs HM-RAG Vector检索

## 整体性能对比

### HM-RAG-Graph

**性能指标**:
- 平均响应时间: 10.43秒
- 响应时间中位数: 10.34秒
- 响应时间稳定性: ±3.62秒

**准确率指标**:
- 平均语义相似度: 0.369
- 语义相似度稳定性: ±0.045

### HM-RAG-Vector

**性能指标**:
- 平均响应时间: 11.58秒
- 响应时间中位数: 12.39秒
- 响应时间稳定性: ±1.95秒

**准确率指标**:
- 平均语义相似度: 0.325
- 语义相似度稳定性: ±0.048

## 按查询类别分析

| 类别 | 系统 | 平均语义相似度 | 平均响应时间 |
|------|------|----------------|--------------|
| 基础概念 | HM-RAG-Graph | 0.409 | 15.11s |
| 基础概念 | HM-RAG-Vector | 0.336 | 10.53s |
| 技术关系 | HM-RAG-Graph | 0.332 | 8.76s |
| 技术关系 | HM-RAG-Vector | 0.321 | 12.39s |
| 技术细节 | HM-RAG-Graph | 0.379 | 7.60s |
| 技术细节 | HM-RAG-Vector | 0.338 | 12.06s |
| 应用场景 | HM-RAG-Graph | 0.386 | 9.41s |
| 应用场景 | HM-RAG-Vector | 0.308 | 10.52s |
| 技术挑战 | HM-RAG-Graph | 0.374 | 10.82s |
| 技术挑战 | HM-RAG-Vector | 0.342 | 10.91s |
| 伦理问题 | HM-RAG-Graph | 0.341 | 10.34s |
| 伦理问题 | HM-RAG-Vector | 0.352 | 12.39s |
| 复杂推理 | HM-RAG-Graph | 0.306 | 12.83s |
| 复杂推理 | HM-RAG-Vector | 0.328 | 13.62s |
| 技术趋势 | HM-RAG-Graph | 0.316 | 10.82s |
| 技术趋势 | HM-RAG-Vector | 0.232 | 13.80s |

## 按查询难度分析

| 难度 | 系统 | 平均语义相似度 | 平均响应时间 |
|------|------|----------------|--------------|
| 简单 | HM-RAG-Graph | 0.384 | 15.20s |
| 简单 | HM-RAG-Vector | 0.293 | 10.27s |
| 中等 | HM-RAG-Graph | 0.374 | 8.03s |
| 中等 | HM-RAG-Vector | 0.337 | 11.50s |
| 困难 | HM-RAG-Graph | 0.352 | 10.94s |
| 困难 | HM-RAG-Vector | 0.327 | 12.49s |

## 结论与建议

### 主要发现
1. **性能对比**: 两种检索方法各有优势
2. **适用场景**: 根据具体需求选择合适的检索方法
3. **优化方向**: 可以考虑混合使用两种方法

---
*报告生成时间: 2025-07-15 20:04:04*
