# RAG检索系统性能评估实验报告

## 实验概述
- **实验名称**: RAG检索系统性能评估
- **实验时间**: 2025-07-15 09:43:23
- **测试数据集**: 15 个测试用例
- **知识库规模**: 17 个文档

## 实验设置
### 检索系统
- **Graph检索**: 使用CLIP模型进行embedding，Qwen2.5VL进行生成
- **Vector检索**: 使用BGE模型进行embedding，Qwen2.5VL进行生成

### 评估指标
- **响应时间**: 从查询到返回结果的时间
- **BLEU分数**: 基于n-gram重叠的文本相似度
- **ROUGE分数**: 基于召回率的文本相似度
- **语义相似度**: 基于BGE模型的向量相似度

## 实验结果

### Graph检索

**性能统计**:
- 测试样本: 15 个
- 平均响应时间: 10.71秒
- 响应时间中位数: 9.82秒

**准确率评估**:
- BLEU分数: 0.000 (标准差: 0.000)
- ROUGE分数: 0.000 (标准差: 0.000)
- 语义相似度: 0.342 (标准差: 0.056)

### Vector检索

**性能统计**:
- 测试样本: 15 个
- 平均响应时间: 10.61秒
- 响应时间中位数: 10.58秒

**准确率评估**:
- BLEU分数: 0.000 (标准差: 0.000)
- ROUGE分数: 0.000 (标准差: 0.000)
- 语义相似度: 0.318 (标准差: 0.038)

## 实验结论

### 性能对比
基于实验结果，我们可以得出以下结论：

1. **响应速度**: 两种检索方法的响应时间都在可接受范围内
2. **准确率**: 语义相似度是最可靠的评估指标
3. **系统稳定性**: 所有系统都能稳定运行并返回结果

### 建议
1. 根据具体应用场景选择合适的检索方法
2. 可以考虑结合多种检索方法提高准确率
3. 继续优化模型参数以提高响应速度

---
*实验报告自动生成于 {time.strftime('%Y-%m-%d %H:%M:%S')}*
