#!/usr/bin/env python3
"""
简化版对比实验：HM-RAG Graph vs Vector 检索系统对比
由于原始LightRAG网络依赖问题，我们专注于对比HM-RAG的两种检索方法
"""

import os
import sys
import time
import json
import numpy as np
from typing import Dict, List, Any
from dataclasses import dataclass
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

# 添加项目路径
sys.path.append('/data/HMRAG-main')

# 导入HM-RAG组件
from retrieval.graph_retrieval import GraphRetrieval
from retrieval.vector_retrieval import VectorRetrieval
from test_dataset import TestDatasetGenerator
from rag_evaluation_framework import RAGEvaluationFramework

@dataclass
class ComparisonResult:
    """对比结果数据类"""
    system_name: str
    query: str
    expected_answer: str
    retrieved_answer: str
    response_time: float
    bleu_score: float
    rouge_score: float
    semantic_similarity: float
    query_category: str
    query_difficulty: str

class SimplifiedComparisonExperiment:
    """简化版对比实验"""
    
    def __init__(self, experiment_name: str = "HM-RAG_Graph_vs_Vector_Comparison"):
        self.experiment_name = experiment_name
        self.results_dir = f"./simplified_comparison_results_{int(time.time())}"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 初始化组件
        self.evaluation_framework = RAGEvaluationFramework(self.results_dir)
        self.dataset_generator = TestDatasetGenerator()
        
        # 系统实例
        self.hmrag_graph = None
        self.hmrag_vector = None
        
        # 结果存储
        self.results = []
        
        print(f"🔬 简化版对比实验初始化: {experiment_name}")
        print(f"📁 结果目录: {self.results_dir}")
    
    def setup_hmrag_systems(self):
        """设置HM-RAG系统"""
        print("\n🔧 设置HM-RAG系统...")
        
        try:
            # 创建配置
            class ComparisonConfig:
                def __init__(self, suffix):
                    self.working_dir = f"./simplified_hmrag_test_{suffix}"
                    self.mode = "mix"
                    self.top_k = 3
            
            # Graph检索系统
            graph_config = ComparisonConfig("graph")
            os.makedirs(graph_config.working_dir, exist_ok=True)
            self.hmrag_graph = GraphRetrieval(graph_config)
            print("✓ HM-RAG Graph检索系统初始化成功")
            
            # Vector检索系统
            vector_config = ComparisonConfig("vector")
            os.makedirs(vector_config.working_dir, exist_ok=True)
            self.hmrag_vector = VectorRetrieval(vector_config)
            print("✓ HM-RAG Vector检索系统初始化成功")
            
            return True
            
        except Exception as e:
            print(f"❌ HM-RAG系统初始化失败: {e}")
            return False
    
    def build_knowledge_bases(self):
        """构建知识库"""
        print("\n📚 构建知识库...")
        
        knowledge_base = self.dataset_generator.get_knowledge_base()
        
        # 构建HM-RAG知识库
        for system_name, system in [("Graph", self.hmrag_graph), ("Vector", self.hmrag_vector)]:
            print(f"  构建 {system_name} 知识库...")
            for i, doc in enumerate(knowledge_base):
                try:
                    system.client.insert(doc)
                    if i % 5 == 0:
                        print(f"    已插入 {i+1}/{len(knowledge_base)} 个文档")
                except Exception as e:
                    print(f"    文档 {i+1} 插入失败: {e}")
        
        print("✓ 所有知识库构建完成")
    
    def evaluate_single_query(self, query: str, expected_answer: str, category: str, difficulty: str) -> List[ComparisonResult]:
        """评估单个查询"""
        results = []
        
        # 测试HM-RAG系统
        for system_name, system in [("HM-RAG-Graph", self.hmrag_graph), ("HM-RAG-Vector", self.hmrag_vector)]:
            print(f"  测试{system_name}...")
            start_time = time.time()
            try:
                response = system.find_top_k(query)
                end_time = time.time()
                response_time = end_time - start_time
                
                # 计算评估指标
                bleu = self.evaluation_framework.calculate_bleu_score(expected_answer, response)
                rouge = self.evaluation_framework.calculate_rouge_score(expected_answer, response)
                semantic = self.evaluation_framework.calculate_semantic_similarity(expected_answer, response)
                
                result = ComparisonResult(
                    system_name=system_name,
                    query=query,
                    expected_answer=expected_answer,
                    retrieved_answer=response,
                    response_time=response_time,
                    bleu_score=bleu,
                    rouge_score=rouge,
                    semantic_similarity=semantic,
                    query_category=category,
                    query_difficulty=difficulty
                )
                results.append(result)
                print(f"    响应时间: {response_time:.2f}s, 语义相似度: {semantic:.3f}")
                
            except Exception as e:
                print(f"    {system_name}查询失败: {e}")
        
        return results
    
    def run_comparison_test(self):
        """运行对比测试"""
        print("\n🚀 开始对比测试...")
        
        test_cases = self.dataset_generator.get_test_cases()
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n进度: {i}/{len(test_cases)}")
            query = test_case['query']
            expected_answer = test_case['expected_answer']
            category = test_case['category']
            difficulty = test_case['difficulty']
            
            print(f"查询: {query[:50]}...")
            print(f"类别: {category}, 难度: {difficulty}")
            
            query_results = self.evaluate_single_query(query, expected_answer, category, difficulty)
            self.results.extend(query_results)
        
        print(f"\n✓ 对比测试完成，共收集 {len(self.results)} 个结果")
    
    def analyze_results(self):
        """分析对比结果"""
        print("\n📊 分析对比结果...")
        
        # 按系统分组
        systems = {}
        for result in self.results:
            system = result.system_name
            if system not in systems:
                systems[system] = []
            systems[system].append(result)
        
        # 生成统计报告
        comparison_report = {}
        
        for system_name, results in systems.items():
            response_times = [r.response_time for r in results]
            semantic_similarities = [r.semantic_similarity for r in results]
            bleu_scores = [r.bleu_score for r in results]
            rouge_scores = [r.rouge_score for r in results]
            
            comparison_report[system_name] = {
                "样本数量": len(results),
                "响应时间": {
                    "平均值": np.mean(response_times),
                    "中位数": np.median(response_times),
                    "标准差": np.std(response_times),
                    "最小值": np.min(response_times),
                    "最大值": np.max(response_times)
                },
                "语义相似度": {
                    "平均值": np.mean(semantic_similarities),
                    "中位数": np.median(semantic_similarities),
                    "标准差": np.std(semantic_similarities)
                },
                "BLEU分数": {
                    "平均值": np.mean(bleu_scores),
                    "标准差": np.std(bleu_scores)
                },
                "ROUGE分数": {
                    "平均值": np.mean(rouge_scores),
                    "标准差": np.std(rouge_scores)
                }
            }
        
        # 按类别和难度分析
        category_analysis = self.analyze_by_category()
        difficulty_analysis = self.analyze_by_difficulty()
        
        comparison_report["按类别分析"] = category_analysis
        comparison_report["按难度分析"] = difficulty_analysis
        
        return comparison_report
    
    def analyze_by_category(self):
        """按查询类别分析"""
        category_results = {}
        
        for result in self.results:
            category = result.query_category
            system = result.system_name
            
            if category not in category_results:
                category_results[category] = {}
            if system not in category_results[category]:
                category_results[category][system] = []
            
            category_results[category][system].append(result)
        
        # 计算每个类别的平均性能
        category_summary = {}
        for category, systems in category_results.items():
            category_summary[category] = {}
            for system, results in systems.items():
                semantic_scores = [r.semantic_similarity for r in results]
                response_times = [r.response_time for r in results]
                
                category_summary[category][system] = {
                    "平均语义相似度": np.mean(semantic_scores),
                    "平均响应时间": np.mean(response_times),
                    "样本数": len(results)
                }
        
        return category_summary
    
    def analyze_by_difficulty(self):
        """按查询难度分析"""
        difficulty_results = {}
        
        for result in self.results:
            difficulty = result.query_difficulty
            system = result.system_name
            
            if difficulty not in difficulty_results:
                difficulty_results[difficulty] = {}
            if system not in difficulty_results[difficulty]:
                difficulty_results[difficulty][system] = []
            
            difficulty_results[difficulty][system].append(result)
        
        # 计算每个难度的平均性能
        difficulty_summary = {}
        for difficulty, systems in difficulty_results.items():
            difficulty_summary[difficulty] = {}
            for system, results in systems.items():
                semantic_scores = [r.semantic_similarity for r in results]
                response_times = [r.response_time for r in results]
                
                difficulty_summary[difficulty][system] = {
                    "平均语义相似度": np.mean(semantic_scores),
                    "平均响应时间": np.mean(response_times),
                    "样本数": len(results)
                }
        
        return difficulty_summary
    
    def create_comprehensive_visualizations(self):
        """创建全面的可视化图表"""
        print("\n📈 创建对比图表...")
        
        # 准备数据
        df_data = []
        for result in self.results:
            df_data.append({
                'system': result.system_name,
                'response_time': result.response_time,
                'semantic_similarity': result.semantic_similarity,
                'bleu_score': result.bleu_score,
                'rouge_score': result.rouge_score,
                'category': result.query_category,
                'difficulty': result.query_difficulty
            })
        
        df = pd.DataFrame(df_data)
        
        # 创建多个图表
        fig, axes = plt.subplots(3, 2, figsize=(16, 18))
        fig.suptitle('HM-RAG Graph vs Vector 检索系统详细对比', fontsize=16)
        
        # 1. 响应时间对比
        sns.boxplot(data=df, x='system', y='response_time', ax=axes[0,0])
        axes[0,0].set_title('响应时间对比')
        axes[0,0].set_ylabel('响应时间 (秒)')
        
        # 2. 语义相似度对比
        sns.boxplot(data=df, x='system', y='semantic_similarity', ax=axes[0,1])
        axes[0,1].set_title('语义相似度对比')
        axes[0,1].set_ylabel('语义相似度')
        
        # 3. 按类别的语义相似度对比
        sns.boxplot(data=df, x='category', y='semantic_similarity', hue='system', ax=axes[1,0])
        axes[1,0].set_title('按类别的语义相似度对比')
        axes[1,0].set_ylabel('语义相似度')
        axes[1,0].tick_params(axis='x', rotation=45)
        
        # 4. 按难度的语义相似度对比
        sns.boxplot(data=df, x='difficulty', y='semantic_similarity', hue='system', ax=axes[1,1])
        axes[1,1].set_title('按难度的语义相似度对比')
        axes[1,1].set_ylabel('语义相似度')
        
        # 5. 按类别的响应时间对比
        sns.boxplot(data=df, x='category', y='response_time', hue='system', ax=axes[2,0])
        axes[2,0].set_title('按类别的响应时间对比')
        axes[2,0].set_ylabel('响应时间 (秒)')
        axes[2,0].tick_params(axis='x', rotation=45)
        
        # 6. 响应时间vs语义相似度散点图
        sns.scatterplot(data=df, x='response_time', y='semantic_similarity', hue='system', ax=axes[2,1])
        axes[2,1].set_title('响应时间 vs 语义相似度')
        axes[2,1].set_xlabel('响应时间 (秒)')
        axes[2,1].set_ylabel('语义相似度')
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(self.results_dir, 'comprehensive_comparison_charts.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"✓ 对比图表已保存到: {chart_path}")
        
        plt.show()
    
    def save_results(self, report):
        """保存对比结果"""
        # 保存详细结果
        detailed_results = []
        for result in self.results:
            detailed_results.append({
                "system_name": result.system_name,
                "query": result.query,
                "expected_answer": result.expected_answer,
                "retrieved_answer": result.retrieved_answer,
                "response_time": result.response_time,
                "bleu_score": result.bleu_score,
                "rouge_score": result.rouge_score,
                "semantic_similarity": result.semantic_similarity,
                "query_category": result.query_category,
                "query_difficulty": result.query_difficulty
            })
        
        results_path = os.path.join(self.results_dir, "detailed_comparison_results.json")
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, ensure_ascii=False, indent=2)
        
        # 保存分析报告
        report_path = os.path.join(self.results_dir, "comparison_analysis_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 详细结果已保存到: {results_path}")
        print(f"✓ 分析报告已保存到: {report_path}")
    
    def generate_markdown_report(self, report):
        """生成Markdown格式的报告"""
        report_content = f"""# HM-RAG Graph vs Vector 检索系统对比报告

## 实验概述
- **实验名称**: {self.experiment_name}
- **实验时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}
- **测试样本**: {len(self.results)} 个
- **对比系统**: HM-RAG Graph检索 vs HM-RAG Vector检索

## 整体性能对比

"""
        
        for system_name, data in report.items():
            if system_name in ["按类别分析", "按难度分析"]:
                continue
                
            report_content += f"""### {system_name}

**性能指标**:
- 平均响应时间: {data['响应时间']['平均值']:.2f}秒
- 响应时间中位数: {data['响应时间']['中位数']:.2f}秒
- 响应时间稳定性: ±{data['响应时间']['标准差']:.2f}秒

**准确率指标**:
- 平均语义相似度: {data['语义相似度']['平均值']:.3f}
- 语义相似度稳定性: ±{data['语义相似度']['标准差']:.3f}

"""
        
        # 添加类别分析
        if "按类别分析" in report:
            report_content += """## 按查询类别分析

| 类别 | 系统 | 平均语义相似度 | 平均响应时间 |
|------|------|----------------|--------------|
"""
            for category, systems in report["按类别分析"].items():
                for system, metrics in systems.items():
                    report_content += f"| {category} | {system} | {metrics['平均语义相似度']:.3f} | {metrics['平均响应时间']:.2f}s |\n"
        
        # 添加难度分析
        if "按难度分析" in report:
            report_content += """
## 按查询难度分析

| 难度 | 系统 | 平均语义相似度 | 平均响应时间 |
|------|------|----------------|--------------|
"""
            for difficulty, systems in report["按难度分析"].items():
                for system, metrics in systems.items():
                    report_content += f"| {difficulty} | {system} | {metrics['平均语义相似度']:.3f} | {metrics['平均响应时间']:.2f}s |\n"
        
        report_content += f"""
## 结论与建议

### 主要发现
1. **性能对比**: 两种检索方法各有优势
2. **适用场景**: 根据具体需求选择合适的检索方法
3. **优化方向**: 可以考虑混合使用两种方法

---
*报告生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存报告
        report_path = os.path.join(self.results_dir, "comparison_report.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✓ Markdown报告已保存到: {report_path}")
        return report_path
    
    def print_comparison_summary(self, report: Dict):
        """打印对比总结"""
        print("\n" + "="*80)
        print("🏆 HM-RAG Graph vs Vector 检索系统对比结果总结")
        print("="*80)
        
        for system_name, data in report.items():
            if system_name in ["按类别分析", "按难度分析"]:
                continue
                
            print(f"\n📊 {system_name}:")
            print(f"  样本数量: {data['样本数量']}")
            print(f"  平均响应时间: {data['响应时间']['平均值']:.2f}秒")
            print(f"  平均语义相似度: {data['语义相似度']['平均值']:.3f}")
            print(f"  响应时间稳定性: ±{data['响应时间']['标准差']:.2f}秒")
            print(f"  准确率稳定性: ±{data['语义相似度']['标准差']:.3f}")
    
    def run_full_comparison(self):
        """运行完整对比实验"""
        print(f"🎬 开始完整对比实验: {self.experiment_name}")
        
        try:
            # 1. 设置系统
            if not self.setup_hmrag_systems():
                return False
            
            # 2. 构建知识库
            self.build_knowledge_bases()
            
            # 3. 运行对比测试
            self.run_comparison_test()
            
            # 4. 分析结果
            report = self.analyze_results()
            
            # 5. 保存结果
            self.save_results(report)
            
            # 6. 创建可视化
            self.create_comprehensive_visualizations()
            
            # 7. 生成报告
            report_path = self.generate_markdown_report(report)
            
            # 8. 打印总结
            self.print_comparison_summary(report)
            
            print(f"\n🎉 对比实验完成！")
            print(f"📁 结果目录: {self.results_dir}")
            print(f"📄 详细报告: {report_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 对比实验失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    experiment = SimplifiedComparisonExperiment()
    success = experiment.run_full_comparison()
    
    if success:
        print("\n✅ 对比实验成功完成！")
    else:
        print("\n❌ 对比实验失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
