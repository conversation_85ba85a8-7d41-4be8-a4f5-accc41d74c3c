#!/usr/bin/env python3
"""
使用transformers库部署CLIP-ViT-B/32模型
"""

import os
import sys
import torch
import numpy as np
from PIL import Image

def test_transformers_clip():
    """测试transformers中的CLIP模型"""
    try:
        from transformers import CLIPProcessor, CLIPModel
        
        print("正在加载CLIP-ViT-B/32模型（transformers版本）...")
        
        # 尝试加载模型
        model_name = "openai/clip-vit-base-patch32"
        
        try:
            processor = CLIPProcessor.from_pretrained(model_name)
            model = CLIPModel.from_pretrained(model_name)
            print("✓ CLIP模型加载成功")
        except Exception as e:
            print(f"✗ 从HuggingFace加载失败: {e}")
            print("尝试使用本地缓存或离线模式...")
            return False, None, None
        
        # 测试文本编码
        device = "cuda" if torch.cuda.is_available() else "cpu"
        model.to(device)
        
        test_texts = ["a photo of a cat", "a photo of a dog", "a beautiful landscape"]
        
        inputs = processor(text=test_texts, return_tensors="pt", padding=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        with torch.no_grad():
            text_features = model.get_text_features(**inputs)
            # 归一化
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
        
        print(f"✓ 文本编码测试成功")
        print(f"✓ 文本特征维度: {text_features.shape}")
        print(f"✓ 特征范围: [{text_features.min().item():.3f}, {text_features.max().item():.3f}]")
        
        return True, model, processor
        
    except ImportError:
        print("✗ transformers库不可用")
        return False, None, None
    except Exception as e:
        print(f"✗ CLIP模型测试失败: {e}")
        return False, None, None

def create_transformers_clip_service():
    """创建基于transformers的CLIP服务"""
    
    service_code = '''#!/usr/bin/env python3
"""
基于transformers的CLIP-ViT-B/32模型服务
"""

from flask import Flask, request, jsonify
import torch
from transformers import CLIPProcessor, CLIPModel
import numpy as np
from PIL import Image
import base64
from io import BytesIO
import logging

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

# 全局变量
model = None
processor = None
device = None

def initialize_clip():
    """初始化CLIP模型"""
    global model, processor, device
    
    try:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"正在加载CLIP模型到设备: {device}")
        
        model_name = "openai/clip-vit-base-patch32"
        processor = CLIPProcessor.from_pretrained(model_name)
        model = CLIPModel.from_pretrained(model_name)
        model.to(device)
        
        print("✓ CLIP模型加载成功")
        return True
    except Exception as e:
        print(f"✗ CLIP模型加载失败: {e}")
        return False

@app.route('/embed/clip', methods=['POST'])
def clip_embed():
    """CLIP文本编码接口"""
    if model is None:
        return jsonify({'error': 'CLIP模型未初始化'}), 500
    
    try:
        data = request.json
        texts = data.get('texts', [])
        
        if not texts:
            return jsonify({'error': '缺少texts参数'}), 400
        
        # 文本编码
        inputs = processor(text=texts, return_tensors="pt", padding=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        with torch.no_grad():
            text_features = model.get_text_features(**inputs)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
        
        # 转换为numpy数组
        embeddings = text_features.cpu().numpy()
        
        return jsonify({
            'embeddings': embeddings.tolist(),
            'dimension': embeddings.shape[1],
            'count': len(texts),
            'model': 'CLIP-ViT-B/32-transformers'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/embed/clip/image', methods=['POST'])
def clip_embed_image():
    """CLIP图像编码接口"""
    if model is None:
        return jsonify({'error': 'CLIP模型未初始化'}), 500
    
    try:
        data = request.json
        images_b64 = data.get('images', [])
        
        if not images_b64:
            return jsonify({'error': '缺少images参数'}), 400
        
        embeddings = []
        
        for img_b64 in images_b64:
            # 解码base64图像
            img_data = base64.b64decode(img_b64)
            image = Image.open(BytesIO(img_data)).convert('RGB')
            
            # 图像编码
            inputs = processor(images=image, return_tensors="pt")
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            with torch.no_grad():
                image_features = model.get_image_features(**inputs)
                image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            
            embeddings.append(image_features.cpu().numpy().tolist()[0])
        
        return jsonify({
            'embeddings': embeddings,
            'dimension': len(embeddings[0]) if embeddings else 512,
            'count': len(embeddings),
            'model': 'CLIP-ViT-B/32-transformers'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health():
    """健康检查"""
    status = 'healthy' if model is not None else 'model_not_loaded'
    return jsonify({
        'status': status,
        'model': 'CLIP-ViT-B/32-transformers',
        'device': str(device) if device else 'unknown',
        'features': ['text_encoding', 'image_encoding']
    })

@app.route('/test', methods=['GET'])
def test():
    """测试接口"""
    if model is None:
        return jsonify({'error': 'CLIP模型未初始化'}), 500
    
    try:
        # 测试文本编码
        test_texts = ["a photo of a cat", "test text"]
        inputs = processor(text=test_texts, return_tensors="pt", padding=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        with torch.no_grad():
            text_features = model.get_text_features(**inputs)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
        
        return jsonify({
            'text_test': {
                'dimension': text_features.shape[1],
                'count': text_features.shape[0],
                'sample_norm': float(text_features[0].norm().item())
            },
            'status': 'success',
            'model': 'CLIP-ViT-B/32-transformers'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("初始化CLIP-ViT-B/32服务（transformers版本）...")
    
    if initialize_clip():
        print("启动CLIP模型服务...")
        print("文本编码API: http://localhost:5001/embed/clip")
        print("图像编码API: http://localhost:5001/embed/clip/image")
        print("健康检查: http://localhost:5001/health")
        print("测试接口: http://localhost:5001/test")
        app.run(host='0.0.0.0', port=5001, debug=False)
    else:
        print("CLIP模型初始化失败，无法启动服务")
        sys.exit(1)
'''
    
    with open('transformers_clip_service.py', 'w', encoding='utf-8') as f:
        f.write(service_code)
    
    print("✓ transformers版CLIP服务脚本已创建: transformers_clip_service.py")

def main():
    """主函数"""
    print("=" * 60)
    print("使用transformers部署CLIP-ViT-B/32模型")
    print("=" * 60)
    
    # 测试transformers CLIP模型
    success, model, processor = test_transformers_clip()
    if not success:
        print("transformers CLIP模型不可用，将继续使用离线模拟版本")
        print("如需真正的CLIP模型，请确保网络连接正常并重试")
        return 1
    
    # 创建transformers CLIP服务
    create_transformers_clip_service()
    
    print("\n" + "=" * 60)
    print("transformers版CLIP-ViT-B/32部署完成!")
    print("=" * 60)
    print("下一步:")
    print("1. 启动CLIP服务: python transformers_clip_service.py")
    print("2. 测试服务: curl http://localhost:5001/health")
    print("3. 更新HM-RAG配置以使用真正的CLIP模型")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
