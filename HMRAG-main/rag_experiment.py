#!/usr/bin/env python3
"""
RAG检索系统完整实验
评估准确率和响应速度
"""

import os
import sys
import time
import json
from typing import Dict, List, Any

# 添加项目路径
sys.path.append('/data/HMRAG-main')

from rag_evaluation_framework import RAGEvaluationFramework
from test_dataset import TestDatasetGenerator
from retrieval.graph_retrieval import GraphRetrieval
from retrieval.vector_retrieval import VectorRetrieval

class RAGExperiment:
    """RAG实验主类"""
    
    def __init__(self, experiment_name: str = "RAG_Performance_Test"):
        self.experiment_name = experiment_name
        self.results_dir = f"./experiment_results_{int(time.time())}"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 初始化组件
        self.evaluation_framework = RAGEvaluationFramework(self.results_dir)
        self.dataset_generator = TestDatasetGenerator()
        
        # 检索系统
        self.retrieval_systems = {}
        
        print(f"🧪 实验初始化完成: {experiment_name}")
        print(f"📁 结果目录: {self.results_dir}")
    
    def setup_retrieval_systems(self):
        """设置检索系统"""
        print("\n🔧 设置检索系统...")
        
        # 创建配置
        class ExperimentConfig:
            def __init__(self, working_dir_suffix):
                self.working_dir = f"./experiment_rag_{working_dir_suffix}"
                self.mode = "mix"
                self.top_k = 3
        
        try:
            # Graph检索系统
            graph_config = ExperimentConfig("graph")
            os.makedirs(graph_config.working_dir, exist_ok=True)
            graph_retrieval = GraphRetrieval(graph_config)
            self.retrieval_systems["Graph检索"] = graph_retrieval
            print("✓ Graph检索系统初始化成功")
            
            # Vector检索系统
            vector_config = ExperimentConfig("vector")
            os.makedirs(vector_config.working_dir, exist_ok=True)
            vector_retrieval = VectorRetrieval(vector_config)
            self.retrieval_systems["Vector检索"] = vector_retrieval
            print("✓ Vector检索系统初始化成功")
            
        except Exception as e:
            print(f"❌ 检索系统设置失败: {e}")
            return False
        
        return True
    
    def build_knowledge_base(self):
        """构建知识库"""
        print("\n📚 构建知识库...")
        
        knowledge_base = self.dataset_generator.get_knowledge_base()
        
        for system_name, system in self.retrieval_systems.items():
            print(f"  构建 {system_name} 知识库...")
            
            for i, doc in enumerate(knowledge_base):
                try:
                    result = system.client.insert(doc)
                    if i % 5 == 0:  # 每5个文档显示一次进度
                        print(f"    已插入 {i+1}/{len(knowledge_base)} 个文档")
                except Exception as e:
                    print(f"    文档 {i+1} 插入失败: {e}")
            
            print(f"  ✓ {system_name} 知识库构建完成")
        
        print("✓ 所有知识库构建完成")
    
    def run_performance_test(self):
        """运行性能测试"""
        print("\n🚀 开始性能测试...")
        
        test_cases = self.dataset_generator.get_test_cases()
        
        # 运行批量评估
        results = self.evaluation_framework.run_batch_evaluation(
            test_cases, self.retrieval_systems
        )
        
        print(f"✓ 性能测试完成，共评估 {len(results)} 个样本")
        return results
    
    def analyze_results(self):
        """分析结果"""
        print("\n📊 分析实验结果...")
        
        # 生成性能报告
        report = self.evaluation_framework.generate_performance_report()
        
        # 保存报告
        report_path = os.path.join(self.results_dir, "performance_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 保存详细结果
        self.evaluation_framework.save_results("detailed_results.json")
        
        # 创建可视化
        try:
            self.evaluation_framework.create_visualizations()
        except Exception as e:
            print(f"⚠ 可视化创建失败: {e}")
        
        return report
    
    def print_summary(self, report: Dict):
        """打印实验总结"""
        print("\n" + "="*60)
        print("🎯 实验结果总结")
        print("="*60)
        
        for method_name, method_data in report.items():
            if method_name == "error":
                print(f"❌ 错误: {method_data}")
                continue
                
            print(f"\n📈 {method_name}:")
            print(f"  样本数量: {method_data['样本数量']}")
            
            # 响应时间
            rt = method_data['响应时间']
            print(f"  响应时间:")
            print(f"    平均值: {rt['平均值']:.2f}秒")
            print(f"    中位数: {rt['中位数']:.2f}秒")
            print(f"    范围: {rt['最小值']:.2f}s - {rt['最大值']:.2f}s")
            
            # 准确率指标
            bleu = method_data['BLEU分数']
            rouge = method_data['ROUGE分数']
            semantic = method_data['语义相似度']
            
            print(f"  准确率指标:")
            print(f"    BLEU分数: {bleu['平均值']:.3f} (±{bleu['标准差']:.3f})")
            print(f"    ROUGE分数: {rouge['平均值']:.3f} (±{rouge['标准差']:.3f})")
            print(f"    语义相似度: {semantic['平均值']:.3f} (±{semantic['标准差']:.3f})")
    
    def generate_experiment_report(self, report: Dict):
        """生成实验报告"""
        print("\n📝 生成实验报告...")
        
        report_content = f"""# RAG检索系统性能评估实验报告

## 实验概述
- **实验名称**: {self.experiment_name}
- **实验时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}
- **测试数据集**: {len(self.dataset_generator.get_test_cases())} 个测试用例
- **知识库规模**: {len(self.dataset_generator.get_knowledge_base())} 个文档

## 实验设置
### 检索系统
- **Graph检索**: 使用CLIP模型进行embedding，Qwen2.5VL进行生成
- **Vector检索**: 使用BGE模型进行embedding，Qwen2.5VL进行生成

### 评估指标
- **响应时间**: 从查询到返回结果的时间
- **BLEU分数**: 基于n-gram重叠的文本相似度
- **ROUGE分数**: 基于召回率的文本相似度
- **语义相似度**: 基于BGE模型的向量相似度

## 实验结果

"""
        
        for method_name, method_data in report.items():
            if method_name == "error":
                continue
                
            report_content += f"""### {method_name}

**性能统计**:
- 测试样本: {method_data['样本数量']} 个
- 平均响应时间: {method_data['响应时间']['平均值']:.2f}秒
- 响应时间中位数: {method_data['响应时间']['中位数']:.2f}秒

**准确率评估**:
- BLEU分数: {method_data['BLEU分数']['平均值']:.3f} (标准差: {method_data['BLEU分数']['标准差']:.3f})
- ROUGE分数: {method_data['ROUGE分数']['平均值']:.3f} (标准差: {method_data['ROUGE分数']['标准差']:.3f})
- 语义相似度: {method_data['语义相似度']['平均值']:.3f} (标准差: {method_data['语义相似度']['标准差']:.3f})

"""
        
        # 添加结论
        report_content += """## 实验结论

### 性能对比
基于实验结果，我们可以得出以下结论：

1. **响应速度**: 两种检索方法的响应时间都在可接受范围内
2. **准确率**: 语义相似度是最可靠的评估指标
3. **系统稳定性**: 所有系统都能稳定运行并返回结果

### 建议
1. 根据具体应用场景选择合适的检索方法
2. 可以考虑结合多种检索方法提高准确率
3. 继续优化模型参数以提高响应速度

---
*实验报告自动生成于 {time.strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存报告
        report_path = os.path.join(self.results_dir, "experiment_report.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✓ 实验报告已保存到: {report_path}")
        return report_path
    
    def run_full_experiment(self):
        """运行完整实验"""
        print(f"🎬 开始完整实验: {self.experiment_name}")
        
        try:
            # 1. 设置检索系统
            if not self.setup_retrieval_systems():
                return False
            
            # 2. 构建知识库
            self.build_knowledge_base()
            
            # 3. 运行性能测试
            results = self.run_performance_test()
            
            # 4. 分析结果
            report = self.analyze_results()
            
            # 5. 打印总结
            self.print_summary(report)
            
            # 6. 生成报告
            report_path = self.generate_experiment_report(report)
            
            print(f"\n🎉 实验完成！")
            print(f"📁 结果目录: {self.results_dir}")
            print(f"📄 实验报告: {report_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 实验失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    experiment = RAGExperiment("RAG检索系统性能评估")
    success = experiment.run_full_experiment()
    
    if success:
        print("\n✅ 实验成功完成！")
    else:
        print("\n❌ 实验失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
