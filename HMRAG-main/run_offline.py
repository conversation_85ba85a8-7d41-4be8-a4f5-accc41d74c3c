#!/usr/bin/env python3
"""
离线运行HM-RAG系统
使用离线模型服务，不依赖外部模型下载
"""

import os
import sys
import subprocess
import time
import signal
import requests
from pathlib import Path

class OfflineHMRAG:
    def __init__(self):
        self.model_service_process = None
        
    def start_offline_model_service(self):
        """启动离线模型服务"""
        print("启动离线模型服务...")
        
        try:
            # 在后台启动离线模型服务
            self.model_service_process = subprocess.Popen([
                sys.executable, "offline_model_service.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            print("正在启动离线模型服务...")
            
            # 等待服务启动
            for i in range(20):  # 最多等待20秒
                try:
                    response = requests.get("http://localhost:5000/health", timeout=2)
                    if response.status_code == 200:
                        print("✓ 离线模型服务启动成功")
                        
                        # 测试服务
                        test_response = requests.get("http://localhost:5000/test", timeout=5)
                        if test_response.status_code == 200:
                            test_data = test_response.json()
                            print(f"✓ BGE测试: {test_data['bge_test']['dimension']}维")
                            print(f"✓ CLIP测试: {test_data['clip_test']['dimension']}维")
                        
                        return True
                except requests.exceptions.RequestException:
                    pass
                
                time.sleep(1)
                print(f"等待服务启动... ({i+1}/20)")
            
            print("✗ 离线模型服务启动超时")
            return False
            
        except Exception as e:
            print(f"✗ 离线模型服务启动失败: {e}")
            return False
    
    def update_retrieval_for_offline(self):
        """更新检索模块以使用离线模式"""
        print("配置离线模式...")
        
        # 创建模型名称文件，指示使用离线模式
        with open('bge_model_name.txt', 'w') as f:
            f.write('offline-mode')
        
        with open('clip_model_name.txt', 'w') as f:
            f.write('offline-mode')
        
        print("✓ 离线模式配置完成")
    
    def run_hmrag(self):
        """运行HM-RAG系统"""
        print("\n" + "=" * 50)
        print("启动HM-RAG系统（离线模式）")
        print("=" * 50)
        
        # 确保工作目录存在
        os.makedirs("working_dir", exist_ok=True)
        
        cmd = [
            sys.executable, "main.py",
            "--config", "config_cn.yaml",
            "--test_number", "2",  # 运行2个测试问题
            "--debug"
        ]
        
        print("运行命令:", " ".join(cmd))
        print("=" * 50)
        
        try:
            # 直接运行，显示输出
            subprocess.run(cmd)
        except KeyboardInterrupt:
            print("\n用户中断运行")
        except Exception as e:
            print(f"运行异常: {e}")
    
    def cleanup(self):
        """清理资源"""
        if self.model_service_process:
            print("\n正在关闭离线模型服务...")
            self.model_service_process.terminate()
            try:
                self.model_service_process.wait(timeout=5)
                print("✓ 离线模型服务已关闭")
            except subprocess.TimeoutExpired:
                self.model_service_process.kill()
                print("✓ 离线模型服务已强制关闭")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在清理...")
        self.cleanup()
        sys.exit(0)
    
    def run(self):
        """主运行函数"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("HM-RAG 离线运行脚本")
        print("=" * 70)
        print("注意: 使用离线embedding方法，性能可能不如预训练模型")
        print("=" * 70)
        
        try:
            # 步骤1: 配置离线模式
            self.update_retrieval_for_offline()
            
            # 步骤2: 启动离线模型服务
            if not self.start_offline_model_service():
                print("离线模型服务启动失败，退出")
                return 1
            
            # 步骤3: 运行HM-RAG
            print("\n🎉 系统准备完成（离线模式）！")
            print("=" * 70)
            
            self.run_hmrag()
            
        except Exception as e:
            print(f"运行异常: {e}")
            return 1
        finally:
            self.cleanup()
        
        return 0

def main():
    """主函数"""
    runner = OfflineHMRAG()
    return runner.run()

if __name__ == "__main__":
    sys.exit(main())
