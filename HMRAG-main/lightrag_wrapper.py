#!/usr/bin/env python3
"""
LightRAG兼容性包装器
提供与原始LightRAG相同的接口，但使用简化的实现
"""

import os
import json
import pickle
import numpy as np
from typing import List, Dict, Any, Optional, Callable
import requests
from sentence_transformers import SentenceTransformer
import faiss
import time

class EmbeddingFunc:
    """Embedding函数包装器"""
    def __init__(self, embedding_dim: int, max_token_size: int, func: Callable):
        self.embedding_dim = embedding_dim
        self.max_token_size = max_token_size
        self.func = func
    
    def __call__(self, texts: List[str]) -> List[List[float]]:
        return self.func(texts)

class QueryParam:
    """查询参数类"""
    def __init__(self, mode: str = "mix", top_k: int = 5):
        self.mode = mode
        self.top_k = top_k

class SimpleLightRAG:
    """简化的LightRAG实现"""
    
    def __init__(self, 
                 working_dir: str,
                 llm_model_func: Callable,
                 llm_model_name: str,
                 llm_model_max_async: int = 160,
                 llm_model_max_token_size: int = 65536,
                 llm_model_kwargs: Dict = None,
                 embedding_func: EmbeddingFunc = None):
        
        self.working_dir = working_dir
        self.llm_model_func = llm_model_func
        self.llm_model_name = llm_model_name
        self.llm_model_max_async = llm_model_max_async
        self.llm_model_max_token_size = llm_model_max_token_size
        self.llm_model_kwargs = llm_model_kwargs or {}
        self.embedding_func = embedding_func
        
        # 确保工作目录存在
        os.makedirs(working_dir, exist_ok=True)
        
        # 初始化向量数据库
        self.vector_db_path = os.path.join(working_dir, "vector_db.index")
        self.metadata_path = os.path.join(working_dir, "metadata.json")
        self.documents_path = os.path.join(working_dir, "documents.pkl")
        
        # 加载或创建向量数据库
        self._load_or_create_vector_db()
        
        print(f"✓ SimpleLightRAG初始化成功，工作目录: {working_dir}")
    
    def _load_or_create_vector_db(self):
        """加载或创建向量数据库"""
        try:
            if os.path.exists(self.vector_db_path) and os.path.exists(self.metadata_path):
                # 加载现有数据库
                self.index = faiss.read_index(self.vector_db_path)
                with open(self.metadata_path, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
                with open(self.documents_path, 'rb') as f:
                    self.documents = pickle.load(f)
                print(f"✓ 加载现有向量数据库，包含 {len(self.documents)} 个文档")
            else:
                # 创建新数据库
                self.index = faiss.IndexFlatIP(self.embedding_func.embedding_dim)
                self.metadata = {"count": 0, "dimension": self.embedding_func.embedding_dim}
                self.documents = []
                print("✓ 创建新的向量数据库")
        except Exception as e:
            print(f"⚠ 向量数据库初始化失败: {e}")
            # 创建新数据库作为备选
            self.index = faiss.IndexFlatIP(self.embedding_func.embedding_dim)
            self.metadata = {"count": 0, "dimension": self.embedding_func.embedding_dim}
            self.documents = []
    
    def insert(self, text: str) -> str:
        """插入文档到向量数据库"""
        try:
            # 生成embedding
            embeddings = self.embedding_func([text])
            embedding = np.array(embeddings[0], dtype=np.float32).reshape(1, -1)
            
            # 归一化向量（用于余弦相似度）
            faiss.normalize_L2(embedding)
            
            # 添加到索引
            self.index.add(embedding)
            
            # 保存文档
            doc_id = len(self.documents)
            self.documents.append({
                "id": doc_id,
                "text": text,
                "timestamp": time.time()
            })
            
            # 更新元数据
            self.metadata["count"] = len(self.documents)
            
            # 保存到磁盘
            self._save_vector_db()
            
            print(f"✓ 文档插入成功，ID: {doc_id}")
            return f"Document {doc_id} inserted successfully"
            
        except Exception as e:
            print(f"✗ 文档插入失败: {e}")
            return f"Failed to insert document: {e}"
    
    def query(self, query: str, param: QueryParam = None) -> str:
        """查询向量数据库"""
        if param is None:
            param = QueryParam()
        
        try:
            if len(self.documents) == 0:
                # 如果没有文档，直接使用LLM回答
                return self._direct_llm_query(query)
            
            # 生成查询embedding
            query_embeddings = self.embedding_func([query])
            query_embedding = np.array(query_embeddings[0], dtype=np.float32).reshape(1, -1)
            
            # 归一化查询向量
            faiss.normalize_L2(query_embedding)
            
            # 搜索相似文档
            k = min(param.top_k, len(self.documents))
            scores, indices = self.index.search(query_embedding, k)
            
            # 获取相关文档
            relevant_docs = []
            for i, idx in enumerate(indices[0]):
                if idx < len(self.documents):
                    doc = self.documents[idx]
                    relevant_docs.append({
                        "text": doc["text"],
                        "score": float(scores[0][i]),
                        "id": doc["id"]
                    })
            
            # 构建增强提示
            if relevant_docs:
                context = "\n".join([doc["text"] for doc in relevant_docs[:3]])
                enhanced_query = f"""基于以下背景信息回答问题：

背景信息：
{context}

问题：{query}

请基于背景信息给出准确、详细的回答："""
            else:
                enhanced_query = query
            
            # 使用LLM生成回答
            response = self.llm_model_func(enhanced_query, self.llm_model_name, **self.llm_model_kwargs)
            
            return response
            
        except Exception as e:
            print(f"✗ 查询失败: {e}")
            # 回退到直接LLM查询
            return self._direct_llm_query(query)
    
    def _direct_llm_query(self, query: str) -> str:
        """直接使用LLM查询（无RAG）"""
        try:
            response = self.llm_model_func(query, self.llm_model_name, **self.llm_model_kwargs)
            return response
        except Exception as e:
            return f"查询失败: {e}"
    
    def _save_vector_db(self):
        """保存向量数据库到磁盘"""
        try:
            faiss.write_index(self.index, self.vector_db_path)
            with open(self.metadata_path, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
            with open(self.documents_path, 'wb') as f:
                pickle.dump(self.documents, f)
        except Exception as e:
            print(f"⚠ 保存向量数据库失败: {e}")

# 兼容性别名
LightRAG = SimpleLightRAG

def test_wrapper():
    """测试包装器功能"""
    print("=" * 50)
    print("测试LightRAG包装器")
    print("=" * 50)
    
    # 创建测试embedding函数
    def test_embed(texts):
        # 返回随机向量用于测试
        return [[0.1] * 512 for _ in texts]
    
    embedding_func = EmbeddingFunc(
        embedding_dim=512,
        max_token_size=8192,
        func=test_embed
    )
    
    # 创建测试LLM函数
    def test_llm(prompt, model_name, **kwargs):
        return f"测试回答: {prompt[:50]}..."
    
    # 创建LightRAG实例
    rag = LightRAG(
        working_dir="./test_wrapper_dir",
        llm_model_func=test_llm,
        llm_model_name="test-model",
        embedding_func=embedding_func
    )
    
    # 测试插入
    result = rag.insert("这是一个测试文档")
    print(f"插入结果: {result}")
    
    # 测试查询
    response = rag.query("测试查询")
    print(f"查询结果: {response}")
    
    print("✓ LightRAG包装器测试完成")

if __name__ == "__main__":
    test_wrapper()
