name: Bug Report
description: File a bug report
title: "[Bug]:"
labels: ["bug", "triage"]

body:
  - type: checkboxes
    id: existingcheck
    attributes:
      label: Do you need to file an issue?
      description: Please help us manage our time by avoiding duplicates and common bugs with the steps below.
      options:
        - label: I have searched the existing issues and this bug is not already filed.
        - label: I believe this is a legitimate bug, not just a question or feature request.
  - type: textarea
    id: description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is.
      placeholder: What went wrong?
  - type: textarea
    id: reproduce
    attributes:
      label: Steps to reproduce
      description: Steps to reproduce the behavior.
      placeholder: How can we replicate the issue?
  - type: textarea
    id: expected_behavior
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: What should have happened?
  - type: textarea
    id: configused
    attributes:
      label: LightRAG Config Used
      description: The LightRAG configuration used for the run.
      placeholder: The settings content or LightRAG configuration
      value: |
        # Paste your config here
  - type: textarea
    id: screenshotslogs
    attributes:
      label: Logs and screenshots
      description: If applicable, add screenshots and logs to help explain your problem.
      placeholder: Add logs and screenshots here
  - type: textarea
    id: additional_information
    attributes:
      label: Additional Information
      description: |
        - LightRAG Version: e.g., v0.1.1
        - Operating System: e.g., Windows 10, Ubuntu 20.04
        - Python Version: e.g., 3.8
        - Related Issues: e.g., #1
        - Any other relevant information.
      value: |
        - LightRAG Version:
        - Operating System:
        - Python Version:
        - Related Issues:
