const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/utils-vendor-BysuhMZA.js","assets/react-vendor-DEwriMA6.js"])))=>i.map(i=>d[i]);
var di=Object.defineProperty;var fi=(e,t,r)=>t in e?di(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Me=(e,t,r)=>fi(e,typeof t!="symbol"?t+"":t,r);import{R as W,r as p,c as hi,g as He,d as gi,e as pi}from"./react-vendor-DEwriMA6.js";import{_ as aa,a as sa,f as er,N as ia,b as la,c as ca,D as bn,d as Ut,F as mi,E as ua,e as vi,g as Un,h as yi,n as qn,v as Be,i as da,j as fa,r as We,k as ha,y as ga,p as bi,l as wi,U as Yr,m as xi,o as Si,S as _i}from"./graph-vendor-B-X5JegA.js";import{j as g,c as wn,P as St,a as pa,D as Ei,C as Ci,S as ki,R as Ti,u as Xe,b as ft,d as ma,e as Ri,A as Ai,f as Ee,g as Ce,h as ji,i as Ni,O as xn,k as va,l as Sn,m as Ii,T as ya,n as ba,o as wa,p as Li,q as Pi,r as xa,s as zi,t as Di,v as Oi,w as Gi,x as Fi,y as ct,z as Mi,B as $i}from"./ui-vendor-CeCm8EER.js";import{t as Hi,c as Sa,a as tr,b as Bi}from"./utils-vendor-BysuhMZA.js";function fe(...e){return Hi(Sa(e))}function rr(e){return e instanceof Error?e.message:`${e}`}function Hg(e,t){let r=0,n=null;return function(...a){const o=Date.now(),l=t-(o-r);l<=0?(n&&(clearTimeout(n),n=null),r=o,e.apply(this,a)):n||(n=setTimeout(()=>{r=Date.now(),n=null,e.apply(this,a)},l))}}const _n=e=>{const t=e;t.use={};for(const r of Object.keys(t.getState()))t.use[r]=()=>t(n=>n[r]);return t},Kr="",Bg="/webui/",Ie="ghost",Vi="#B2EBF2",Ui="#000",qi="#E2E2E2",Qr="#EEEEEE",Wi="#F57F17",Xi="#969696",Yi="#F57F17",Wn="#B2EBF2",Nt=50,Xn=100,ut=4,Jr=20,Vg=15,Yn="*",Ug={"text/plain":[".txt",".md",".html",".htm",".tex",".json",".xml",".yaml",".yml",".rtf",".odt",".epub",".csv",".log",".conf",".ini",".properties",".sql",".bat",".sh",".c",".cpp",".py",".java",".js",".ts",".swift",".go",".rb",".php",".css",".scss",".less"],"application/pdf":[".pdf"],"application/msword":[".doc"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":[".pptx"]},qg={name:"LightRAG",github:"https://github.com/HKUDS/LightRAG"},Ki="modulepreload",Qi=function(e){return"/webui/"+e},Kn={},Ji=function(t,r,n){let a=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),i=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));a=Promise.allSettled(r.map(s=>{if(s=Qi(s),s in Kn)return;Kn[s]=!0;const c=s.endsWith(".css"),u=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${s}"]${u}`))return;const d=document.createElement("link");if(d.rel=c?"stylesheet":Ki,c||(d.as="script"),d.crossOrigin="",d.href=s,i&&d.setAttribute("nonce",i),document.head.appendChild(d),c)return new Promise((h,f)=>{d.addEventListener("load",h),d.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${s}`)))})}))}function o(l){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=l,window.dispatchEvent(i),!i.defaultPrevented)throw l}return a.then(l=>{for(const i of l||[])i.status==="rejected"&&o(i.reason);return t().catch(o)})};function _a(e,t){let r;try{r=e()}catch{return}return{getItem:a=>{var o;const l=s=>s===null?null:JSON.parse(s,void 0),i=(o=r.getItem(a))!=null?o:null;return i instanceof Promise?i.then(l):l(i)},setItem:(a,o)=>r.setItem(a,JSON.stringify(o,void 0)),removeItem:a=>r.removeItem(a)}}const Zr=e=>t=>{try{const r=e(t);return r instanceof Promise?r:{then(n){return Zr(n)(r)},catch(n){return this}}}catch(r){return{then(n){return this},catch(n){return Zr(n)(r)}}}},Zi=(e,t)=>(r,n,a)=>{let o={storage:_a(()=>localStorage),partialize:y=>y,version:0,merge:(y,k)=>({...k,...y}),...t},l=!1;const i=new Set,s=new Set;let c=o.storage;if(!c)return e((...y)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),r(...y)},n,a);const u=()=>{const y=o.partialize({...n()});return c.setItem(o.name,{state:y,version:o.version})},d=a.setState;a.setState=(y,k)=>{d(y,k),u()};const h=e((...y)=>{r(...y),u()},n,a);a.getInitialState=()=>h;let f;const b=()=>{var y,k;if(!c)return;l=!1,i.forEach(E=>{var j;return E((j=n())!=null?j:h)});const I=((k=o.onRehydrateStorage)==null?void 0:k.call(o,(y=n())!=null?y:h))||void 0;return Zr(c.getItem.bind(c))(o.name).then(E=>{if(E)if(typeof E.version=="number"&&E.version!==o.version){if(o.migrate){const j=o.migrate(E.state,E.version);return j instanceof Promise?j.then(A=>[!0,A]):[!0,j]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,E.state];return[!1,void 0]}).then(E=>{var j;const[A,N]=E;if(f=o.merge(N,(j=n())!=null?j:h),r(f,!0),A)return u()}).then(()=>{I==null||I(f,void 0),f=n(),l=!0,s.forEach(E=>E(f))}).catch(E=>{I==null||I(void 0,E)})};return a.persist={setOptions:y=>{o={...o,...y},y.storage&&(c=y.storage)},clearStorage:()=>{c==null||c.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>b(),hasHydrated:()=>l,onHydrate:y=>(i.add(y),()=>{i.delete(y)}),onFinishHydration:y=>(s.add(y),()=>{s.delete(y)})},o.skipHydration||b(),f||h},el=Zi,tl=tr()(el(e=>({theme:"system",language:"en",showPropertyPanel:!0,showNodeSearchBar:!0,showLegend:!1,showNodeLabel:!0,enableNodeDrag:!0,showEdgeLabel:!1,enableHideUnselectedEdges:!0,enableEdgeEvents:!1,minEdgeSize:1,maxEdgeSize:1,graphQueryMaxDepth:3,graphMaxNodes:1e3,backendMaxGraphNodes:null,graphLayoutMaxIterations:15,queryLabel:Yn,enableHealthCheck:!0,apiKey:null,currentTab:"documents",showFileName:!1,retrievalHistory:[],querySettings:{mode:"global",response_type:"Multiple Paragraphs",top_k:10,max_token_for_text_unit:6e3,max_token_for_global_context:4e3,max_token_for_local_context:4e3,only_need_context:!1,only_need_prompt:!1,stream:!0,history_turns:3,hl_keywords:[],ll_keywords:[],user_prompt:""},setTheme:t=>e({theme:t}),setLanguage:t=>{e({language:t}),Ji(async()=>{const{default:r}=await import("./utils-vendor-BysuhMZA.js").then(n=>n.d);return{default:r}},__vite__mapDeps([0,1])).then(({default:r})=>{r.language!==t&&r.changeLanguage(t)})},setGraphLayoutMaxIterations:t=>e({graphLayoutMaxIterations:t}),setQueryLabel:t=>e({queryLabel:t}),setGraphQueryMaxDepth:t=>e({graphQueryMaxDepth:t}),setGraphMaxNodes:(t,r=!1)=>{const n=Z.getState();if(n.graphMaxNodes!==t)if(r){const a=n.queryLabel;e({graphMaxNodes:t,queryLabel:""}),setTimeout(()=>{e({queryLabel:a})},300)}else e({graphMaxNodes:t})},setBackendMaxGraphNodes:t=>e({backendMaxGraphNodes:t}),setMinEdgeSize:t=>e({minEdgeSize:t}),setMaxEdgeSize:t=>e({maxEdgeSize:t}),setEnableHealthCheck:t=>e({enableHealthCheck:t}),setApiKey:t=>e({apiKey:t}),setCurrentTab:t=>e({currentTab:t}),setRetrievalHistory:t=>e({retrievalHistory:t}),updateQuerySettings:t=>e(r=>({querySettings:{...r.querySettings,...t}})),setShowFileName:t=>e({showFileName:t}),setShowLegend:t=>e({showLegend:t})}),{name:"settings-storage",storage:_a(()=>localStorage),version:14,migrate:(e,t)=>(t<2&&(e.showEdgeLabel=!1),t<3&&(e.queryLabel=Yn),t<4&&(e.showPropertyPanel=!0,e.showNodeSearchBar=!0,e.showNodeLabel=!0,e.enableHealthCheck=!0,e.apiKey=null),t<5&&(e.currentTab="documents"),t<6&&(e.querySettings={mode:"global",response_type:"Multiple Paragraphs",top_k:10,max_token_for_text_unit:4e3,max_token_for_global_context:4e3,max_token_for_local_context:4e3,only_need_context:!1,only_need_prompt:!1,stream:!0,history_turns:3,hl_keywords:[],ll_keywords:[]},e.retrievalHistory=[]),t<7&&(e.graphQueryMaxDepth=3,e.graphLayoutMaxIterations=15),t<8&&(e.graphMinDegree=0,e.language="en"),t<9&&(e.showFileName=!1),t<10&&(delete e.graphMinDegree,e.graphMaxNodes=1e3),t<11&&(e.minEdgeSize=1,e.maxEdgeSize=1),t<12&&(e.retrievalHistory=[]),t<13&&e.querySettings&&(e.querySettings.user_prompt=""),t<14&&(e.backendMaxGraphNodes=null),e)})),Z=_n(tl);class rl{constructor(){Me(this,"nodes",[]);Me(this,"edges",[]);Me(this,"nodeIdMap",{});Me(this,"edgeIdMap",{});Me(this,"edgeDynamicIdMap",{});Me(this,"getNode",t=>{const r=this.nodeIdMap[t];if(r!==void 0)return this.nodes[r]});Me(this,"getEdge",(t,r=!0)=>{const n=r?this.edgeDynamicIdMap[t]:this.edgeIdMap[t];if(n!==void 0)return this.edges[n]});Me(this,"buildDynamicMap",()=>{this.edgeDynamicIdMap={};for(let t=0;t<this.edges.length;t++){const r=this.edges[t];this.edgeDynamicIdMap[r.dynamicId]=t}})}}const nl=tr()((e,t)=>({selectedNode:null,focusedNode:null,selectedEdge:null,focusedEdge:null,moveToSelectedNode:!1,isFetching:!1,graphIsEmpty:!1,lastSuccessfulQueryLabel:"",graphDataFetchAttempted:!1,labelsFetchAttempted:!1,rawGraph:null,sigmaGraph:null,sigmaInstance:null,allDatabaseLabels:["*"],typeColorMap:new Map,searchEngine:null,setGraphIsEmpty:r=>e({graphIsEmpty:r}),setLastSuccessfulQueryLabel:r=>e({lastSuccessfulQueryLabel:r}),setIsFetching:r=>e({isFetching:r}),setSelectedNode:(r,n)=>e({selectedNode:r,moveToSelectedNode:n}),setFocusedNode:r=>e({focusedNode:r}),setSelectedEdge:r=>e({selectedEdge:r}),setFocusedEdge:r=>e({focusedEdge:r}),clearSelection:()=>e({selectedNode:null,focusedNode:null,selectedEdge:null,focusedEdge:null}),reset:()=>{e({selectedNode:null,focusedNode:null,selectedEdge:null,focusedEdge:null,rawGraph:null,sigmaGraph:null,searchEngine:null,moveToSelectedNode:!1,graphIsEmpty:!1})},setRawGraph:r=>e({rawGraph:r}),setSigmaGraph:r=>{e({sigmaGraph:r})},setAllDatabaseLabels:r=>e({allDatabaseLabels:r}),fetchAllDatabaseLabels:async()=>{try{console.log("Fetching all database labels...");const r=await al();e({allDatabaseLabels:["*",...r]});return}catch(r){throw console.error("Failed to fetch all database labels:",r),e({allDatabaseLabels:["*"]}),r}},setMoveToSelectedNode:r=>e({moveToSelectedNode:r}),setSigmaInstance:r=>e({sigmaInstance:r}),setTypeColorMap:r=>e({typeColorMap:r}),setSearchEngine:r=>e({searchEngine:r}),resetSearchEngine:()=>e({searchEngine:null}),setGraphDataFetchAttempted:r=>e({graphDataFetchAttempted:r}),setLabelsFetchAttempted:r=>e({labelsFetchAttempted:r}),nodeToExpand:null,nodeToPrune:null,triggerNodeExpand:r=>e({nodeToExpand:r}),triggerNodePrune:r=>e({nodeToPrune:r}),graphDataVersion:0,incrementGraphDataVersion:()=>e(r=>({graphDataVersion:r.graphDataVersion+1})),updateNodeAndSelect:async(r,n,a,o)=>{const l=t(),{sigmaGraph:i,rawGraph:s}=l;if(!(!i||!s||!i.hasNode(r)))try{const c=i.getNodeAttributes(r);if(console.log("updateNodeAndSelect",r,n,a,o),r===n&&a==="entity_id"){i.addNode(o,{...c,label:o});const u=[];i.forEachEdge(r,(h,f,b,y)=>{const k=b===r?y:b,I=b===r,E=h,j=s.edgeDynamicIdMap[E],A=i.addEdge(I?o:k,I?k:o,f);j!==void 0&&u.push({originalDynamicId:E,newEdgeId:A,edgeIndex:j}),i.dropEdge(h)}),i.dropNode(r);const d=s.nodeIdMap[r];d!==void 0&&(s.nodes[d].id=o,s.nodes[d].labels=[o],s.nodes[d].properties.entity_id=o,delete s.nodeIdMap[r],s.nodeIdMap[o]=d),u.forEach(({originalDynamicId:h,newEdgeId:f,edgeIndex:b})=>{s.edges[b]&&(s.edges[b].source===r&&(s.edges[b].source=o),s.edges[b].target===r&&(s.edges[b].target=o),s.edges[b].dynamicId=f,delete s.edgeDynamicIdMap[h],s.edgeDynamicIdMap[f]=b)}),e({selectedNode:o,moveToSelectedNode:!0})}else{const u=s.nodeIdMap[String(r)];u!==void 0&&(s.nodes[u].properties[a]=o,a==="entity_id"&&(s.nodes[u].labels=[o],i.setNodeAttribute(String(r),"label",o))),e(d=>({graphDataVersion:d.graphDataVersion+1}))}}catch(c){throw console.error("Error updating node in graph:",c),new Error("Failed to update node in graph")}},updateEdgeAndSelect:async(r,n,a,o,l,i)=>{const s=t(),{sigmaGraph:c,rawGraph:u}=s;if(!(!c||!u))try{const d=u.edgeIdMap[String(r)];d!==void 0&&u.edges[d]&&(u.edges[d].properties[l]=i,n!==void 0&&l==="keywords"&&c.setEdgeAttribute(n,"label",i)),e(h=>({graphDataVersion:h.graphDataVersion+1})),e({selectedEdge:n})}catch(d){throw console.error(`Error updating edge ${a}->${o} in graph:`,d),new Error("Failed to update edge in graph")}}})),te=_n(nl);class ol{constructor(){Me(this,"navigate",null)}setNavigate(t){this.navigate=t}resetAllApplicationState(t=!1){console.log("Resetting all application state...");const r=te.getState(),n=r.sigmaInstance;r.reset(),r.setGraphDataFetchAttempted(!1),r.setLabelsFetchAttempted(!1),r.setSigmaInstance(null),r.setIsFetching(!1),En.getState().clear(),t||Z.getState().setRetrievalHistory([]),sessionStorage.clear(),n&&(n.getGraph().clear(),n.kill(),te.getState().setSigmaInstance(null))}navigateToLogin(){if(!this.navigate){console.error("Navigation function not set");return}const t=qt.getState().username;t&&localStorage.setItem("LIGHTRAG-PREVIOUS-USER",t),this.resetAllApplicationState(!0),qt.getState().logout(),this.navigate("/login")}navigateToHome(){if(!this.navigate){console.error("Navigation function not set");return}this.navigate("/")}}const Ea=new ol,Wg="Invalid API Key",Xg="API Key required",xe=Bi.create({baseURL:Kr,headers:{"Content-Type":"application/json"}});xe.interceptors.request.use(e=>{const t=Z.getState().apiKey,r=localStorage.getItem("LIGHTRAG-API-TOKEN");return r&&(e.headers.Authorization=`Bearer ${r}`),t&&(e.headers["X-API-Key"]=t),e});xe.interceptors.response.use(e=>e,e=>{var t,r,n,a;if(e.response){if(((t=e.response)==null?void 0:t.status)===401){if((n=(r=e.config)==null?void 0:r.url)!=null&&n.includes("/login"))throw e;return Ea.navigateToLogin(),Promise.reject(new Error("Authentication required"))}throw new Error(`${e.response.status} ${e.response.statusText}
${JSON.stringify(e.response.data)}
${(a=e.config)==null?void 0:a.url}`)}throw e});const Ca=async(e,t,r)=>(await xe.get(`/graphs?label=${encodeURIComponent(e)}&max_depth=${t}&max_nodes=${r}`)).data,al=async()=>(await xe.get("/graph/label/list")).data,sl=async()=>{try{return(await xe.get("/health")).data}catch(e){return{status:"error",message:rr(e)}}},Yg=async()=>(await xe.get("/documents")).data,Kg=async()=>(await xe.post("/documents/scan")).data,Qg=async e=>(await xe.post("/query",e)).data,Jg=async(e,t,r)=>{const n=Z.getState().apiKey,a=localStorage.getItem("LIGHTRAG-API-TOKEN"),o={"Content-Type":"application/json",Accept:"application/x-ndjson"};a&&(o.Authorization=`Bearer ${a}`),n&&(o["X-API-Key"]=n);try{const l=await fetch(`${Kr}/query/stream`,{method:"POST",headers:o,body:JSON.stringify(e)});if(!l.ok){if(l.status===401)throw Ea.navigateToLogin(),new Error("Authentication required");let u="Unknown error";try{u=await l.text()}catch{}const d=`${Kr}/query/stream`;throw new Error(`${l.status} ${l.statusText}
${JSON.stringify({error:u})}
${d}`)}if(!l.body)throw new Error("Response body is null");const i=l.body.getReader(),s=new TextDecoder;let c="";for(;;){const{done:u,value:d}=await i.read();if(u)break;c+=s.decode(d,{stream:!0});const h=c.split(`
`);c=h.pop()||"";for(const f of h)if(f.trim())try{const b=JSON.parse(f);b.response?t(b.response):b.error&&r&&r(b.error)}catch(b){console.error("Error parsing stream chunk:",f,b),r&&r(`Error parsing server response: ${f}`)}}if(c.trim())try{const u=JSON.parse(c);u.response?t(u.response):u.error&&r&&r(u.error)}catch(u){console.error("Error parsing final chunk:",c,u),r&&r(`Error parsing final server response: ${c}`)}}catch(l){const i=rr(l);if(i==="Authentication required"){console.error("Authentication required for stream request"),r&&r("Authentication required");return}const s=i.match(/^(\d{3})\s/);if(s){const c=parseInt(s[1],10);let u=i;switch(c){case 403:u="You do not have permission to access this resource (403 Forbidden)",console.error("Permission denied for stream request:",i);break;case 404:u="The requested resource does not exist (404 Not Found)",console.error("Resource not found for stream request:",i);break;case 429:u="Too many requests, please try again later (429 Too Many Requests)",console.error("Rate limited for stream request:",i);break;case 500:case 502:case 503:case 504:u=`Server error, please try again later (${c})`,console.error("Server error for stream request:",i);break;default:console.error("Stream request failed with status code:",c,i)}r&&r(u);return}if(i.includes("NetworkError")||i.includes("Failed to fetch")||i.includes("Network request failed")){console.error("Network error for stream request:",i),r&&r("Network connection error, please check your internet connection");return}if(i.includes("Error parsing")||i.includes("SyntaxError")){console.error("JSON parsing error in stream:",i),r&&r("Error processing response data");return}console.error("Unhandled stream error:",i),r?r(i):console.error("No error handler provided for stream error:",i)}},Zg=async(e,t)=>{const r=new FormData;return r.append("file",e),(await xe.post("/documents/upload",r,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:t!==void 0?a=>{const o=Math.round(a.loaded*100/a.total);t(o)}:void 0})).data},ep=async()=>(await xe.delete("/documents")).data,tp=async e=>(await xe.post("/documents/clear_cache",{modes:e})).data,rp=async(e,t=!1)=>(await xe.delete("/documents/delete_document",{data:{doc_ids:e,delete_file:t}})).data,np=async()=>{try{const e=await xe.get("/auth-status",{timeout:5e3,headers:{Accept:"application/json"}});if((e.headers["content-type"]||"").includes("text/html"))return console.warn("Received HTML response instead of JSON for auth-status endpoint"),{auth_configured:!0,auth_mode:"enabled"};if(e.data&&typeof e.data=="object"&&"auth_configured"in e.data&&typeof e.data.auth_configured=="boolean"){if(e.data.auth_configured)return e.data;if(e.data.access_token&&typeof e.data.access_token=="string")return e.data;console.warn("Auth not configured but no valid access token provided")}return console.warn("Received invalid auth status response:",e.data),{auth_configured:!0,auth_mode:"enabled"}}catch(e){return console.error("Failed to get auth status:",rr(e)),{auth_configured:!0,auth_mode:"enabled"}}},op=async()=>(await xe.get("/documents/pipeline_status")).data,ap=async(e,t)=>{const r=new FormData;return r.append("username",e),r.append("password",t),(await xe.post("/login",r,{headers:{"Content-Type":"multipart/form-data"}})).data},il=async(e,t,r=!1)=>(await xe.post("/graph/entity/edit",{entity_name:e,updated_data:t,allow_rename:r})).data,ll=async(e,t,r)=>(await xe.post("/graph/relation/edit",{source_id:e,target_id:t,updated_data:r})).data,cl=async e=>{try{return(await xe.get(`/graph/entity/exists?name=${encodeURIComponent(e)}`)).data.exists}catch(t){return console.error("Error checking entity name:",t),!1}},ul=tr()(e=>({health:!0,message:null,messageTitle:null,lastCheckTime:Date.now(),status:null,pipelineBusy:!1,check:async()=>{var r;const t=await sl();if(t.status==="healthy"){if((t.core_version||t.api_version)&&qt.getState().setVersion(t.core_version||null,t.api_version||null),("webui_title"in t||"webui_description"in t)&&qt.getState().setCustomTitle("webui_title"in t?t.webui_title??null:null,"webui_description"in t?t.webui_description??null:null),(r=t.configuration)!=null&&r.max_graph_nodes){const n=parseInt(t.configuration.max_graph_nodes,10);!isNaN(n)&&n>0&&Z.getState().backendMaxGraphNodes!==n&&(Z.getState().setBackendMaxGraphNodes(n),Z.getState().graphMaxNodes>n&&Z.getState().setGraphMaxNodes(n,!0))}return e({health:!0,message:null,messageTitle:null,lastCheckTime:Date.now(),status:t,pipelineBusy:t.pipeline_busy}),!0}return e({health:!1,message:t.message,messageTitle:"Backend Health Check Error!",lastCheckTime:Date.now(),status:null}),!1},clear:()=>{e({health:!0,message:null,messageTitle:null})},setErrorMessage:(t,r)=>{e({health:!1,message:t,messageTitle:r})},setPipelineBusy:t=>{e({pipelineBusy:t})}})),En=_n(ul),ka=e=>{try{const t=e.split(".");return t.length!==3?{}:JSON.parse(atob(t[1]))}catch(t){return console.error("Error parsing token payload:",t),{}}},Ta=e=>ka(e).sub||null,dl=e=>ka(e).role==="guest",fl=()=>{const e=localStorage.getItem("LIGHTRAG-API-TOKEN"),t=localStorage.getItem("LIGHTRAG-CORE-VERSION"),r=localStorage.getItem("LIGHTRAG-API-VERSION"),n=localStorage.getItem("LIGHTRAG-WEBUI-TITLE"),a=localStorage.getItem("LIGHTRAG-WEBUI-DESCRIPTION"),o=e?Ta(e):null;return e?{isAuthenticated:!0,isGuestMode:dl(e),coreVersion:t,apiVersion:r,username:o,webuiTitle:n,webuiDescription:a}:{isAuthenticated:!1,isGuestMode:!1,coreVersion:t,apiVersion:r,username:null,webuiTitle:n,webuiDescription:a}},qt=tr(e=>{const t=fl();return{isAuthenticated:t.isAuthenticated,isGuestMode:t.isGuestMode,coreVersion:t.coreVersion,apiVersion:t.apiVersion,username:t.username,webuiTitle:t.webuiTitle,webuiDescription:t.webuiDescription,login:(r,n=!1,a=null,o=null,l=null,i=null)=>{localStorage.setItem("LIGHTRAG-API-TOKEN",r),a&&localStorage.setItem("LIGHTRAG-CORE-VERSION",a),o&&localStorage.setItem("LIGHTRAG-API-VERSION",o),l?localStorage.setItem("LIGHTRAG-WEBUI-TITLE",l):localStorage.removeItem("LIGHTRAG-WEBUI-TITLE"),i?localStorage.setItem("LIGHTRAG-WEBUI-DESCRIPTION",i):localStorage.removeItem("LIGHTRAG-WEBUI-DESCRIPTION");const s=Ta(r);e({isAuthenticated:!0,isGuestMode:n,username:s,coreVersion:a,apiVersion:o,webuiTitle:l,webuiDescription:i})},logout:()=>{localStorage.removeItem("LIGHTRAG-API-TOKEN");const r=localStorage.getItem("LIGHTRAG-CORE-VERSION"),n=localStorage.getItem("LIGHTRAG-API-VERSION"),a=localStorage.getItem("LIGHTRAG-WEBUI-TITLE"),o=localStorage.getItem("LIGHTRAG-WEBUI-DESCRIPTION");e({isAuthenticated:!1,isGuestMode:!1,username:null,coreVersion:r,apiVersion:n,webuiTitle:a,webuiDescription:o})},setVersion:(r,n)=>{r&&localStorage.setItem("LIGHTRAG-CORE-VERSION",r),n&&localStorage.setItem("LIGHTRAG-API-VERSION",n),e({coreVersion:r,apiVersion:n})},setCustomTitle:(r,n)=>{r?localStorage.setItem("LIGHTRAG-WEBUI-TITLE",r):localStorage.removeItem("LIGHTRAG-WEBUI-TITLE"),n?localStorage.setItem("LIGHTRAG-WEBUI-DESCRIPTION",n):localStorage.removeItem("LIGHTRAG-WEBUI-DESCRIPTION"),e({webuiTitle:r,webuiDescription:n})}}});var hl=e=>{switch(e){case"success":return ml;case"info":return yl;case"warning":return vl;case"error":return bl;default:return null}},gl=Array(12).fill(0),pl=({visible:e,className:t})=>W.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},W.createElement("div",{className:"sonner-spinner"},gl.map((r,n)=>W.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),ml=W.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},W.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),vl=W.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},W.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),yl=W.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},W.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),bl=W.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},W.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),wl=W.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},W.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),W.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),xl=()=>{let[e,t]=W.useState(document.hidden);return W.useEffect(()=>{let r=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",r),()=>window.removeEventListener("visibilitychange",r)},[]),e},en=1,Sl=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...n}=e,a=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:en++,o=this.toasts.find(i=>i.id===a),l=e.dismissible===void 0?!0:e.dismissible;return this.dismissedToasts.has(a)&&this.dismissedToasts.delete(a),o?this.toasts=this.toasts.map(i=>i.id===a?(this.publish({...i,...e,id:a,title:r}),{...i,...e,id:a,dismissible:l,title:r}):i):this.addToast({title:r,...n,dismissible:l,id:a}),a},this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach(t=>{this.subscribers.forEach(r=>r({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let r;t.loading!==void 0&&(r=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let n=e instanceof Promise?e:e(),a=r!==void 0,o,l=n.then(async s=>{if(o=["resolve",s],W.isValidElement(s))a=!1,this.create({id:r,type:"default",message:s});else if(El(s)&&!s.ok){a=!1;let c=typeof t.error=="function"?await t.error(`HTTP error! status: ${s.status}`):t.error,u=typeof t.description=="function"?await t.description(`HTTP error! status: ${s.status}`):t.description;this.create({id:r,type:"error",message:c,description:u})}else if(t.success!==void 0){a=!1;let c=typeof t.success=="function"?await t.success(s):t.success,u=typeof t.description=="function"?await t.description(s):t.description;this.create({id:r,type:"success",message:c,description:u})}}).catch(async s=>{if(o=["reject",s],t.error!==void 0){a=!1;let c=typeof t.error=="function"?await t.error(s):t.error,u=typeof t.description=="function"?await t.description(s):t.description;this.create({id:r,type:"error",message:c,description:u})}}).finally(()=>{var s;a&&(this.dismiss(r),r=void 0),(s=t.finally)==null||s.call(t)}),i=()=>new Promise((s,c)=>l.then(()=>o[0]==="reject"?c(o[1]):s(o[1])).catch(c));return typeof r!="string"&&typeof r!="number"?{unwrap:i}:Object.assign(r,{unwrap:i})},this.custom=(e,t)=>{let r=(t==null?void 0:t.id)||en++;return this.create({jsx:e(r),id:r,...t}),r},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},Ae=new Sl,_l=(e,t)=>{let r=(t==null?void 0:t.id)||en++;return Ae.addToast({title:e,...t,id:r}),r},El=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Cl=_l,kl=()=>Ae.toasts,Tl=()=>Ae.getActiveToasts(),rt=Object.assign(Cl,{success:Ae.success,info:Ae.info,warning:Ae.warning,error:Ae.error,custom:Ae.custom,message:Ae.message,promise:Ae.promise,dismiss:Ae.dismiss,loading:Ae.loading},{getHistory:kl,getToasts:Tl});function Rl(e,{insertAt:t}={}){if(typeof document>"u")return;let r=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",t==="top"&&r.firstChild?r.insertBefore(n,r.firstChild):r.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}Rl(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function It(e){return e.label!==void 0}var Al=3,jl="32px",Nl="16px",Qn=4e3,Il=356,Ll=14,Pl=20,zl=200;function Fe(...e){return e.filter(Boolean).join(" ")}function Dl(e){let[t,r]=e.split("-"),n=[];return t&&n.push(t),r&&n.push(r),n}var Ol=e=>{var t,r,n,a,o,l,i,s,c,u,d;let{invert:h,toast:f,unstyled:b,interacting:y,setHeights:k,visibleToasts:I,heights:E,index:j,toasts:A,expanded:N,removeToast:z,defaultRichColors:m,closeButton:_,style:x,cancelButtonStyle:T,actionButtonStyle:R,className:O="",descriptionClassName:w="",duration:H,position:K,gap:D,loadingIcon:C,expandByDefault:S,classNames:B,icons:ae,closeButtonAriaLabel:M="Close toast",pauseWhenPageIsHidden:v}=e,[P,V]=W.useState(null),[$,J]=W.useState(null),[X,Y]=W.useState(!1),[ie,ne]=W.useState(!1),[se,F]=W.useState(!1),[Q,q]=W.useState(!1),[U,L]=W.useState(!1),[oe,ue]=W.useState(0),[re,ee]=W.useState(0),G=W.useRef(f.duration||H||Qn),ge=W.useRef(null),pe=W.useRef(null),ye=j===0,we=j+1<=I,de=f.type,me=f.dismissible!==!1,Ne=f.className||"",ke=f.descriptionClassName||"",Te=W.useMemo(()=>E.findIndex(ce=>ce.toastId===f.id)||0,[E,f.id]),Pe=W.useMemo(()=>{var ce;return(ce=f.closeButton)!=null?ce:_},[f.closeButton,_]),Ye=W.useMemo(()=>f.duration||H||Qn,[f.duration,H]),Ke=W.useRef(0),Re=W.useRef(0),st=W.useRef(0),ze=W.useRef(null),[ii,li]=K.split("-"),Bn=W.useMemo(()=>E.reduce((ce,he,ve)=>ve>=Te?ce:ce+he.height,0),[E,Te]),Vn=xl(),ci=f.invert||h,hr=de==="loading";Re.current=W.useMemo(()=>Te*D+Bn,[Te,Bn]),W.useEffect(()=>{G.current=Ye},[Ye]),W.useEffect(()=>{Y(!0)},[]),W.useEffect(()=>{let ce=pe.current;if(ce){let he=ce.getBoundingClientRect().height;return ee(he),k(ve=>[{toastId:f.id,height:he,position:f.position},...ve]),()=>k(ve=>ve.filter(De=>De.toastId!==f.id))}},[k,f.id]),W.useLayoutEffect(()=>{if(!X)return;let ce=pe.current,he=ce.style.height;ce.style.height="auto";let ve=ce.getBoundingClientRect().height;ce.style.height=he,ee(ve),k(De=>De.find(Oe=>Oe.toastId===f.id)?De.map(Oe=>Oe.toastId===f.id?{...Oe,height:ve}:Oe):[{toastId:f.id,height:ve,position:f.position},...De])},[X,f.title,f.description,k,f.id]);let Qe=W.useCallback(()=>{ne(!0),ue(Re.current),k(ce=>ce.filter(he=>he.toastId!==f.id)),setTimeout(()=>{z(f)},zl)},[f,z,k,Re]);W.useEffect(()=>{if(f.promise&&de==="loading"||f.duration===1/0||f.type==="loading")return;let ce;return N||y||v&&Vn?(()=>{if(st.current<Ke.current){let he=new Date().getTime()-Ke.current;G.current=G.current-he}st.current=new Date().getTime()})():G.current!==1/0&&(Ke.current=new Date().getTime(),ce=setTimeout(()=>{var he;(he=f.onAutoClose)==null||he.call(f,f),Qe()},G.current)),()=>clearTimeout(ce)},[N,y,f,de,v,Vn,Qe]),W.useEffect(()=>{f.delete&&Qe()},[Qe,f.delete]);function ui(){var ce,he,ve;return ae!=null&&ae.loading?W.createElement("div",{className:Fe(B==null?void 0:B.loader,(ce=f==null?void 0:f.classNames)==null?void 0:ce.loader,"sonner-loader"),"data-visible":de==="loading"},ae.loading):C?W.createElement("div",{className:Fe(B==null?void 0:B.loader,(he=f==null?void 0:f.classNames)==null?void 0:he.loader,"sonner-loader"),"data-visible":de==="loading"},C):W.createElement(pl,{className:Fe(B==null?void 0:B.loader,(ve=f==null?void 0:f.classNames)==null?void 0:ve.loader),visible:de==="loading"})}return W.createElement("li",{tabIndex:0,ref:pe,className:Fe(O,Ne,B==null?void 0:B.toast,(t=f==null?void 0:f.classNames)==null?void 0:t.toast,B==null?void 0:B.default,B==null?void 0:B[de],(r=f==null?void 0:f.classNames)==null?void 0:r[de]),"data-sonner-toast":"","data-rich-colors":(n=f.richColors)!=null?n:m,"data-styled":!(f.jsx||f.unstyled||b),"data-mounted":X,"data-promise":!!f.promise,"data-swiped":U,"data-removed":ie,"data-visible":we,"data-y-position":ii,"data-x-position":li,"data-index":j,"data-front":ye,"data-swiping":se,"data-dismissible":me,"data-type":de,"data-invert":ci,"data-swipe-out":Q,"data-swipe-direction":$,"data-expanded":!!(N||S&&X),style:{"--index":j,"--toasts-before":j,"--z-index":A.length-j,"--offset":`${ie?oe:Re.current}px`,"--initial-height":S?"auto":`${re}px`,...x,...f.style},onDragEnd:()=>{F(!1),V(null),ze.current=null},onPointerDown:ce=>{hr||!me||(ge.current=new Date,ue(Re.current),ce.target.setPointerCapture(ce.pointerId),ce.target.tagName!=="BUTTON"&&(F(!0),ze.current={x:ce.clientX,y:ce.clientY}))},onPointerUp:()=>{var ce,he,ve,De;if(Q||!me)return;ze.current=null;let Oe=Number(((ce=pe.current)==null?void 0:ce.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),Je=Number(((he=pe.current)==null?void 0:he.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),it=new Date().getTime()-((ve=ge.current)==null?void 0:ve.getTime()),Ge=P==="x"?Oe:Je,Ze=Math.abs(Ge)/it;if(Math.abs(Ge)>=Pl||Ze>.11){ue(Re.current),(De=f.onDismiss)==null||De.call(f,f),J(P==="x"?Oe>0?"right":"left":Je>0?"down":"up"),Qe(),q(!0),L(!1);return}F(!1),V(null)},onPointerMove:ce=>{var he,ve,De,Oe;if(!ze.current||!me||((he=window.getSelection())==null?void 0:he.toString().length)>0)return;let Je=ce.clientY-ze.current.y,it=ce.clientX-ze.current.x,Ge=(ve=e.swipeDirections)!=null?ve:Dl(K);!P&&(Math.abs(it)>1||Math.abs(Je)>1)&&V(Math.abs(it)>Math.abs(Je)?"x":"y");let Ze={x:0,y:0};P==="y"?(Ge.includes("top")||Ge.includes("bottom"))&&(Ge.includes("top")&&Je<0||Ge.includes("bottom")&&Je>0)&&(Ze.y=Je):P==="x"&&(Ge.includes("left")||Ge.includes("right"))&&(Ge.includes("left")&&it<0||Ge.includes("right")&&it>0)&&(Ze.x=it),(Math.abs(Ze.x)>0||Math.abs(Ze.y)>0)&&L(!0),(De=pe.current)==null||De.style.setProperty("--swipe-amount-x",`${Ze.x}px`),(Oe=pe.current)==null||Oe.style.setProperty("--swipe-amount-y",`${Ze.y}px`)}},Pe&&!f.jsx?W.createElement("button",{"aria-label":M,"data-disabled":hr,"data-close-button":!0,onClick:hr||!me?()=>{}:()=>{var ce;Qe(),(ce=f.onDismiss)==null||ce.call(f,f)},className:Fe(B==null?void 0:B.closeButton,(a=f==null?void 0:f.classNames)==null?void 0:a.closeButton)},(o=ae==null?void 0:ae.close)!=null?o:wl):null,f.jsx||p.isValidElement(f.title)?f.jsx?f.jsx:typeof f.title=="function"?f.title():f.title:W.createElement(W.Fragment,null,de||f.icon||f.promise?W.createElement("div",{"data-icon":"",className:Fe(B==null?void 0:B.icon,(l=f==null?void 0:f.classNames)==null?void 0:l.icon)},f.promise||f.type==="loading"&&!f.icon?f.icon||ui():null,f.type!=="loading"?f.icon||(ae==null?void 0:ae[de])||hl(de):null):null,W.createElement("div",{"data-content":"",className:Fe(B==null?void 0:B.content,(i=f==null?void 0:f.classNames)==null?void 0:i.content)},W.createElement("div",{"data-title":"",className:Fe(B==null?void 0:B.title,(s=f==null?void 0:f.classNames)==null?void 0:s.title)},typeof f.title=="function"?f.title():f.title),f.description?W.createElement("div",{"data-description":"",className:Fe(w,ke,B==null?void 0:B.description,(c=f==null?void 0:f.classNames)==null?void 0:c.description)},typeof f.description=="function"?f.description():f.description):null),p.isValidElement(f.cancel)?f.cancel:f.cancel&&It(f.cancel)?W.createElement("button",{"data-button":!0,"data-cancel":!0,style:f.cancelButtonStyle||T,onClick:ce=>{var he,ve;It(f.cancel)&&me&&((ve=(he=f.cancel).onClick)==null||ve.call(he,ce),Qe())},className:Fe(B==null?void 0:B.cancelButton,(u=f==null?void 0:f.classNames)==null?void 0:u.cancelButton)},f.cancel.label):null,p.isValidElement(f.action)?f.action:f.action&&It(f.action)?W.createElement("button",{"data-button":!0,"data-action":!0,style:f.actionButtonStyle||R,onClick:ce=>{var he,ve;It(f.action)&&((ve=(he=f.action).onClick)==null||ve.call(he,ce),!ce.defaultPrevented&&Qe())},className:Fe(B==null?void 0:B.actionButton,(d=f==null?void 0:f.classNames)==null?void 0:d.actionButton)},f.action.label):null))};function Jn(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}function Gl(e,t){let r={};return[e,t].forEach((n,a)=>{let o=a===1,l=o?"--mobile-offset":"--offset",i=o?Nl:jl;function s(c){["top","right","bottom","left"].forEach(u=>{r[`${l}-${u}`]=typeof c=="number"?`${c}px`:c})}typeof n=="number"||typeof n=="string"?s(n):typeof n=="object"?["top","right","bottom","left"].forEach(c=>{n[c]===void 0?r[`${l}-${c}`]=i:r[`${l}-${c}`]=typeof n[c]=="number"?`${n[c]}px`:n[c]}):s(i)}),r}var sp=p.forwardRef(function(e,t){let{invert:r,position:n="bottom-right",hotkey:a=["altKey","KeyT"],expand:o,closeButton:l,className:i,offset:s,mobileOffset:c,theme:u="light",richColors:d,duration:h,style:f,visibleToasts:b=Al,toastOptions:y,dir:k=Jn(),gap:I=Ll,loadingIcon:E,icons:j,containerAriaLabel:A="Notifications",pauseWhenPageIsHidden:N}=e,[z,m]=W.useState([]),_=W.useMemo(()=>Array.from(new Set([n].concat(z.filter(v=>v.position).map(v=>v.position)))),[z,n]),[x,T]=W.useState([]),[R,O]=W.useState(!1),[w,H]=W.useState(!1),[K,D]=W.useState(u!=="system"?u:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),C=W.useRef(null),S=a.join("+").replace(/Key/g,"").replace(/Digit/g,""),B=W.useRef(null),ae=W.useRef(!1),M=W.useCallback(v=>{m(P=>{var V;return(V=P.find($=>$.id===v.id))!=null&&V.delete||Ae.dismiss(v.id),P.filter(({id:$})=>$!==v.id)})},[]);return W.useEffect(()=>Ae.subscribe(v=>{if(v.dismiss){m(P=>P.map(V=>V.id===v.id?{...V,delete:!0}:V));return}setTimeout(()=>{hi.flushSync(()=>{m(P=>{let V=P.findIndex($=>$.id===v.id);return V!==-1?[...P.slice(0,V),{...P[V],...v},...P.slice(V+1)]:[v,...P]})})})}),[]),W.useEffect(()=>{if(u!=="system"){D(u);return}if(u==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?D("dark"):D("light")),typeof window>"u")return;let v=window.matchMedia("(prefers-color-scheme: dark)");try{v.addEventListener("change",({matches:P})=>{D(P?"dark":"light")})}catch{v.addListener(({matches:V})=>{try{D(V?"dark":"light")}catch($){console.error($)}})}},[u]),W.useEffect(()=>{z.length<=1&&O(!1)},[z]),W.useEffect(()=>{let v=P=>{var V,$;a.every(J=>P[J]||P.code===J)&&(O(!0),(V=C.current)==null||V.focus()),P.code==="Escape"&&(document.activeElement===C.current||($=C.current)!=null&&$.contains(document.activeElement))&&O(!1)};return document.addEventListener("keydown",v),()=>document.removeEventListener("keydown",v)},[a]),W.useEffect(()=>{if(C.current)return()=>{B.current&&(B.current.focus({preventScroll:!0}),B.current=null,ae.current=!1)}},[C.current]),W.createElement("section",{ref:t,"aria-label":`${A} ${S}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},_.map((v,P)=>{var V;let[$,J]=v.split("-");return z.length?W.createElement("ol",{key:v,dir:k==="auto"?Jn():k,tabIndex:-1,ref:C,className:i,"data-sonner-toaster":!0,"data-theme":K,"data-y-position":$,"data-lifted":R&&z.length>1&&!o,"data-x-position":J,style:{"--front-toast-height":`${((V=x[0])==null?void 0:V.height)||0}px`,"--width":`${Il}px`,"--gap":`${I}px`,...f,...Gl(s,c)},onBlur:X=>{ae.current&&!X.currentTarget.contains(X.relatedTarget)&&(ae.current=!1,B.current&&(B.current.focus({preventScroll:!0}),B.current=null))},onFocus:X=>{X.target instanceof HTMLElement&&X.target.dataset.dismissible==="false"||ae.current||(ae.current=!0,B.current=X.relatedTarget)},onMouseEnter:()=>O(!0),onMouseMove:()=>O(!0),onMouseLeave:()=>{w||O(!1)},onDragEnd:()=>O(!1),onPointerDown:X=>{X.target instanceof HTMLElement&&X.target.dataset.dismissible==="false"||H(!0)},onPointerUp:()=>H(!1)},z.filter(X=>!X.position&&P===0||X.position===v).map((X,Y)=>{var ie,ne;return W.createElement(Ol,{key:X.id,icons:j,index:Y,toast:X,defaultRichColors:d,duration:(ie=y==null?void 0:y.duration)!=null?ie:h,className:y==null?void 0:y.className,descriptionClassName:y==null?void 0:y.descriptionClassName,invert:r,visibleToasts:b,closeButton:(ne=y==null?void 0:y.closeButton)!=null?ne:l,interacting:w,position:v,style:y==null?void 0:y.style,unstyled:y==null?void 0:y.unstyled,classNames:y==null?void 0:y.classNames,cancelButtonStyle:y==null?void 0:y.cancelButtonStyle,actionButtonStyle:y==null?void 0:y.actionButtonStyle,removeToast:M,toasts:z.filter(se=>se.position==X.position),heights:x.filter(se=>se.position==X.position),setHeights:T,expandByDefault:o,gap:I,loadingIcon:E,expanded:R,pauseWhenPageIsHidden:N,swipeDirections:e.swipeDirections})})):null}))});const Fl={theme:"system",setTheme:()=>null},Ra=p.createContext(Fl);function ip({children:e,...t}){const r=Z.use.theme(),n=Z.use.setTheme();p.useEffect(()=>{const o=window.document.documentElement;if(o.classList.remove("light","dark"),r==="system"){const l=window.matchMedia("(prefers-color-scheme: dark)"),i=s=>{o.classList.remove("light","dark"),o.classList.add(s.matches?"dark":"light")};return o.classList.add(l.matches?"dark":"light"),l.addEventListener("change",i),()=>l.removeEventListener("change",i)}else o.classList.add(r)},[r]);const a={theme:r,setTheme:n};return g.jsx(Ra.Provider,{...t,value:a,children:e})}const Ml=(e,t,r,n)=>{var o,l,i,s;const a=[r,{code:t,...n||{}}];if((l=(o=e==null?void 0:e.services)==null?void 0:o.logger)!=null&&l.forward)return e.services.logger.forward(a,"warn","react-i18next::",!0);ht(a[0])&&(a[0]=`react-i18next:: ${a[0]}`),(s=(i=e==null?void 0:e.services)==null?void 0:i.logger)!=null&&s.warn?e.services.logger.warn(...a):console!=null&&console.warn&&console.warn(...a)},Zn={},tn=(e,t,r,n)=>{ht(r)&&Zn[r]||(ht(r)&&(Zn[r]=new Date),Ml(e,t,r,n))},Aa=(e,t)=>()=>{if(e.isInitialized)t();else{const r=()=>{setTimeout(()=>{e.off("initialized",r)},0),t()};e.on("initialized",r)}},rn=(e,t,r)=>{e.loadNamespaces(t,Aa(e,r))},eo=(e,t,r,n)=>{if(ht(r)&&(r=[r]),e.options.preload&&e.options.preload.indexOf(t)>-1)return rn(e,r,n);r.forEach(a=>{e.options.ns.indexOf(a)<0&&e.options.ns.push(a)}),e.loadLanguages(t,Aa(e,n))},$l=(e,t,r={})=>!t.languages||!t.languages.length?(tn(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0):t.hasLoadedNamespace(e,{lng:r.lng,precheck:(n,a)=>{var o;if(((o=r.bindI18n)==null?void 0:o.indexOf("languageChanging"))>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!a(n.isLanguageChangingTo,e))return!1}}),ht=e=>typeof e=="string",Hl=e=>typeof e=="object"&&e!==null,Bl=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Vl={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Ul=e=>Vl[e],ql=e=>e.replace(Bl,Ul);let nn={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:ql};const Wl=(e={})=>{nn={...nn,...e}},Xl=()=>nn;let ja;const Yl=e=>{ja=e},Kl=()=>ja,lp={type:"3rdParty",init(e){Wl(e.options.react),Yl(e)}},Ql=p.createContext();class Jl{constructor(){this.usedNamespaces={}}addUsedNamespaces(t){t.forEach(r=>{this.usedNamespaces[r]||(this.usedNamespaces[r]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const Zl=(e,t)=>{const r=p.useRef();return p.useEffect(()=>{r.current=e},[e,t]),r.current},Na=(e,t,r,n)=>e.getFixedT(t,r,n),ec=(e,t,r,n)=>p.useCallback(Na(e,t,r,n),[e,t,r,n]),Se=(e,t={})=>{var A,N,z,m;const{i18n:r}=t,{i18n:n,defaultNS:a}=p.useContext(Ql)||{},o=r||n||Kl();if(o&&!o.reportNamespaces&&(o.reportNamespaces=new Jl),!o){tn(o,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const _=(T,R)=>ht(R)?R:Hl(R)&&ht(R.defaultValue)?R.defaultValue:Array.isArray(T)?T[T.length-1]:T,x=[_,{},!1];return x.t=_,x.i18n={},x.ready=!1,x}(A=o.options.react)!=null&&A.wait&&tn(o,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const l={...Xl(),...o.options.react,...t},{useSuspense:i,keyPrefix:s}=l;let c=a||((N=o.options)==null?void 0:N.defaultNS);c=ht(c)?[c]:c||["translation"],(m=(z=o.reportNamespaces).addUsedNamespaces)==null||m.call(z,c);const u=(o.isInitialized||o.initializedStoreOnce)&&c.every(_=>$l(_,o,l)),d=ec(o,t.lng||null,l.nsMode==="fallback"?c:c[0],s),h=()=>d,f=()=>Na(o,t.lng||null,l.nsMode==="fallback"?c:c[0],s),[b,y]=p.useState(h);let k=c.join();t.lng&&(k=`${t.lng}${k}`);const I=Zl(k),E=p.useRef(!0);p.useEffect(()=>{const{bindI18n:_,bindI18nStore:x}=l;E.current=!0,!u&&!i&&(t.lng?eo(o,t.lng,c,()=>{E.current&&y(f)}):rn(o,c,()=>{E.current&&y(f)})),u&&I&&I!==k&&E.current&&y(f);const T=()=>{E.current&&y(f)};return _&&(o==null||o.on(_,T)),x&&(o==null||o.store.on(x,T)),()=>{E.current=!1,o&&(_==null||_.split(" ").forEach(R=>o.off(R,T))),x&&o&&x.split(" ").forEach(R=>o.store.off(R,T))}},[o,k]),p.useEffect(()=>{E.current&&u&&y(h)},[o,s,u]);const j=[b,o,u];if(j.t=b,j.i18n=o,j.ready=u,u||!u&&!i)return j;throw new Promise(_=>{t.lng?eo(o,t.lng,c,()=>_()):rn(o,c,()=>_())})},to=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,ro=Sa,tc=(e,t)=>r=>{var n;if((t==null?void 0:t.variants)==null)return ro(e,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:a,defaultVariants:o}=t,l=Object.keys(a).map(c=>{const u=r==null?void 0:r[c],d=o==null?void 0:o[c];if(u===null)return null;const h=to(u)||to(d);return a[c][h]}),i=r&&Object.entries(r).reduce((c,u)=>{let[d,h]=u;return h===void 0||(c[d]=h),c},{}),s=t==null||(n=t.compoundVariants)===null||n===void 0?void 0:n.reduce((c,u)=>{let{class:d,className:h,...f}=u;return Object.entries(f).every(b=>{let[y,k]=b;return Array.isArray(k)?k.includes({...o,...i}[y]):{...o,...i}[y]===k})?[...c,d,h]:c},[]);return ro(e,l,s,r==null?void 0:r.class,r==null?void 0:r.className)};var[nr,cp]=wn("Tooltip",[pa]),or=pa(),Ia="TooltipProvider",rc=700,on="tooltip.open",[nc,Cn]=nr(Ia),La=e=>{const{__scopeTooltip:t,delayDuration:r=rc,skipDelayDuration:n=300,disableHoverableContent:a=!1,children:o}=e,[l,i]=p.useState(!0),s=p.useRef(!1),c=p.useRef(0);return p.useEffect(()=>{const u=c.current;return()=>window.clearTimeout(u)},[]),g.jsx(nc,{scope:t,isOpenDelayed:l,delayDuration:r,onOpen:p.useCallback(()=>{window.clearTimeout(c.current),i(!1)},[]),onClose:p.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>i(!0),n)},[n]),isPointerInTransitRef:s,onPointerInTransitChange:p.useCallback(u=>{s.current=u},[]),disableHoverableContent:a,children:o})};La.displayName=Ia;var ar="Tooltip",[oc,sr]=nr(ar),Pa=e=>{const{__scopeTooltip:t,children:r,open:n,defaultOpen:a=!1,onOpenChange:o,disableHoverableContent:l,delayDuration:i}=e,s=Cn(ar,e.__scopeTooltip),c=or(t),[u,d]=p.useState(null),h=ft(),f=p.useRef(0),b=l??s.disableHoverableContent,y=i??s.delayDuration,k=p.useRef(!1),[I=!1,E]=ma({prop:n,defaultProp:a,onChange:m=>{m?(s.onOpen(),document.dispatchEvent(new CustomEvent(on))):s.onClose(),o==null||o(m)}}),j=p.useMemo(()=>I?k.current?"delayed-open":"instant-open":"closed",[I]),A=p.useCallback(()=>{window.clearTimeout(f.current),f.current=0,k.current=!1,E(!0)},[E]),N=p.useCallback(()=>{window.clearTimeout(f.current),f.current=0,E(!1)},[E]),z=p.useCallback(()=>{window.clearTimeout(f.current),f.current=window.setTimeout(()=>{k.current=!0,E(!0),f.current=0},y)},[y,E]);return p.useEffect(()=>()=>{f.current&&(window.clearTimeout(f.current),f.current=0)},[]),g.jsx(Ri,{...c,children:g.jsx(oc,{scope:t,contentId:h,open:I,stateAttribute:j,trigger:u,onTriggerChange:d,onTriggerEnter:p.useCallback(()=>{s.isOpenDelayed?z():A()},[s.isOpenDelayed,z,A]),onTriggerLeave:p.useCallback(()=>{b?N():(window.clearTimeout(f.current),f.current=0)},[N,b]),onOpen:A,onClose:N,disableHoverableContent:b,children:r})})};Pa.displayName=ar;var an="TooltipTrigger",za=p.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,a=sr(an,r),o=Cn(an,r),l=or(r),i=p.useRef(null),s=Xe(t,i,a.onTriggerChange),c=p.useRef(!1),u=p.useRef(!1),d=p.useCallback(()=>c.current=!1,[]);return p.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),g.jsx(Ai,{asChild:!0,...l,children:g.jsx(Ee.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...n,ref:s,onPointerMove:Ce(e.onPointerMove,h=>{h.pointerType!=="touch"&&!u.current&&!o.isPointerInTransitRef.current&&(a.onTriggerEnter(),u.current=!0)}),onPointerLeave:Ce(e.onPointerLeave,()=>{a.onTriggerLeave(),u.current=!1}),onPointerDown:Ce(e.onPointerDown,()=>{c.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:Ce(e.onFocus,()=>{c.current||a.onOpen()}),onBlur:Ce(e.onBlur,a.onClose),onClick:Ce(e.onClick,a.onClose)})})});za.displayName=an;var ac="TooltipPortal",[up,sc]=nr(ac,{forceMount:void 0}),wt="TooltipContent",Da=p.forwardRef((e,t)=>{const r=sc(wt,e.__scopeTooltip),{forceMount:n=r.forceMount,side:a="top",...o}=e,l=sr(wt,e.__scopeTooltip);return g.jsx(St,{present:n||l.open,children:l.disableHoverableContent?g.jsx(Oa,{side:a,...o,ref:t}):g.jsx(ic,{side:a,...o,ref:t})})}),ic=p.forwardRef((e,t)=>{const r=sr(wt,e.__scopeTooltip),n=Cn(wt,e.__scopeTooltip),a=p.useRef(null),o=Xe(t,a),[l,i]=p.useState(null),{trigger:s,onClose:c}=r,u=a.current,{onPointerInTransitChange:d}=n,h=p.useCallback(()=>{i(null),d(!1)},[d]),f=p.useCallback((b,y)=>{const k=b.currentTarget,I={x:b.clientX,y:b.clientY},E=dc(I,k.getBoundingClientRect()),j=fc(I,E),A=hc(y.getBoundingClientRect()),N=pc([...j,...A]);i(N),d(!0)},[d]);return p.useEffect(()=>()=>h(),[h]),p.useEffect(()=>{if(s&&u){const b=k=>f(k,u),y=k=>f(k,s);return s.addEventListener("pointerleave",b),u.addEventListener("pointerleave",y),()=>{s.removeEventListener("pointerleave",b),u.removeEventListener("pointerleave",y)}}},[s,u,f,h]),p.useEffect(()=>{if(l){const b=y=>{const k=y.target,I={x:y.clientX,y:y.clientY},E=(s==null?void 0:s.contains(k))||(u==null?void 0:u.contains(k)),j=!gc(I,l);E?h():j&&(h(),c())};return document.addEventListener("pointermove",b),()=>document.removeEventListener("pointermove",b)}},[s,u,l,c,h]),g.jsx(Oa,{...e,ref:o})}),[lc,cc]=nr(ar,{isInside:!1}),Oa=p.forwardRef((e,t)=>{const{__scopeTooltip:r,children:n,"aria-label":a,onEscapeKeyDown:o,onPointerDownOutside:l,...i}=e,s=sr(wt,r),c=or(r),{onClose:u}=s;return p.useEffect(()=>(document.addEventListener(on,u),()=>document.removeEventListener(on,u)),[u]),p.useEffect(()=>{if(s.trigger){const d=h=>{const f=h.target;f!=null&&f.contains(s.trigger)&&u()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[s.trigger,u]),g.jsx(Ei,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:d=>d.preventDefault(),onDismiss:u,children:g.jsxs(Ci,{"data-state":s.stateAttribute,...c,...i,ref:t,style:{...i.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[g.jsx(ki,{children:n}),g.jsx(lc,{scope:r,isInside:!0,children:g.jsx(Ti,{id:s.contentId,role:"tooltip",children:a||n})})]})})});Da.displayName=wt;var Ga="TooltipArrow",uc=p.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,a=or(r);return cc(Ga,r).isInside?null:g.jsx(ji,{...a,...n,ref:t})});uc.displayName=Ga;function dc(e,t){const r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),a=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(r,n,a,o)){case o:return"left";case a:return"right";case r:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function fc(e,t,r=5){const n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return n}function hc(e){const{top:t,right:r,bottom:n,left:a}=e;return[{x:a,y:t},{x:r,y:t},{x:r,y:n},{x:a,y:n}]}function gc(e,t){const{x:r,y:n}=e;let a=!1;for(let o=0,l=t.length-1;o<t.length;l=o++){const i=t[o].x,s=t[o].y,c=t[l].x,u=t[l].y;s>n!=u>n&&r<(c-i)*(n-s)/(u-s)+i&&(a=!a)}return a}function pc(e){const t=e.slice();return t.sort((r,n)=>r.x<n.x?-1:r.x>n.x?1:r.y<n.y?-1:r.y>n.y?1:0),mc(t)}function mc(e){if(e.length<=1)return e.slice();const t=[];for(let n=0;n<e.length;n++){const a=e[n];for(;t.length>=2;){const o=t[t.length-1],l=t[t.length-2];if((o.x-l.x)*(a.y-l.y)>=(o.y-l.y)*(a.x-l.x))t.pop();else break}t.push(a)}t.pop();const r=[];for(let n=e.length-1;n>=0;n--){const a=e[n];for(;r.length>=2;){const o=r[r.length-1],l=r[r.length-2];if((o.x-l.x)*(a.y-l.y)>=(o.y-l.y)*(a.x-l.x))r.pop();else break}r.push(a)}return r.pop(),t.length===1&&r.length===1&&t[0].x===r[0].x&&t[0].y===r[0].y?t:t.concat(r)}var vc=La,yc=Pa,bc=za,Fa=Da;const Ma=vc,$a=yc,Ha=bc,wc=e=>typeof e!="string"?e:g.jsx("div",{className:"relative top-0 pt-1 whitespace-pre-wrap break-words",children:e}),kn=p.forwardRef(({className:e,side:t="left",align:r="start",children:n,...a},o)=>{const l=p.useRef(null);return p.useEffect(()=>{l.current&&(l.current.scrollTop=0)},[n]),g.jsx(Fa,{ref:o,side:t,align:r,className:fe("bg-popover text-popover-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 max-h-[60vh] overflow-y-auto whitespace-pre-wrap break-words rounded-md border px-3 py-2 text-sm shadow-md z-60",e),...a,children:typeof n=="string"?wc(n):n})});kn.displayName=Fa.displayName;const no=tc("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"size-8"}},defaultVariants:{variant:"default",size:"default"}}),be=p.forwardRef(({className:e,variant:t,tooltip:r,size:n,side:a="right",asChild:o=!1,...l},i)=>{const s=o?Ni:"button";return r?g.jsx(Ma,{children:g.jsxs($a,{children:[g.jsx(Ha,{asChild:!0,children:g.jsx(s,{className:fe(no({variant:t,size:n,className:e}),"cursor-pointer"),ref:i,...l})}),g.jsx(kn,{side:a,children:r})]})}):g.jsx(s,{className:fe(no({variant:t,size:n,className:e}),"cursor-pointer"),ref:i,...l})});be.displayName="Button";const Wt=p.forwardRef(({className:e,type:t,...r},n)=>g.jsx("input",{type:t,className:fe("border-input file:text-foreground placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 rounded-md border bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm [&::-webkit-inner-spin-button]:opacity-50 [&::-webkit-outer-spin-button]:opacity-50",e),ref:n,...r}));Wt.displayName="Input";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xc=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Ba=(...e)=>e.filter((t,r,n)=>!!t&&t.trim()!==""&&n.indexOf(t)===r).join(" ").trim();/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Sc={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _c=p.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:a="",children:o,iconNode:l,...i},s)=>p.createElement("svg",{ref:s,...Sc,width:t,height:t,stroke:e,strokeWidth:n?Number(r)*24/Number(t):r,className:Ba("lucide",a),...i},[...l.map(([c,u])=>p.createElement(c,u)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=(e,t)=>{const r=p.forwardRef(({className:n,...a},o)=>p.createElement(_c,{ref:o,iconNode:t,className:Ba(`lucide-${xc(e)}`,n),...a}));return r.displayName=`${e}`,r};/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ec=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],dp=le("Activity",Ec);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cc=[["path",{d:"M17 12H7",key:"16if0g"}],["path",{d:"M19 18H5",key:"18s9l3"}],["path",{d:"M21 6H3",key:"1jwq7v"}]],fp=le("AlignCenter",Cc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kc=[["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M17 18H3",key:"1amg6g"}],["path",{d:"M21 6H3",key:"1jwq7v"}]],hp=le("AlignLeft",kc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tc=[["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M21 18H7",key:"1ygte8"}],["path",{d:"M21 6H3",key:"1jwq7v"}]],gp=le("AlignRight",Tc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rc=[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]],pp=le("ArrowDown",Rc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ac=[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]],mp=le("ArrowUp",Ac);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jc=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],Nc=le("BookOpen",jc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ic=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Va=le("Check",Ic);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lc=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],vp=le("ChevronDown",Lc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pc=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],yp=le("ChevronUp",Pc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zc=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],Dc=le("ChevronsUpDown",zc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oc=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],bp=le("CircleAlert",Oc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gc=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],wp=le("Copy",Gc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fc=[["path",{d:"m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21",key:"182aya"}],["path",{d:"M22 21H7",key:"t4ddhn"}],["path",{d:"m5 11 9 9",key:"1mo9qw"}]],xp=le("Eraser",Fc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mc=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],Sp=le("FileText",Mc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $c=[["path",{d:"M20 7h-3a2 2 0 0 1-2-2V2",key:"x099mo"}],["path",{d:"M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z",key:"18t6ie"}],["path",{d:"M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8",key:"1nja0z"}]],_p=le("Files",$c);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hc=[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]],Ep=le("Filter",Hc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bc=[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["rect",{width:"10",height:"8",x:"7",y:"8",rx:"1",key:"vys8me"}]],Vc=le("Fullscreen",Bc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uc=[["path",{d:"M6 3v12",key:"qpgusn"}],["path",{d:"M18 9a3 3 0 1 0 0-6 3 3 0 0 0 0 6z",key:"1d02ji"}],["path",{d:"M6 21a3 3 0 1 0 0-6 3 3 0 0 0 0 6z",key:"chk6ph"}],["path",{d:"M15 6a9 9 0 0 0-9 9",key:"or332x"}],["path",{d:"M18 15v6",key:"9wciyi"}],["path",{d:"M21 18h-6",key:"139f0c"}]],qc=le("GitBranchPlus",Uc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wc=[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]],Cp=le("Github",Wc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xc=[["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"19",cy:"5",r:"1",key:"w8mnmm"}],["circle",{cx:"5",cy:"5",r:"1",key:"lttvr7"}],["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}],["circle",{cx:"19",cy:"19",r:"1",key:"shf9b7"}],["circle",{cx:"5",cy:"19",r:"1",key:"bfqh0e"}]],Yc=le("Grip",Xc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kc=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],Ua=le("LoaderCircle",Kc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qc=[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]],kp=le("Loader",Qc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jc=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],Tp=le("LogOut",Jc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zc=[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]],eu=le("Maximize",Zc);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tu=[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]],ru=le("Minimize",tu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nu=[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]],Rp=le("Palette",nu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ou=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],au=le("Pause",ou);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const su=[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]],iu=le("Pencil",su);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lu=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],cu=le("Play",lu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uu=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],du=le("RefreshCw",uu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fu=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],hu=le("RotateCcw",fu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gu=[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]],pu=le("RotateCw",gu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mu=[["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["path",{d:"M8.12 8.12 12 12",key:"1alkpv"}],["path",{d:"M20 4 8.12 15.88",key:"xgtan2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M14.8 14.8 20 20",key:"ptml3r"}]],vu=le("Scissors",mu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yu=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]],bu=le("Search",yu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wu=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],Ap=le("Send",wu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xu=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Su=le("Settings",xu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _u=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]],jp=le("Trash",_u);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eu=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],Np=le("TriangleAlert",Eu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cu=[["path",{d:"M9 14 4 9l5-5",key:"102s5s"}],["path",{d:"M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11",key:"f3b9sd"}]],qa=le("Undo2",Cu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ku=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]],Ip=le("Upload",ku);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tu=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Ru=le("X",Tu);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Au=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],Lp=le("Zap",Au);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ju=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]],Nu=le("ZoomIn",ju);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iu=[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]],Lu=le("ZoomOut",Iu),Pu=wa,Pp=Li,zu=va,Wa=p.forwardRef(({className:e,...t},r)=>g.jsx(xn,{ref:r,className:fe("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/30",e),...t}));Wa.displayName=xn.displayName;const Xa=p.forwardRef(({className:e,children:t,...r},n)=>g.jsxs(zu,{children:[g.jsx(Wa,{}),g.jsxs(Sn,{ref:n,className:fe("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg",e),...r,children:[t,g.jsxs(Ii,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:pointer-events-none",children:[g.jsx(Ru,{className:"h-4 w-4"}),g.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Xa.displayName=Sn.displayName;const Ya=({className:e,...t})=>g.jsx("div",{className:fe("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});Ya.displayName="DialogHeader";const Ka=({className:e,...t})=>g.jsx("div",{className:fe("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});Ka.displayName="DialogFooter";const Qa=p.forwardRef(({className:e,...t},r)=>g.jsx(ya,{ref:r,className:fe("text-lg leading-none font-semibold tracking-tight",e),...t}));Qa.displayName=ya.displayName;const Ja=p.forwardRef(({className:e,...t},r)=>g.jsx(ba,{ref:r,className:fe("text-muted-foreground text-sm",e),...t}));Ja.displayName=ba.displayName;const Tn=zi,Rn=Di,ir=p.forwardRef(({className:e,align:t="center",sideOffset:r=4,collisionPadding:n,sticky:a,avoidCollisions:o=!1,...l},i)=>g.jsx(Pi,{children:g.jsx(xa,{ref:i,align:t,sideOffset:r,collisionPadding:n,sticky:a,avoidCollisions:o,className:fe("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 rounded-md border p-4 shadow-md outline-none",e),...l})}));ir.displayName=xa.displayName;var Du=`
precision mediump float;

varying vec4 v_color;
varying float v_border;

const float radius = 0.5;
const vec4 transparent = vec4(0.0, 0.0, 0.0, 0.0);

void main(void) {
  vec2 m = gl_PointCoord - vec2(0.5, 0.5);
  float dist = radius - length(m);

  // No antialiasing for picking mode:
  #ifdef PICKING_MODE
  if (dist > v_border)
    gl_FragColor = v_color;
  else
    gl_FragColor = transparent;

  #else
  float t = 0.0;
  if (dist > v_border)
    t = 1.0;
  else if (dist > 0.0)
    t = dist / v_border;

  gl_FragColor = mix(transparent, v_color, t);
  #endif
}
`,Ou=Du,Gu=`
attribute vec4 a_id;
attribute vec4 a_color;
attribute vec2 a_position;
attribute float a_size;

uniform float u_sizeRatio;
uniform float u_pixelRatio;
uniform mat3 u_matrix;

varying vec4 v_color;
varying float v_border;

const float bias = 255.0 / 254.0;

void main() {
  gl_Position = vec4(
    (u_matrix * vec3(a_position, 1)).xy,
    0,
    1
  );

  // Multiply the point size twice:
  //  - x SCALING_RATIO to correct the canvas scaling
  //  - x 2 to correct the formulae
  gl_PointSize = a_size / u_sizeRatio * u_pixelRatio * 2.0;

  v_border = (0.5 / a_size) * u_sizeRatio;

  #ifdef PICKING_MODE
  // For picking mode, we use the ID as the color:
  v_color = a_id;
  #else
  // For normal mode, we use the color:
  v_color = a_color;
  #endif

  v_color.a *= bias;
}
`,Fu=Gu,Za=WebGLRenderingContext,oo=Za.UNSIGNED_BYTE,ao=Za.FLOAT,Mu=["u_sizeRatio","u_pixelRatio","u_matrix"],$u=function(e){function t(){return la(this,t),ca(this,t,arguments)}return aa(t,e),sa(t,[{key:"getDefinition",value:function(){return{VERTICES:1,VERTEX_SHADER_SOURCE:Fu,FRAGMENT_SHADER_SOURCE:Ou,METHOD:WebGLRenderingContext.POINTS,UNIFORMS:Mu,ATTRIBUTES:[{name:"a_position",size:2,type:ao},{name:"a_size",size:1,type:ao},{name:"a_color",size:4,type:oo,normalized:!0},{name:"a_id",size:4,type:oo,normalized:!0}]}}},{key:"processVisibleItem",value:function(n,a,o){var l=this.array;l[a++]=o.x,l[a++]=o.y,l[a++]=o.size,l[a++]=er(o.color),l[a++]=n}},{key:"setUniforms",value:function(n,a){var o=n.sizeRatio,l=n.pixelRatio,i=n.matrix,s=a.gl,c=a.uniformLocations,u=c.u_sizeRatio,d=c.u_pixelRatio,h=c.u_matrix;s.uniform1f(d,l),s.uniform1f(u,o),s.uniformMatrix3fv(h,!1,i)}}])}(ia),Hu=`
attribute vec4 a_id;
attribute vec4 a_color;
attribute vec2 a_normal;
attribute float a_normalCoef;
attribute vec2 a_positionStart;
attribute vec2 a_positionEnd;
attribute float a_positionCoef;
attribute float a_sourceRadius;
attribute float a_targetRadius;
attribute float a_sourceRadiusCoef;
attribute float a_targetRadiusCoef;

uniform mat3 u_matrix;
uniform float u_zoomRatio;
uniform float u_sizeRatio;
uniform float u_pixelRatio;
uniform float u_correctionRatio;
uniform float u_minEdgeThickness;
uniform float u_lengthToThicknessRatio;
uniform float u_feather;

varying vec4 v_color;
varying vec2 v_normal;
varying float v_thickness;
varying float v_feather;

const float bias = 255.0 / 254.0;

void main() {
  float minThickness = u_minEdgeThickness;

  vec2 normal = a_normal * a_normalCoef;
  vec2 position = a_positionStart * (1.0 - a_positionCoef) + a_positionEnd * a_positionCoef;

  float normalLength = length(normal);
  vec2 unitNormal = normal / normalLength;

  // These first computations are taken from edge.vert.glsl. Please read it to
  // get better comments on what's happening:
  float pixelsThickness = max(normalLength, minThickness * u_sizeRatio);
  float webGLThickness = pixelsThickness * u_correctionRatio / u_sizeRatio;

  // Here, we move the point to leave space for the arrow heads:
  // Source arrow head
  float sourceRadius = a_sourceRadius * a_sourceRadiusCoef;
  float sourceDirection = sign(sourceRadius);
  float webGLSourceRadius = sourceDirection * sourceRadius * 2.0 * u_correctionRatio / u_sizeRatio;
  float webGLSourceArrowHeadLength = webGLThickness * u_lengthToThicknessRatio * 2.0;
  vec2 sourceCompensationVector =
    vec2(-sourceDirection * unitNormal.y, sourceDirection * unitNormal.x)
    * (webGLSourceRadius + webGLSourceArrowHeadLength);
    
  // Target arrow head
  float targetRadius = a_targetRadius * a_targetRadiusCoef;
  float targetDirection = sign(targetRadius);
  float webGLTargetRadius = targetDirection * targetRadius * 2.0 * u_correctionRatio / u_sizeRatio;
  float webGLTargetArrowHeadLength = webGLThickness * u_lengthToThicknessRatio * 2.0;
  vec2 targetCompensationVector =
  vec2(-targetDirection * unitNormal.y, targetDirection * unitNormal.x)
    * (webGLTargetRadius + webGLTargetArrowHeadLength);

  // Here is the proper position of the vertex
  gl_Position = vec4((u_matrix * vec3(position + unitNormal * webGLThickness + sourceCompensationVector + targetCompensationVector, 1)).xy, 0, 1);

  v_thickness = webGLThickness / u_zoomRatio;

  v_normal = unitNormal;

  v_feather = u_feather * u_correctionRatio / u_zoomRatio / u_pixelRatio * 2.0;

  #ifdef PICKING_MODE
  // For picking mode, we use the ID as the color:
  v_color = a_id;
  #else
  // For normal mode, we use the color:
  v_color = a_color;
  #endif

  v_color.a *= bias;
}
`,Bu=Hu,es=WebGLRenderingContext,so=es.UNSIGNED_BYTE,Ue=es.FLOAT,Vu=["u_matrix","u_zoomRatio","u_sizeRatio","u_correctionRatio","u_pixelRatio","u_feather","u_minEdgeThickness","u_lengthToThicknessRatio"],Uu={lengthToThicknessRatio:bn.lengthToThicknessRatio};function ts(e){var t=Ut(Ut({},Uu),{});return function(r){function n(){return la(this,n),ca(this,n,arguments)}return aa(n,r),sa(n,[{key:"getDefinition",value:function(){return{VERTICES:6,VERTEX_SHADER_SOURCE:Bu,FRAGMENT_SHADER_SOURCE:mi,METHOD:WebGLRenderingContext.TRIANGLES,UNIFORMS:Vu,ATTRIBUTES:[{name:"a_positionStart",size:2,type:Ue},{name:"a_positionEnd",size:2,type:Ue},{name:"a_normal",size:2,type:Ue},{name:"a_color",size:4,type:so,normalized:!0},{name:"a_id",size:4,type:so,normalized:!0},{name:"a_sourceRadius",size:1,type:Ue},{name:"a_targetRadius",size:1,type:Ue}],CONSTANT_ATTRIBUTES:[{name:"a_positionCoef",size:1,type:Ue},{name:"a_normalCoef",size:1,type:Ue},{name:"a_sourceRadiusCoef",size:1,type:Ue},{name:"a_targetRadiusCoef",size:1,type:Ue}],CONSTANT_DATA:[[0,1,-1,0],[0,-1,1,0],[1,1,0,1],[1,1,0,1],[0,-1,1,0],[1,-1,0,-1]]}}},{key:"processVisibleItem",value:function(o,l,i,s,c){var u=c.size||1,d=i.x,h=i.y,f=s.x,b=s.y,y=er(c.color),k=f-d,I=b-h,E=i.size||1,j=s.size||1,A=k*k+I*I,N=0,z=0;A&&(A=1/Math.sqrt(A),N=-I*A*u,z=k*A*u);var m=this.array;m[l++]=d,m[l++]=h,m[l++]=f,m[l++]=b,m[l++]=N,m[l++]=z,m[l++]=y,m[l++]=o,m[l++]=E,m[l++]=j}},{key:"setUniforms",value:function(o,l){var i=l.gl,s=l.uniformLocations,c=s.u_matrix,u=s.u_zoomRatio,d=s.u_feather,h=s.u_pixelRatio,f=s.u_correctionRatio,b=s.u_sizeRatio,y=s.u_minEdgeThickness,k=s.u_lengthToThicknessRatio;i.uniformMatrix3fv(c,!1,o.matrix),i.uniform1f(u,o.zoomRatio),i.uniform1f(b,o.sizeRatio),i.uniform1f(f,o.correctionRatio),i.uniform1f(h,o.pixelRatio),i.uniform1f(d,o.antiAliasingFeather),i.uniform1f(y,o.minEdgeThickness),i.uniform1f(k,t.lengthToThicknessRatio)}}])}(ua)}ts();function qu(e){return vi([ts(),Un(e),Un(Ut(Ut({},e),{},{extremity:"source"}))])}qu();function Wu(e){if(Array.isArray(e))return e}function Xu(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,a,o,l,i=[],s=!0,c=!1;try{if(o=(r=r.call(e)).next,t!==0)for(;!(s=(n=o.call(r)).done)&&(i.push(n.value),i.length!==t);s=!0);}catch(u){c=!0,a=u}finally{try{if(!s&&r.return!=null&&(l=r.return(),Object(l)!==l))return}finally{if(c)throw a}}return i}}function sn(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function rs(e,t){if(e){if(typeof e=="string")return sn(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?sn(e,t):void 0}}function Yu(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ku(e,t){return Wu(e)||Xu(e,t)||rs(e,t)||Yu()}function Qu(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ju(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ns(e){var t=Ju(e,"string");return typeof t=="symbol"?t:t+""}function Zu(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ns(n.key),n)}}function ed(e,t,r){return t&&Zu(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Xt(e){return Xt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Xt(e)}function os(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(os=function(){return!!e})()}function ln(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function td(e,t){if(t&&(typeof t=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ln(e)}function rd(e,t,r){return t=Xt(t),td(e,os()?Reflect.construct(t,r||[],Xt(e).constructor):t.apply(e,r))}function cn(e,t){return cn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,n){return r.__proto__=n,r},cn(e,t)}function nd(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&cn(e,t)}function vt(e,t,r){return(t=ns(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function od(e){if(Array.isArray(e))return sn(e)}function ad(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function sd(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gr(e){return od(e)||ad(e)||rs(e)||sd()}function io(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function lo(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?io(Object(r),!0).forEach(function(n){vt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):io(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}var id="relative",ld={drawLabel:void 0,drawHover:void 0,borders:[{size:{value:.1},color:{attribute:"borderColor"}},{size:{fill:!0},color:{attribute:"color"}}]},cd="#000000";function ud(e){var t=e.borders,r=qn(t.filter(function(a){var o=a.size;return"fill"in o}).length),n=`
precision highp float;

varying vec2 v_diffVector;
varying float v_radius;

#ifdef PICKING_MODE
varying vec4 v_color;
#else
// For normal mode, we use the border colors defined in the program:
`.concat(t.flatMap(function(a,o){var l=a.size;return"attribute"in l?["varying float v_borderSize_".concat(o+1,";")]:[]}).join(`
`),`
`).concat(t.flatMap(function(a,o){var l=a.color;return"attribute"in l?["varying vec4 v_borderColor_".concat(o+1,";")]:"value"in l?["uniform vec4 u_borderColor_".concat(o+1,";")]:[]}).join(`
`),`
#endif

uniform float u_correctionRatio;

const float bias = 255.0 / 254.0;
const vec4 transparent = vec4(0.0, 0.0, 0.0, 0.0);

void main(void) {
  float dist = length(v_diffVector);
  float aaBorder = 2.0 * u_correctionRatio;
  float v_borderSize_0 = v_radius;
  vec4 v_borderColor_0 = transparent;

  // No antialiasing for picking mode:
  #ifdef PICKING_MODE
  if (dist > v_radius)
    gl_FragColor = transparent;
  else {
    gl_FragColor = v_color;
    gl_FragColor.a *= bias;
  }
  #else
  // Sizes:
`).concat(t.flatMap(function(a,o){var l=a.size;if("fill"in l)return[];l=l;var i="attribute"in l?"v_borderSize_".concat(o+1):qn(l.value),s=(l.mode||id)==="pixels"?"u_correctionRatio":"v_radius";return["  float borderSize_".concat(o+1," = ").concat(s," * ").concat(i,";")]}).join(`
`),`
  // Now, let's split the remaining space between "fill" borders:
  float fillBorderSize = (v_radius - (`).concat(t.flatMap(function(a,o){var l=a.size;return"fill"in l?[]:["borderSize_".concat(o+1)]}).join(" + "),") ) / ").concat(r,`;
`).concat(t.flatMap(function(a,o){var l=a.size;return"fill"in l?["  float borderSize_".concat(o+1," = fillBorderSize;")]:[]}).join(`
`),`

  // Finally, normalize all border sizes, to start from the full size and to end with the smallest:
  float adjustedBorderSize_0 = v_radius;
`).concat(t.map(function(a,o){return"  float adjustedBorderSize_".concat(o+1," = adjustedBorderSize_").concat(o," - borderSize_").concat(o+1,";")}).join(`
`),`

  // Colors:
  vec4 borderColor_0 = transparent;
`).concat(t.map(function(a,o){var l=a.color,i=[];return"attribute"in l?i.push("  vec4 borderColor_".concat(o+1," = v_borderColor_").concat(o+1,";")):"transparent"in l?i.push("  vec4 borderColor_".concat(o+1," = vec4(0.0, 0.0, 0.0, 0.0);")):i.push("  vec4 borderColor_".concat(o+1," = u_borderColor_").concat(o+1,";")),i.push("  borderColor_".concat(o+1,".a *= bias;")),i.push("  if (borderSize_".concat(o+1," <= 1.0 * u_correctionRatio) { borderColor_").concat(o+1," = borderColor_").concat(o,"; }")),i.join(`
`)}).join(`
`),`
  if (dist > adjustedBorderSize_0) {
    gl_FragColor = borderColor_0;
  } else `).concat(t.map(function(a,o){return"if (dist > adjustedBorderSize_".concat(o,` - aaBorder) {
    gl_FragColor = mix(borderColor_`).concat(o+1,", borderColor_").concat(o,", (dist - adjustedBorderSize_").concat(o,` + aaBorder) / aaBorder);
  } else if (dist > adjustedBorderSize_`).concat(o+1,`) {
    gl_FragColor = borderColor_`).concat(o+1,`;
  } else `)}).join(""),` { /* Nothing to add here */ }
  #endif
}
`);return n}function dd(e){var t=e.borders,r=`
attribute vec2 a_position;
attribute float a_size;
attribute float a_angle;

uniform mat3 u_matrix;
uniform float u_sizeRatio;
uniform float u_correctionRatio;

varying vec2 v_diffVector;
varying float v_radius;

#ifdef PICKING_MODE
attribute vec4 a_id;
varying vec4 v_color;
#else
`.concat(t.flatMap(function(n,a){var o=n.size;return"attribute"in o?["attribute float a_borderSize_".concat(a+1,";"),"varying float v_borderSize_".concat(a+1,";")]:[]}).join(`
`),`
`).concat(t.flatMap(function(n,a){var o=n.color;return"attribute"in o?["attribute vec4 a_borderColor_".concat(a+1,";"),"varying vec4 v_borderColor_".concat(a+1,";")]:[]}).join(`
`),`
#endif

const float bias = 255.0 / 254.0;
const vec4 transparent = vec4(0.0, 0.0, 0.0, 0.0);

void main() {
  float size = a_size * u_correctionRatio / u_sizeRatio * 4.0;
  vec2 diffVector = size * vec2(cos(a_angle), sin(a_angle));
  vec2 position = a_position + diffVector;
  gl_Position = vec4(
    (u_matrix * vec3(position, 1)).xy,
    0,
    1
  );

  v_radius = size / 2.0;
  v_diffVector = diffVector;

  #ifdef PICKING_MODE
  v_color = a_id;
  #else
`).concat(t.flatMap(function(n,a){var o=n.size;return"attribute"in o?["  v_borderSize_".concat(a+1," = a_borderSize_").concat(a+1,";")]:[]}).join(`
`),`
`).concat(t.flatMap(function(n,a){var o=n.color;return"attribute"in o?["  v_borderColor_".concat(a+1," = a_borderColor_").concat(a+1,";")]:[]}).join(`
`),`
  #endif
}
`);return r}var as=WebGLRenderingContext,co=as.UNSIGNED_BYTE,Lt=as.FLOAT;function fd(e){var t,r=lo(lo({},ld),{}),n=r.borders,a=r.drawLabel,o=r.drawHover,l=["u_sizeRatio","u_correctionRatio","u_matrix"].concat(gr(n.flatMap(function(i,s){var c=i.color;return"value"in c?["u_borderColor_".concat(s+1)]:[]})));return t=function(i){nd(s,i);function s(){var c;Qu(this,s);for(var u=arguments.length,d=new Array(u),h=0;h<u;h++)d[h]=arguments[h];return c=rd(this,s,[].concat(d)),vt(ln(c),"drawLabel",a),vt(ln(c),"drawHover",o),c}return ed(s,[{key:"getDefinition",value:function(){return{VERTICES:3,VERTEX_SHADER_SOURCE:dd(r),FRAGMENT_SHADER_SOURCE:ud(r),METHOD:WebGLRenderingContext.TRIANGLES,UNIFORMS:l,ATTRIBUTES:[{name:"a_position",size:2,type:Lt},{name:"a_id",size:4,type:co,normalized:!0},{name:"a_size",size:1,type:Lt}].concat(gr(n.flatMap(function(u,d){var h=u.color;return"attribute"in h?[{name:"a_borderColor_".concat(d+1),size:4,type:co,normalized:!0}]:[]})),gr(n.flatMap(function(u,d){var h=u.size;return"attribute"in h?[{name:"a_borderSize_".concat(d+1),size:1,type:Lt}]:[]}))),CONSTANT_ATTRIBUTES:[{name:"a_angle",size:1,type:Lt}],CONSTANT_DATA:[[s.ANGLE_1],[s.ANGLE_2],[s.ANGLE_3]]}}},{key:"processVisibleItem",value:function(u,d,h){var f=this.array;f[d++]=h.x,f[d++]=h.y,f[d++]=u,f[d++]=h.size,n.forEach(function(b){var y=b.color;"attribute"in y&&(f[d++]=er(h[y.attribute]||y.defaultValue||cd))}),n.forEach(function(b){var y=b.size;"attribute"in y&&(f[d++]=h[y.attribute]||y.defaultValue)})}},{key:"setUniforms",value:function(u,d){var h=d.gl,f=d.uniformLocations,b=f.u_sizeRatio,y=f.u_correctionRatio,k=f.u_matrix;h.uniform1f(y,u.correctionRatio),h.uniform1f(b,u.sizeRatio),h.uniformMatrix3fv(k,!1,u.matrix),n.forEach(function(I,E){var j=I.color;if("value"in j){var A=f["u_borderColor_".concat(E+1)],N=yi(j.value),z=Ku(N,4),m=z[0],_=z[1],x=z[2],T=z[3];h.uniform4f(A,m/255,_/255,x/255,T/255)}})}}]),s}(ia),vt(t,"ANGLE_1",0),vt(t,"ANGLE_2",2*Math.PI/3),vt(t,"ANGLE_3",4*Math.PI/3),t}var hd=fd();function gd(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ss(e){var t=gd(e,"string");return typeof t=="symbol"?t:t+""}function is(e,t,r){return(t=ss(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,n)}return r}function Yt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?uo(Object(r),!0).forEach(function(n){is(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uo(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function pd(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function md(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ss(n.key),n)}}function vd(e,t,r){return t&&md(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Kt(e){return Kt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Kt(e)}function ls(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ls=function(){return!!e})()}function cs(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function yd(e,t){if(t&&(typeof t=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cs(e)}function bd(e,t,r){return t=Kt(t),yd(e,ls()?Reflect.construct(t,r||[],Kt(e).constructor):t.apply(e,r))}function un(e,t){return un=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,n){return r.__proto__=n,r},un(e,t)}function wd(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&un(e,t)}function dn(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function xd(e){if(Array.isArray(e))return dn(e)}function Sd(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function _d(e,t){if(e){if(typeof e=="string")return dn(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?dn(e,t):void 0}}function Ed(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function pr(e){return xd(e)||Sd(e)||_d(e)||Ed()}function us(e,t,r,n){var a=Math.pow(1-e,2)*t.x+2*(1-e)*e*r.x+Math.pow(e,2)*n.x,o=Math.pow(1-e,2)*t.y+2*(1-e)*e*r.y+Math.pow(e,2)*n.y;return{x:a,y:o}}function Cd(e,t,r){for(var n=20,a=0,o=e,l=0;l<n;l++){var i=us((l+1)/n,e,t,r);a+=Math.sqrt(Math.pow(o.x-i.x,2)+Math.pow(o.y-i.y,2)),o=i}return a}function kd(e){var t=e.curvatureAttribute,r=e.defaultCurvature,n=e.keepLabelUpright,a=n===void 0?!0:n;return function(o,l,i,s,c){var u=c.edgeLabelSize,d=l[t]||r,h=c.edgeLabelFont,f=c.edgeLabelWeight,b=c.edgeLabelColor.attribute?l[c.edgeLabelColor.attribute]||c.edgeLabelColor.color||"#000":c.edgeLabelColor.color,y=l.label;if(y){o.fillStyle=b,o.font="".concat(f," ").concat(u,"px ").concat(h);var k=!a||i.x<s.x,I=k?i.x:s.x,E=k?i.y:s.y,j=k?s.x:i.x,A=k?s.y:i.y,N=(I+j)/2,z=(E+A)/2,m=j-I,_=A-E,x=Math.sqrt(Math.pow(m,2)+Math.pow(_,2)),T=k?1:-1,R=N+_*d*T,O=z-m*d*T,w=l.size*.7+5,H={x:O-E,y:-(R-I)},K=Math.sqrt(Math.pow(H.x,2)+Math.pow(H.y,2)),D={x:A-O,y:-(j-R)},C=Math.sqrt(Math.pow(D.x,2)+Math.pow(D.y,2));I+=w*H.x/K,E+=w*H.y/K,j+=w*D.x/C,A+=w*D.y/C,R+=w*_/x,O-=w*m/x;var S={x:R,y:O},B={x:I,y:E},ae={x:j,y:A},M=Cd(B,S,ae);if(!(M<i.size+s.size)){var v=o.measureText(y).width,P=M-i.size-s.size;if(v>P){var V="…";for(y=y+V,v=o.measureText(y).width;v>P&&y.length>1;)y=y.slice(0,-2)+V,v=o.measureText(y).width;if(y.length<4)return}for(var $={},J=0,X=y.length;J<X;J++){var Y=y[J];$[Y]||($[Y]=o.measureText(Y).width*(1+d*.35))}for(var ie=.5-v/M/2,ne=0,se=y.length;ne<se;ne++){var F=y[ne],Q=us(ie,B,S,ae),q=2*(1-ie)*(R-I)+2*ie*(j-R),U=2*(1-ie)*(O-E)+2*ie*(A-O),L=Math.atan2(U,q);o.save(),o.translate(Q.x,Q.y),o.rotate(L),o.fillText(F,0,0),o.restore(),ie+=$[F]/M}}}}}function Td(e){var t=e.arrowHead,r=(t==null?void 0:t.extremity)==="target"||(t==null?void 0:t.extremity)==="both",n=(t==null?void 0:t.extremity)==="source"||(t==null?void 0:t.extremity)==="both",a=`
precision highp float;

varying vec4 v_color;
varying float v_thickness;
varying float v_feather;
varying vec2 v_cpA;
varying vec2 v_cpB;
varying vec2 v_cpC;
`.concat(r?`
varying float v_targetSize;
varying vec2 v_targetPoint;`:"",`
`).concat(n?`
varying float v_sourceSize;
varying vec2 v_sourcePoint;`:"",`
`).concat(t?`
uniform float u_lengthToThicknessRatio;
uniform float u_widenessToThicknessRatio;`:"",`

float det(vec2 a, vec2 b) {
  return a.x * b.y - b.x * a.y;
}

vec2 getDistanceVector(vec2 b0, vec2 b1, vec2 b2) {
  float a = det(b0, b2), b = 2.0 * det(b1, b0), d = 2.0 * det(b2, b1);
  float f = b * d - a * a;
  vec2 d21 = b2 - b1, d10 = b1 - b0, d20 = b2 - b0;
  vec2 gf = 2.0 * (b * d21 + d * d10 + a * d20);
  gf = vec2(gf.y, -gf.x);
  vec2 pp = -f * gf / dot(gf, gf);
  vec2 d0p = b0 - pp;
  float ap = det(d0p, d20), bp = 2.0 * det(d10, d0p);
  float t = clamp((ap + bp) / (2.0 * a + b + d), 0.0, 1.0);
  return mix(mix(b0, b1, t), mix(b1, b2, t), t);
}

float distToQuadraticBezierCurve(vec2 p, vec2 b0, vec2 b1, vec2 b2) {
  return length(getDistanceVector(b0 - p, b1 - p, b2 - p));
}

const vec4 transparent = vec4(0.0, 0.0, 0.0, 0.0);

void main(void) {
  float dist = distToQuadraticBezierCurve(gl_FragCoord.xy, v_cpA, v_cpB, v_cpC);
  float thickness = v_thickness;
`).concat(r?`
  float distToTarget = length(gl_FragCoord.xy - v_targetPoint);
  float targetArrowLength = v_targetSize + thickness * u_lengthToThicknessRatio;
  if (distToTarget < targetArrowLength) {
    thickness = (distToTarget - v_targetSize) / (targetArrowLength - v_targetSize) * u_widenessToThicknessRatio * thickness;
  }`:"",`
`).concat(n?`
  float distToSource = length(gl_FragCoord.xy - v_sourcePoint);
  float sourceArrowLength = v_sourceSize + thickness * u_lengthToThicknessRatio;
  if (distToSource < sourceArrowLength) {
    thickness = (distToSource - v_sourceSize) / (sourceArrowLength - v_sourceSize) * u_widenessToThicknessRatio * thickness;
  }`:"",`

  float halfThickness = thickness / 2.0;
  if (dist < halfThickness) {
    #ifdef PICKING_MODE
    gl_FragColor = v_color;
    #else
    float t = smoothstep(
      halfThickness - v_feather,
      halfThickness,
      dist
    );

    gl_FragColor = mix(v_color, transparent, t);
    #endif
  } else {
    gl_FragColor = transparent;
  }
}
`);return a}function Rd(e){var t=e.arrowHead,r=(t==null?void 0:t.extremity)==="target"||(t==null?void 0:t.extremity)==="both",n=(t==null?void 0:t.extremity)==="source"||(t==null?void 0:t.extremity)==="both",a=`
attribute vec4 a_id;
attribute vec4 a_color;
attribute float a_direction;
attribute float a_thickness;
attribute vec2 a_source;
attribute vec2 a_target;
attribute float a_current;
attribute float a_curvature;
`.concat(r?`attribute float a_targetSize;
`:"",`
`).concat(n?`attribute float a_sourceSize;
`:"",`

uniform mat3 u_matrix;
uniform float u_sizeRatio;
uniform float u_pixelRatio;
uniform vec2 u_dimensions;
uniform float u_minEdgeThickness;
uniform float u_feather;

varying vec4 v_color;
varying float v_thickness;
varying float v_feather;
varying vec2 v_cpA;
varying vec2 v_cpB;
varying vec2 v_cpC;
`).concat(r?`
varying float v_targetSize;
varying vec2 v_targetPoint;`:"",`
`).concat(n?`
varying float v_sourceSize;
varying vec2 v_sourcePoint;`:"",`
`).concat(t?`
uniform float u_widenessToThicknessRatio;`:"",`

const float bias = 255.0 / 254.0;
const float epsilon = 0.7;

vec2 clipspaceToViewport(vec2 pos, vec2 dimensions) {
  return vec2(
    (pos.x + 1.0) * dimensions.x / 2.0,
    (pos.y + 1.0) * dimensions.y / 2.0
  );
}

vec2 viewportToClipspace(vec2 pos, vec2 dimensions) {
  return vec2(
    pos.x / dimensions.x * 2.0 - 1.0,
    pos.y / dimensions.y * 2.0 - 1.0
  );
}

void main() {
  float minThickness = u_minEdgeThickness;

  // Selecting the correct position
  // Branchless "position = a_source if a_current == 1.0 else a_target"
  vec2 position = a_source * max(0.0, a_current) + a_target * max(0.0, 1.0 - a_current);
  position = (u_matrix * vec3(position, 1)).xy;

  vec2 source = (u_matrix * vec3(a_source, 1)).xy;
  vec2 target = (u_matrix * vec3(a_target, 1)).xy;

  vec2 viewportPosition = clipspaceToViewport(position, u_dimensions);
  vec2 viewportSource = clipspaceToViewport(source, u_dimensions);
  vec2 viewportTarget = clipspaceToViewport(target, u_dimensions);

  vec2 delta = viewportTarget.xy - viewportSource.xy;
  float len = length(delta);
  vec2 normal = vec2(-delta.y, delta.x) * a_direction;
  vec2 unitNormal = normal / len;
  float boundingBoxThickness = len * a_curvature;

  float curveThickness = max(minThickness, a_thickness / u_sizeRatio);
  v_thickness = curveThickness * u_pixelRatio;
  v_feather = u_feather;

  v_cpA = viewportSource;
  v_cpB = 0.5 * (viewportSource + viewportTarget) + unitNormal * a_direction * boundingBoxThickness;
  v_cpC = viewportTarget;

  vec2 viewportOffsetPosition = (
    viewportPosition +
    unitNormal * (boundingBoxThickness / 2.0 + sign(boundingBoxThickness) * (`).concat(t?"curveThickness * u_widenessToThicknessRatio":"curveThickness",` + epsilon)) *
    max(0.0, a_direction) // NOTE: cutting the bounding box in half to avoid overdraw
  );

  position = viewportToClipspace(viewportOffsetPosition, u_dimensions);
  gl_Position = vec4(position, 0, 1);
    
`).concat(r?`
  v_targetSize = a_targetSize * u_pixelRatio / u_sizeRatio;
  v_targetPoint = viewportTarget;
`:"",`
`).concat(n?`
  v_sourceSize = a_sourceSize * u_pixelRatio / u_sizeRatio;
  v_sourcePoint = viewportSource;
`:"",`

  #ifdef PICKING_MODE
  // For picking mode, we use the ID as the color:
  v_color = a_id;
  #else
  // For normal mode, we use the color:
  v_color = a_color;
  #endif

  v_color.a *= bias;
}
`);return a}var ds=.25,Ad={arrowHead:null,curvatureAttribute:"curvature",defaultCurvature:ds},fs=WebGLRenderingContext,fo=fs.UNSIGNED_BYTE,et=fs.FLOAT;function An(e){var t=Yt(Yt({},Ad),e||{}),r=t,n=r.arrowHead,a=r.curvatureAttribute,o=r.drawLabel,l=(n==null?void 0:n.extremity)==="target"||(n==null?void 0:n.extremity)==="both",i=(n==null?void 0:n.extremity)==="source"||(n==null?void 0:n.extremity)==="both",s=["u_matrix","u_sizeRatio","u_dimensions","u_pixelRatio","u_feather","u_minEdgeThickness"].concat(pr(n?["u_lengthToThicknessRatio","u_widenessToThicknessRatio"]:[]));return function(c){wd(u,c);function u(){var d;pd(this,u);for(var h=arguments.length,f=new Array(h),b=0;b<h;b++)f[b]=arguments[b];return d=bd(this,u,[].concat(f)),is(cs(d),"drawLabel",o||kd(t)),d}return vd(u,[{key:"getDefinition",value:function(){return{VERTICES:6,VERTEX_SHADER_SOURCE:Rd(t),FRAGMENT_SHADER_SOURCE:Td(t),METHOD:WebGLRenderingContext.TRIANGLES,UNIFORMS:s,ATTRIBUTES:[{name:"a_source",size:2,type:et},{name:"a_target",size:2,type:et}].concat(pr(l?[{name:"a_targetSize",size:1,type:et}]:[]),pr(i?[{name:"a_sourceSize",size:1,type:et}]:[]),[{name:"a_thickness",size:1,type:et},{name:"a_curvature",size:1,type:et},{name:"a_color",size:4,type:fo,normalized:!0},{name:"a_id",size:4,type:fo,normalized:!0}]),CONSTANT_ATTRIBUTES:[{name:"a_current",size:1,type:et},{name:"a_direction",size:1,type:et}],CONSTANT_DATA:[[0,1],[0,-1],[1,1],[0,-1],[1,1],[1,-1]]}}},{key:"processVisibleItem",value:function(h,f,b,y,k){var I,E=k.size||1,j=b.x,A=b.y,N=y.x,z=y.y,m=er(k.color),_=(I=k[a])!==null&&I!==void 0?I:ds,x=this.array;x[f++]=j,x[f++]=A,x[f++]=N,x[f++]=z,l&&(x[f++]=y.size),i&&(x[f++]=b.size),x[f++]=E,x[f++]=_,x[f++]=m,x[f++]=h}},{key:"setUniforms",value:function(h,f){var b=f.gl,y=f.uniformLocations,k=y.u_matrix,I=y.u_pixelRatio,E=y.u_feather,j=y.u_sizeRatio,A=y.u_dimensions,N=y.u_minEdgeThickness;if(b.uniformMatrix3fv(k,!1,h.matrix),b.uniform1f(I,h.pixelRatio),b.uniform1f(j,h.sizeRatio),b.uniform1f(E,h.antiAliasingFeather),b.uniform2f(A,h.width*h.pixelRatio,h.height*h.pixelRatio),b.uniform1f(N,h.minEdgeThickness),n){var z=y.u_lengthToThicknessRatio,m=y.u_widenessToThicknessRatio;b.uniform1f(z,n.lengthToThicknessRatio),b.uniform1f(m,n.widenessToThicknessRatio)}}}]),u}(ua)}var jd=An(),Nd=An({arrowHead:bn});An({arrowHead:Yt(Yt({},bn),{},{extremity:"both"})});const Id=({node:e,move:t})=>{const r=Be(),{gotoNode:n}=da();return p.useEffect(()=>{const a=r.getGraph();if(t){if(e&&a.hasNode(e))try{a.setNodeAttribute(e,"highlighted",!0),n(e)}catch(o){console.error("Error focusing on node:",o)}else r.setCustomBBox(null),r.getCamera().animate({x:.5,y:.5,ratio:1},{duration:0});te.getState().setMoveToSelectedNode(!1)}else if(e&&a.hasNode(e))try{a.setNodeAttribute(e,"highlighted",!0)}catch(o){console.error("Error highlighting node:",o)}return()=>{if(e&&a.hasNode(e))try{a.setNodeAttribute(e,"highlighted",!1)}catch(o){console.error("Error cleaning up node highlight:",o)}}},[e,t,r,n]),null};function _t(e,t){const r=Be(),n=p.useRef(t);return fa(n.current,t)||(n.current=t),{positions:p.useCallback(()=>n.current?e(r.getGraph(),n.current):{},[r,n,e]),assign:p.useCallback(()=>{n.current&&e.assign(r.getGraph(),n.current)},[r,n,e])}}function jn(e,t){const r=Be(),[n,a]=p.useState(!1),[o,l]=p.useState(null),i=p.useRef(t);return fa(i.current,t)||(i.current=t),p.useEffect(()=>{a(!1);let s=null;return i.current&&(s=new e(r.getGraph(),i.current)),l(s),()=>{s!==null&&s.kill()}},[r,i,l,a,e]),{stop:p.useCallback(()=>{o&&(o.stop(),a(!1))},[o,a]),start:p.useCallback(()=>{o&&(o.start(),a(!0))},[o,a]),kill:p.useCallback(()=>{o&&o.kill(),a(!1)},[o,a]),isRunning:n}}var mr,ho;function At(){if(ho)return mr;ho=1;function e(r){return!r||typeof r!="object"||typeof r=="function"||Array.isArray(r)||r instanceof Set||r instanceof Map||r instanceof RegExp||r instanceof Date}function t(r,n){r=r||{};var a={};for(var o in n){var l=r[o],i=n[o];if(!e(i)){a[o]=t(l,i);continue}l===void 0?a[o]=i:a[o]=l}return a}return mr=t,mr}var vr,go;function Ld(){if(go)return vr;go=1;function e(r){return function(n,a){return n+Math.floor(r()*(a-n+1))}}var t=e(Math.random);return t.createRandom=e,vr=t,vr}var yr,po;function Pd(){if(po)return yr;po=1;var e=Ld().createRandom;function t(n){var a=e(n);return function(o){for(var l=o.length,i=l-1,s=-1;++s<l;){var c=a(s,i),u=o[c];o[c]=o[s],o[s]=u}}}var r=t(Math.random);return r.createShuffleInPlace=t,yr=r,yr}var br,mo;function zd(){if(mo)return br;mo=1;var e=At(),t=We(),r=Pd(),n={attributes:{x:"x",y:"y"},center:0,hierarchyAttributes:[],rng:Math.random,scale:1};function a(m,_,x,T,R){this.wrappedCircle=R||null,this.children={},this.countChildren=0,this.id=m||null,this.next=null,this.previous=null,this.x=_||null,this.y=x||null,R?this.r=1010101:this.r=T||999}a.prototype.hasChildren=function(){return this.countChildren>0},a.prototype.addChild=function(m,_){this.children[m]=_,++this.countChildren},a.prototype.getChild=function(m){if(!this.children.hasOwnProperty(m)){var _=new a;this.children[m]=_,++this.countChildren}return this.children[m]},a.prototype.applyPositionToChildren=function(){if(this.hasChildren()){var m=this;for(var _ in m.children){var x=m.children[_];x.x+=m.x,x.y+=m.y,x.applyPositionToChildren()}}};function o(m,_,x){for(var T in _.children){var R=_.children[T];R.hasChildren()?o(m,R,x):x[R.id]={x:R.x,y:R.y}}}function l(m,_){var x=m.r-_.r,T=_.x-m.x,R=_.y-m.y;return x<0||x*x<T*T+R*R}function i(m,_){var x=m.r-_.r+1e-6,T=_.x-m.x,R=_.y-m.y;return x>0&&x*x>T*T+R*R}function s(m,_){for(var x=0;x<_.length;++x)if(!i(m,_[x]))return!1;return!0}function c(m){return new a(null,m.x,m.y,m.r)}function u(m,_){var x=m.x,T=m.y,R=m.r,O=_.x,w=_.y,H=_.r,K=O-x,D=w-T,C=H-R,S=Math.sqrt(K*K+D*D);return new a(null,(x+O+K/S*C)/2,(T+w+D/S*C)/2,(S+R+H)/2)}function d(m,_,x){var T=m.x,R=m.y,O=m.r,w=_.x,H=_.y,K=_.r,D=x.x,C=x.y,S=x.r,B=T-w,ae=T-D,M=R-H,v=R-C,P=K-O,V=S-O,$=T*T+R*R-O*O,J=$-w*w-H*H+K*K,X=$-D*D-C*C+S*S,Y=ae*M-B*v,ie=(M*X-v*J)/(Y*2)-T,ne=(v*P-M*V)/Y,se=(ae*J-B*X)/(Y*2)-R,F=(B*V-ae*P)/Y,Q=ne*ne+F*F-1,q=2*(O+ie*ne+se*F),U=ie*ie+se*se-O*O,L=-(Q?(q+Math.sqrt(q*q-4*Q*U))/(2*Q):U/q);return new a(null,T+ie+ne*L,R+se+F*L,L)}function h(m){switch(m.length){case 1:return c(m[0]);case 2:return u(m[0],m[1]);case 3:return d(m[0],m[1],m[2]);default:throw new Error("graphology-layout/circlepack: Invalid basis length "+m.length)}}function f(m,_){var x,T;if(s(_,m))return[_];for(x=0;x<m.length;++x)if(l(_,m[x])&&s(u(m[x],_),m))return[m[x],_];for(x=0;x<m.length-1;++x)for(T=x+1;T<m.length;++T)if(l(u(m[x],m[T]),_)&&l(u(m[x],_),m[T])&&l(u(m[T],_),m[x])&&s(d(m[x],m[T],_),m))return[m[x],m[T],_];throw new Error("graphology-layout/circlepack: extendBasis failure !")}function b(m){var _=m.wrappedCircle,x=m.next.wrappedCircle,T=_.r+x.r,R=(_.x*x.r+x.x*_.r)/T,O=(_.y*x.r+x.y*_.r)/T;return R*R+O*O}function y(m,_){var x=0,T=m.slice(),R=m.length,O=[],w,H;for(_(T);x<R;)w=T[x],H&&i(H,w)?++x:(O=f(O,w),H=h(O),x=0);return H}function k(m,_,x){var T=m.x-_.x,R,O,w=m.y-_.y,H,K,D=T*T+w*w;D?(O=_.r+x.r,O*=O,K=m.r+x.r,K*=K,O>K?(R=(D+K-O)/(2*D),H=Math.sqrt(Math.max(0,K/D-R*R)),x.x=m.x-R*T-H*w,x.y=m.y-R*w+H*T):(R=(D+O-K)/(2*D),H=Math.sqrt(Math.max(0,O/D-R*R)),x.x=_.x+R*T-H*w,x.y=_.y+R*w+H*T)):(x.x=_.x+x.r,x.y=_.y)}function I(m,_){var x=m.r+_.r-1e-6,T=_.x-m.x,R=_.y-m.y;return x>0&&x*x>T*T+R*R}function E(m,_){var x=m.length;if(x===0)return 0;var T,R,O,w,H,K,D,C,S,B;if(T=m[0],T.x=0,T.y=0,x<=1)return T.r;if(R=m[1],T.x=-R.r,R.x=T.r,R.y=0,x<=2)return T.r+R.r;O=m[2],k(R,T,O),T=new a(null,null,null,null,T),R=new a(null,null,null,null,R),O=new a(null,null,null,null,O),T.next=O.previous=R,R.next=T.previous=O,O.next=R.previous=T;e:for(K=3;K<x;++K){O=m[K],k(T.wrappedCircle,R.wrappedCircle,O),O=new a(null,null,null,null,O),D=R.next,C=T.previous,S=R.wrappedCircle.r,B=T.wrappedCircle.r;do if(S<=B){if(I(D.wrappedCircle,O.wrappedCircle)){R=D,T.next=R,R.previous=T,--K;continue e}S+=D.wrappedCircle.r,D=D.next}else{if(I(C.wrappedCircle,O.wrappedCircle)){T=C,T.next=R,R.previous=T,--K;continue e}B+=C.wrappedCircle.r,C=C.previous}while(D!==C.next);for(O.previous=T,O.next=R,T.next=R.previous=R=O,w=b(T);(O=O.next)!==R;)(H=b(O))<w&&(T=O,w=H);R=T.next}T=[R.wrappedCircle],O=R;for(var ae=1e4;(O=O.next)!==R&&--ae!==0;)T.push(O.wrappedCircle);for(O=y(T,_),K=0;K<x;++K)T=m[K],T.x-=O.x,T.y-=O.y;return O.r}function j(m,_){var x=0;if(m.hasChildren()){for(var T in m.children){var R=m.children[T];R.hasChildren()&&(R.r=j(R,_))}x=E(Object.values(m.children),_)}return x}function A(m,_){j(m,_);for(var x in m.children){var T=m.children[x];T.applyPositionToChildren()}}function N(m,_,x){if(!t(_))throw new Error("graphology-layout/circlepack: the given graph is not a valid graphology instance.");x=e(x,n);var T={},R={},O=_.nodes(),w=x.center,H=x.hierarchyAttributes,K=r.createShuffleInPlace(x.rng),D=x.scale,C=new a;_.forEachNode(function(P,V){var $=V.size?V.size:1,J=new a(P,null,null,$),X=C;H.forEach(function(Y){var ie=V[Y];X=X.getChild(ie)}),X.addChild(P,J)}),A(C,K),o(_,C,T);var S=O.length,B,ae,M;for(M=0;M<S;M++){var v=O[M];B=w+D*T[v].x,ae=w+D*T[v].y,R[v]={x:B,y:ae},m&&(_.setNodeAttribute(v,x.attributes.x,B),_.setNodeAttribute(v,x.attributes.y,ae))}return R}var z=N.bind(null,!1);return z.assign=N.bind(null,!0),br=z,br}var Dd=zd();const Od=He(Dd);function Gd(e={}){return _t(Od,e)}var wr,vo;function Fd(){if(vo)return wr;vo=1;var e=At(),t=We(),r={dimensions:["x","y"],center:.5,scale:1};function n(o,l,i){if(!t(l))throw new Error("graphology-layout/random: the given graph is not a valid graphology instance.");i=e(i,r);var s=i.dimensions;if(!Array.isArray(s)||s.length!==2)throw new Error("graphology-layout/random: given dimensions are invalid.");var c=i.center,u=i.scale,d=Math.PI*2,h=(c-.5)*u,f=l.order,b=s[0],y=s[1];function k(j,A){return A[b]=u*Math.cos(j*d/f)+h,A[y]=u*Math.sin(j*d/f)+h,A}var I=0;if(!o){var E={};return l.forEachNode(function(j){E[j]=k(I++,{})}),E}l.updateEachNodeAttributes(function(j,A){return k(I++,A),A},{attributes:s})}var a=n.bind(null,!1);return a.assign=n.bind(null,!0),wr=a,wr}var Md=Fd();const $d=He(Md);function Hd(e={}){return _t($d,e)}var kt={},yo;function Nn(){if(yo)return kt;yo=1;function e(n){return typeof n!="number"||isNaN(n)?1:n}function t(n,a){var o={},l=function(c){return typeof c>"u"?a:c};typeof a=="function"&&(l=a);var i=function(c){return l(c[n])},s=function(){return l(void 0)};return typeof n=="string"?(o.fromAttributes=i,o.fromGraph=function(c,u){return i(c.getNodeAttributes(u))},o.fromEntry=function(c,u){return i(u)}):typeof n=="function"?(o.fromAttributes=function(){throw new Error("graphology-utils/getters/createNodeValueGetter: irrelevant usage.")},o.fromGraph=function(c,u){return l(n(u,c.getNodeAttributes(u)))},o.fromEntry=function(c,u){return l(n(c,u))}):(o.fromAttributes=s,o.fromGraph=s,o.fromEntry=s),o}function r(n,a){var o={},l=function(c){return typeof c>"u"?a:c};typeof a=="function"&&(l=a);var i=function(c){return l(c[n])},s=function(){return l(void 0)};return typeof n=="string"?(o.fromAttributes=i,o.fromGraph=function(c,u){return i(c.getEdgeAttributes(u))},o.fromEntry=function(c,u){return i(u)},o.fromPartialEntry=o.fromEntry,o.fromMinimalEntry=o.fromEntry):typeof n=="function"?(o.fromAttributes=function(){throw new Error("graphology-utils/getters/createEdgeValueGetter: irrelevant usage.")},o.fromGraph=function(c,u){var d=c.extremities(u);return l(n(u,c.getEdgeAttributes(u),d[0],d[1],c.getNodeAttributes(d[0]),c.getNodeAttributes(d[1]),c.isUndirected(u)))},o.fromEntry=function(c,u,d,h,f,b,y){return l(n(c,u,d,h,f,b,y))},o.fromPartialEntry=function(c,u,d,h){return l(n(c,u,d,h))},o.fromMinimalEntry=function(c,u){return l(n(c,u))}):(o.fromAttributes=s,o.fromGraph=s,o.fromEntry=s,o.fromMinimalEntry=s),o}return kt.createNodeValueGetter=t,kt.createEdgeValueGetter=r,kt.createEdgeWeightGetter=function(n){return r(n,e)},kt}var xr,bo;function hs(){if(bo)return xr;bo=1;const{createNodeValueGetter:e,createEdgeValueGetter:t}=Nn();return xr=function(n,a,o){const{nodeXAttribute:l,nodeYAttribute:i}=o,{attraction:s,repulsion:c,gravity:u,inertia:d,maxMove:h}=o.settings;let{shouldSkipNode:f,shouldSkipEdge:b,isNodeFixed:y}=o;y=e(y),f=e(f,!1),b=t(b,!1);const k=n.filterNodes((j,A)=>!f.fromEntry(j,A)),I=k.length;for(let j=0;j<I;j++){const A=k[j],N=n.getNodeAttributes(A),z=a[A];z?a[A]={dx:z.dx*d,dy:z.dy*d,x:N[l]||0,y:N[i]||0}:a[A]={dx:0,dy:0,x:N[l]||0,y:N[i]||0}}if(c)for(let j=0;j<I;j++){const A=k[j],N=a[A];for(let z=j+1;z<I;z++){const m=k[z],_=a[m],x=_.x-N.x,T=_.y-N.y,R=Math.sqrt(x*x+T*T)||1,O=c/R*x,w=c/R*T;N.dx-=O,N.dy-=w,_.dx+=O,_.dy+=w}}if(s&&n.forEachEdge((j,A,N,z,m,_,x)=>{if(N===z||f.fromEntry(N,m)||f.fromEntry(z,_)||b.fromEntry(j,A,N,z,m,_,x))return;const T=a[N],R=a[z],O=R.x-T.x,w=R.y-T.y,H=Math.sqrt(O*O+w*w)||1,K=s*H*O,D=s*H*w;T.dx+=K,T.dy+=D,R.dx-=K,R.dy-=D}),u)for(let j=0;j<I;j++){const A=k[j],N=a[A],{x:z,y:m}=N,_=Math.sqrt(z*z+m*m)||1;a[A].dx-=z*u*_,a[A].dy-=m*u*_}const E=!1;for(let j=0;j<I;j++){const A=k[j],N=a[A],z=Math.sqrt(N.dx*N.dx+N.dy*N.dy);z>h&&(N.dx*=h/z,N.dy*=h/z),y.fromGraph(n,A)?N.fixed=!0:(N.x+=N.dx,N.y+=N.dy,N.fixed=!1)}return{converged:E}},xr}var Pt={},wo;function gs(){return wo||(wo=1,Pt.assignLayoutChanges=function(e,t,r){const{nodeXAttribute:n,nodeYAttribute:a}=r;e.updateEachNodeAttributes((o,l)=>{const i=t[o];return!i||i.fixed||(l[n]=i.x,l[a]=i.y),l},{attributes:["x","y"]})},Pt.collectLayoutChanges=function(e){const t={};for(const r in e){const n=e[r];t[r]={x:n.x,y:n.y}}return t}),Pt}var Sr,xo;function ps(){return xo||(xo=1,Sr={nodeXAttribute:"x",nodeYAttribute:"y",isNodeFixed:"fixed",shouldSkipNode:null,shouldSkipEdge:null,settings:{attraction:5e-4,repulsion:.1,gravity:1e-4,inertia:.6,maxMove:200}}),Sr}var _r,So;function Bd(){if(So)return _r;So=1;const e=We(),t=At(),r=hs(),n=gs(),a=ps();function o(i,s,c){if(!e(s))throw new Error("graphology-layout-force: the given graph is not a valid graphology instance.");typeof c=="number"?c={maxIterations:c}:c=c||{};const u=c.maxIterations;if(c=t(c,a),typeof u!="number"||u<=0)throw new Error("graphology-layout-force: you should provide a positive number of maximum iterations.");const d={};let h=null,f;for(f=0;f<u&&(h=r(s,d,c),!h.converged);f++);if(i){n.assignLayoutChanges(s,d,c);return}return n.collectLayoutChanges(d)}const l=o.bind(null,!1);return l.assign=o.bind(null,!0),_r=l,_r}var Vd=Bd();const Ud=He(Vd);var Er,_o;function qd(){if(_o)return Er;_o=1;const e=We(),t=At(),r=hs(),n=gs(),a=ps();function o(l,i){if(!e(l))throw new Error("graphology-layout-force/worker: the given graph is not a valid graphology instance.");i=t(i,a),this.callbacks={},i.onConverged&&(this.callbacks.onConverged=i.onConverged),this.graph=l,this.params=i,this.nodeStates={},this.frameID=null,this.running=!1,this.killed=!1}return o.prototype.isRunning=function(){return this.running},o.prototype.runFrame=function(){let{converged:l}=r(this.graph,this.nodeStates,this.params);n.assignLayoutChanges(this.graph,this.nodeStates,this.params),l=!1,l?(this.callbacks.onConverged&&this.callbacks.onConverged(),this.stop()):this.frameID=window.requestAnimationFrame(()=>this.runFrame())},o.prototype.stop=function(){return this.running=!1,this.frameID!==null&&(window.cancelAnimationFrame(this.frameID),this.frameID=null),this},o.prototype.start=function(){if(this.killed)throw new Error("graphology-layout-force/worker.start: layout was killed.");this.running||(this.running=!0,this.runFrame())},o.prototype.kill=function(){this.stop(),delete this.nodeStates,this.killed=!0},Er=o,Er}var Wd=qd();const Xd=He(Wd);function Yd(e={maxIterations:100}){return _t(Ud,e)}function Kd(e={}){return jn(Xd,e)}var Cr,Eo;function Qd(){if(Eo)return Cr;Eo=1;var e=0,t=1,r=2,n=3,a=4,o=5,l=6,i=7,s=8,c=9,u=0,d=1,h=2,f=0,b=1,y=2,k=3,I=4,E=5,j=6,A=7,N=8,z=3,m=10,_=3,x=9,T=10;return Cr=function(O,w,H){var K,D,C,S,B,ae,M,v,P,V,$=w.length,J=H.length,X=O.adjustSizes,Y=O.barnesHutTheta*O.barnesHutTheta,ie,ne,se,F,Q,q,U,L=[];for(C=0;C<$;C+=m)w[C+a]=w[C+r],w[C+o]=w[C+n],w[C+r]=0,w[C+n]=0;if(O.outboundAttractionDistribution){for(ie=0,C=0;C<$;C+=m)ie+=w[C+l];ie/=$/m}if(O.barnesHutOptimize){var oe=1/0,ue=-1/0,re=1/0,ee=-1/0,G,ge,pe;for(C=0;C<$;C+=m)oe=Math.min(oe,w[C+e]),ue=Math.max(ue,w[C+e]),re=Math.min(re,w[C+t]),ee=Math.max(ee,w[C+t]);var ye=ue-oe,we=ee-re;for(ye>we?(re-=(ye-we)/2,ee=re+ye):(oe-=(we-ye)/2,ue=oe+we),L[0+f]=-1,L[0+b]=(oe+ue)/2,L[0+y]=(re+ee)/2,L[0+k]=Math.max(ue-oe,ee-re),L[0+I]=-1,L[0+E]=-1,L[0+j]=0,L[0+A]=0,L[0+N]=0,K=1,C=0;C<$;C+=m)for(D=0,pe=z;;)if(L[D+E]>=0){w[C+e]<L[D+b]?w[C+t]<L[D+y]?G=L[D+E]:G=L[D+E]+x:w[C+t]<L[D+y]?G=L[D+E]+x*2:G=L[D+E]+x*3,L[D+A]=(L[D+A]*L[D+j]+w[C+e]*w[C+l])/(L[D+j]+w[C+l]),L[D+N]=(L[D+N]*L[D+j]+w[C+t]*w[C+l])/(L[D+j]+w[C+l]),L[D+j]+=w[C+l],D=G;continue}else if(L[D+f]<0){L[D+f]=C;break}else{if(L[D+E]=K*x,v=L[D+k]/2,P=L[D+E],L[P+f]=-1,L[P+b]=L[D+b]-v,L[P+y]=L[D+y]-v,L[P+k]=v,L[P+I]=P+x,L[P+E]=-1,L[P+j]=0,L[P+A]=0,L[P+N]=0,P+=x,L[P+f]=-1,L[P+b]=L[D+b]-v,L[P+y]=L[D+y]+v,L[P+k]=v,L[P+I]=P+x,L[P+E]=-1,L[P+j]=0,L[P+A]=0,L[P+N]=0,P+=x,L[P+f]=-1,L[P+b]=L[D+b]+v,L[P+y]=L[D+y]-v,L[P+k]=v,L[P+I]=P+x,L[P+E]=-1,L[P+j]=0,L[P+A]=0,L[P+N]=0,P+=x,L[P+f]=-1,L[P+b]=L[D+b]+v,L[P+y]=L[D+y]+v,L[P+k]=v,L[P+I]=L[D+I],L[P+E]=-1,L[P+j]=0,L[P+A]=0,L[P+N]=0,K+=4,w[L[D+f]+e]<L[D+b]?w[L[D+f]+t]<L[D+y]?G=L[D+E]:G=L[D+E]+x:w[L[D+f]+t]<L[D+y]?G=L[D+E]+x*2:G=L[D+E]+x*3,L[D+j]=w[L[D+f]+l],L[D+A]=w[L[D+f]+e],L[D+N]=w[L[D+f]+t],L[G+f]=L[D+f],L[D+f]=-1,w[C+e]<L[D+b]?w[C+t]<L[D+y]?ge=L[D+E]:ge=L[D+E]+x:w[C+t]<L[D+y]?ge=L[D+E]+x*2:ge=L[D+E]+x*3,G===ge)if(pe--){D=G;continue}else{pe=z;break}L[ge+f]=C;break}}if(O.barnesHutOptimize)for(ne=O.scalingRatio,C=0;C<$;C+=m)for(D=0;;)if(L[D+E]>=0)if(q=Math.pow(w[C+e]-L[D+A],2)+Math.pow(w[C+t]-L[D+N],2),V=L[D+k],4*V*V/q<Y){if(se=w[C+e]-L[D+A],F=w[C+t]-L[D+N],X===!0?q>0?(U=ne*w[C+l]*L[D+j]/q,w[C+r]+=se*U,w[C+n]+=F*U):q<0&&(U=-ne*w[C+l]*L[D+j]/Math.sqrt(q),w[C+r]+=se*U,w[C+n]+=F*U):q>0&&(U=ne*w[C+l]*L[D+j]/q,w[C+r]+=se*U,w[C+n]+=F*U),D=L[D+I],D<0)break;continue}else{D=L[D+E];continue}else{if(ae=L[D+f],ae>=0&&ae!==C&&(se=w[C+e]-w[ae+e],F=w[C+t]-w[ae+t],q=se*se+F*F,X===!0?q>0?(U=ne*w[C+l]*w[ae+l]/q,w[C+r]+=se*U,w[C+n]+=F*U):q<0&&(U=-ne*w[C+l]*w[ae+l]/Math.sqrt(q),w[C+r]+=se*U,w[C+n]+=F*U):q>0&&(U=ne*w[C+l]*w[ae+l]/q,w[C+r]+=se*U,w[C+n]+=F*U)),D=L[D+I],D<0)break;continue}else for(ne=O.scalingRatio,S=0;S<$;S+=m)for(B=0;B<S;B+=m)se=w[S+e]-w[B+e],F=w[S+t]-w[B+t],X===!0?(q=Math.sqrt(se*se+F*F)-w[S+s]-w[B+s],q>0?(U=ne*w[S+l]*w[B+l]/q/q,w[S+r]+=se*U,w[S+n]+=F*U,w[B+r]-=se*U,w[B+n]-=F*U):q<0&&(U=100*ne*w[S+l]*w[B+l],w[S+r]+=se*U,w[S+n]+=F*U,w[B+r]-=se*U,w[B+n]-=F*U)):(q=Math.sqrt(se*se+F*F),q>0&&(U=ne*w[S+l]*w[B+l]/q/q,w[S+r]+=se*U,w[S+n]+=F*U,w[B+r]-=se*U,w[B+n]-=F*U));for(P=O.gravity/O.scalingRatio,ne=O.scalingRatio,C=0;C<$;C+=m)U=0,se=w[C+e],F=w[C+t],q=Math.sqrt(Math.pow(se,2)+Math.pow(F,2)),O.strongGravityMode?q>0&&(U=ne*w[C+l]*P):q>0&&(U=ne*w[C+l]*P/q),w[C+r]-=se*U,w[C+n]-=F*U;for(ne=1*(O.outboundAttractionDistribution?ie:1),M=0;M<J;M+=_)S=H[M+u],B=H[M+d],v=H[M+h],Q=Math.pow(v,O.edgeWeightInfluence),se=w[S+e]-w[B+e],F=w[S+t]-w[B+t],X===!0?(q=Math.sqrt(se*se+F*F)-w[S+s]-w[B+s],O.linLogMode?O.outboundAttractionDistribution?q>0&&(U=-ne*Q*Math.log(1+q)/q/w[S+l]):q>0&&(U=-ne*Q*Math.log(1+q)/q):O.outboundAttractionDistribution?q>0&&(U=-ne*Q/w[S+l]):q>0&&(U=-ne*Q)):(q=Math.sqrt(Math.pow(se,2)+Math.pow(F,2)),O.linLogMode?O.outboundAttractionDistribution?q>0&&(U=-ne*Q*Math.log(1+q)/q/w[S+l]):q>0&&(U=-ne*Q*Math.log(1+q)/q):O.outboundAttractionDistribution?(q=1,U=-ne*Q/w[S+l]):(q=1,U=-ne*Q)),q>0&&(w[S+r]+=se*U,w[S+n]+=F*U,w[B+r]-=se*U,w[B+n]-=F*U);var de,me,Ne,ke,Te,Pe;if(X===!0)for(C=0;C<$;C+=m)w[C+c]!==1&&(de=Math.sqrt(Math.pow(w[C+r],2)+Math.pow(w[C+n],2)),de>T&&(w[C+r]=w[C+r]*T/de,w[C+n]=w[C+n]*T/de),me=w[C+l]*Math.sqrt((w[C+a]-w[C+r])*(w[C+a]-w[C+r])+(w[C+o]-w[C+n])*(w[C+o]-w[C+n])),Ne=Math.sqrt((w[C+a]+w[C+r])*(w[C+a]+w[C+r])+(w[C+o]+w[C+n])*(w[C+o]+w[C+n]))/2,ke=.1*Math.log(1+Ne)/(1+Math.sqrt(me)),Te=w[C+e]+w[C+r]*(ke/O.slowDown),w[C+e]=Te,Pe=w[C+t]+w[C+n]*(ke/O.slowDown),w[C+t]=Pe);else for(C=0;C<$;C+=m)w[C+c]!==1&&(me=w[C+l]*Math.sqrt((w[C+a]-w[C+r])*(w[C+a]-w[C+r])+(w[C+o]-w[C+n])*(w[C+o]-w[C+n])),Ne=Math.sqrt((w[C+a]+w[C+r])*(w[C+a]+w[C+r])+(w[C+o]+w[C+n])*(w[C+o]+w[C+n]))/2,ke=w[C+i]*Math.log(1+Ne)/(1+Math.sqrt(me)),w[C+i]=Math.min(1,Math.sqrt(ke*(Math.pow(w[C+r],2)+Math.pow(w[C+n],2))/(1+Math.sqrt(me)))),Te=w[C+e]+w[C+r]*(ke/O.slowDown),w[C+e]=Te,Pe=w[C+t]+w[C+n]*(ke/O.slowDown),w[C+t]=Pe);return{}},Cr}var qe={},Co;function ms(){if(Co)return qe;Co=1;var e=10,t=3;return qe.assign=function(r){r=r||{};var n=Array.prototype.slice.call(arguments).slice(1),a,o,l;for(a=0,l=n.length;a<l;a++)if(n[a])for(o in n[a])r[o]=n[a][o];return r},qe.validateSettings=function(r){return"linLogMode"in r&&typeof r.linLogMode!="boolean"?{message:"the `linLogMode` setting should be a boolean."}:"outboundAttractionDistribution"in r&&typeof r.outboundAttractionDistribution!="boolean"?{message:"the `outboundAttractionDistribution` setting should be a boolean."}:"adjustSizes"in r&&typeof r.adjustSizes!="boolean"?{message:"the `adjustSizes` setting should be a boolean."}:"edgeWeightInfluence"in r&&typeof r.edgeWeightInfluence!="number"?{message:"the `edgeWeightInfluence` setting should be a number."}:"scalingRatio"in r&&!(typeof r.scalingRatio=="number"&&r.scalingRatio>=0)?{message:"the `scalingRatio` setting should be a number >= 0."}:"strongGravityMode"in r&&typeof r.strongGravityMode!="boolean"?{message:"the `strongGravityMode` setting should be a boolean."}:"gravity"in r&&!(typeof r.gravity=="number"&&r.gravity>=0)?{message:"the `gravity` setting should be a number >= 0."}:"slowDown"in r&&!(typeof r.slowDown=="number"||r.slowDown>=0)?{message:"the `slowDown` setting should be a number >= 0."}:"barnesHutOptimize"in r&&typeof r.barnesHutOptimize!="boolean"?{message:"the `barnesHutOptimize` setting should be a boolean."}:"barnesHutTheta"in r&&!(typeof r.barnesHutTheta=="number"&&r.barnesHutTheta>=0)?{message:"the `barnesHutTheta` setting should be a number >= 0."}:null},qe.graphToByteArrays=function(r,n){var a=r.order,o=r.size,l={},i,s=new Float32Array(a*e),c=new Float32Array(o*t);return i=0,r.forEachNode(function(u,d){l[u]=i,s[i]=d.x,s[i+1]=d.y,s[i+2]=0,s[i+3]=0,s[i+4]=0,s[i+5]=0,s[i+6]=1,s[i+7]=1,s[i+8]=d.size||1,s[i+9]=d.fixed?1:0,i+=e}),i=0,r.forEachEdge(function(u,d,h,f,b,y,k){var I=l[h],E=l[f],j=n(u,d,h,f,b,y,k);s[I+6]+=j,s[E+6]+=j,c[i]=I,c[i+1]=E,c[i+2]=j,i+=t}),{nodes:s,edges:c}},qe.assignLayoutChanges=function(r,n,a){var o=0;r.updateEachNodeAttributes(function(l,i){return i.x=n[o],i.y=n[o+1],o+=e,a?a(l,i):i})},qe.readGraphPositions=function(r,n){var a=0;r.forEachNode(function(o,l){n[a]=l.x,n[a+1]=l.y,a+=e})},qe.collectLayoutChanges=function(r,n,a){for(var o=r.nodes(),l={},i=0,s=0,c=n.length;i<c;i+=e){if(a){var u=Object.assign({},r.getNodeAttributes(o[s]));u.x=n[i],u.y=n[i+1],u=a(o[s],u),l[o[s]]={x:u.x,y:u.y}}else l[o[s]]={x:n[i],y:n[i+1]};s++}return l},qe.createWorker=function(n){var a=window.URL||window.webkitURL,o=n.toString(),l=a.createObjectURL(new Blob(["("+o+").call(this);"],{type:"text/javascript"})),i=new Worker(l);return a.revokeObjectURL(l),i},qe}var kr,ko;function vs(){return ko||(ko=1,kr={linLogMode:!1,outboundAttractionDistribution:!1,adjustSizes:!1,edgeWeightInfluence:1,scalingRatio:1,strongGravityMode:!1,gravity:1,slowDown:1,barnesHutOptimize:!1,barnesHutTheta:.5}),kr}var Tr,To;function Jd(){if(To)return Tr;To=1;var e=We(),t=Nn().createEdgeWeightGetter,r=Qd(),n=ms(),a=vs();function o(s,c,u){if(!e(c))throw new Error("graphology-layout-forceatlas2: the given graph is not a valid graphology instance.");typeof u=="number"&&(u={iterations:u});var d=u.iterations;if(typeof d!="number")throw new Error("graphology-layout-forceatlas2: invalid number of iterations.");if(d<=0)throw new Error("graphology-layout-forceatlas2: you should provide a positive number of iterations.");var h=t("getEdgeWeight"in u?u.getEdgeWeight:"weight").fromEntry,f=typeof u.outputReducer=="function"?u.outputReducer:null,b=n.assign({},a,u.settings),y=n.validateSettings(b);if(y)throw new Error("graphology-layout-forceatlas2: "+y.message);var k=n.graphToByteArrays(c,h),I;for(I=0;I<d;I++)r(b,k.nodes,k.edges);if(s){n.assignLayoutChanges(c,k.nodes,f);return}return n.collectLayoutChanges(c,k.nodes)}function l(s){var c=typeof s=="number"?s:s.order;return{barnesHutOptimize:c>2e3,strongGravityMode:!0,gravity:.05,scalingRatio:10,slowDown:1+Math.log(c)}}var i=o.bind(null,!1);return i.assign=o.bind(null,!0),i.inferSettings=l,Tr=i,Tr}var Zd=Jd();const ef=He(Zd);var Rr,Ro;function tf(){return Ro||(Ro=1,Rr=function(){var t,r,n={};(function(){var o=0,l=1,i=2,s=3,c=4,u=5,d=6,h=7,f=8,b=9,y=0,k=1,I=2,E=0,j=1,A=2,N=3,z=4,m=5,_=6,x=7,T=8,R=3,O=10,w=3,H=9,K=10;n.exports=function(C,S,B){var ae,M,v,P,V,$,J,X,Y,ie,ne=S.length,se=B.length,F=C.adjustSizes,Q=C.barnesHutTheta*C.barnesHutTheta,q,U,L,oe,ue,re,ee,G=[];for(v=0;v<ne;v+=O)S[v+c]=S[v+i],S[v+u]=S[v+s],S[v+i]=0,S[v+s]=0;if(C.outboundAttractionDistribution){for(q=0,v=0;v<ne;v+=O)q+=S[v+d];q/=ne/O}if(C.barnesHutOptimize){var ge=1/0,pe=-1/0,ye=1/0,we=-1/0,de,me,Ne;for(v=0;v<ne;v+=O)ge=Math.min(ge,S[v+o]),pe=Math.max(pe,S[v+o]),ye=Math.min(ye,S[v+l]),we=Math.max(we,S[v+l]);var ke=pe-ge,Te=we-ye;for(ke>Te?(ye-=(ke-Te)/2,we=ye+ke):(ge-=(Te-ke)/2,pe=ge+Te),G[0+E]=-1,G[0+j]=(ge+pe)/2,G[0+A]=(ye+we)/2,G[0+N]=Math.max(pe-ge,we-ye),G[0+z]=-1,G[0+m]=-1,G[0+_]=0,G[0+x]=0,G[0+T]=0,ae=1,v=0;v<ne;v+=O)for(M=0,Ne=R;;)if(G[M+m]>=0){S[v+o]<G[M+j]?S[v+l]<G[M+A]?de=G[M+m]:de=G[M+m]+H:S[v+l]<G[M+A]?de=G[M+m]+H*2:de=G[M+m]+H*3,G[M+x]=(G[M+x]*G[M+_]+S[v+o]*S[v+d])/(G[M+_]+S[v+d]),G[M+T]=(G[M+T]*G[M+_]+S[v+l]*S[v+d])/(G[M+_]+S[v+d]),G[M+_]+=S[v+d],M=de;continue}else if(G[M+E]<0){G[M+E]=v;break}else{if(G[M+m]=ae*H,X=G[M+N]/2,Y=G[M+m],G[Y+E]=-1,G[Y+j]=G[M+j]-X,G[Y+A]=G[M+A]-X,G[Y+N]=X,G[Y+z]=Y+H,G[Y+m]=-1,G[Y+_]=0,G[Y+x]=0,G[Y+T]=0,Y+=H,G[Y+E]=-1,G[Y+j]=G[M+j]-X,G[Y+A]=G[M+A]+X,G[Y+N]=X,G[Y+z]=Y+H,G[Y+m]=-1,G[Y+_]=0,G[Y+x]=0,G[Y+T]=0,Y+=H,G[Y+E]=-1,G[Y+j]=G[M+j]+X,G[Y+A]=G[M+A]-X,G[Y+N]=X,G[Y+z]=Y+H,G[Y+m]=-1,G[Y+_]=0,G[Y+x]=0,G[Y+T]=0,Y+=H,G[Y+E]=-1,G[Y+j]=G[M+j]+X,G[Y+A]=G[M+A]+X,G[Y+N]=X,G[Y+z]=G[M+z],G[Y+m]=-1,G[Y+_]=0,G[Y+x]=0,G[Y+T]=0,ae+=4,S[G[M+E]+o]<G[M+j]?S[G[M+E]+l]<G[M+A]?de=G[M+m]:de=G[M+m]+H:S[G[M+E]+l]<G[M+A]?de=G[M+m]+H*2:de=G[M+m]+H*3,G[M+_]=S[G[M+E]+d],G[M+x]=S[G[M+E]+o],G[M+T]=S[G[M+E]+l],G[de+E]=G[M+E],G[M+E]=-1,S[v+o]<G[M+j]?S[v+l]<G[M+A]?me=G[M+m]:me=G[M+m]+H:S[v+l]<G[M+A]?me=G[M+m]+H*2:me=G[M+m]+H*3,de===me)if(Ne--){M=de;continue}else{Ne=R;break}G[me+E]=v;break}}if(C.barnesHutOptimize)for(U=C.scalingRatio,v=0;v<ne;v+=O)for(M=0;;)if(G[M+m]>=0)if(re=Math.pow(S[v+o]-G[M+x],2)+Math.pow(S[v+l]-G[M+T],2),ie=G[M+N],4*ie*ie/re<Q){if(L=S[v+o]-G[M+x],oe=S[v+l]-G[M+T],F===!0?re>0?(ee=U*S[v+d]*G[M+_]/re,S[v+i]+=L*ee,S[v+s]+=oe*ee):re<0&&(ee=-U*S[v+d]*G[M+_]/Math.sqrt(re),S[v+i]+=L*ee,S[v+s]+=oe*ee):re>0&&(ee=U*S[v+d]*G[M+_]/re,S[v+i]+=L*ee,S[v+s]+=oe*ee),M=G[M+z],M<0)break;continue}else{M=G[M+m];continue}else{if($=G[M+E],$>=0&&$!==v&&(L=S[v+o]-S[$+o],oe=S[v+l]-S[$+l],re=L*L+oe*oe,F===!0?re>0?(ee=U*S[v+d]*S[$+d]/re,S[v+i]+=L*ee,S[v+s]+=oe*ee):re<0&&(ee=-U*S[v+d]*S[$+d]/Math.sqrt(re),S[v+i]+=L*ee,S[v+s]+=oe*ee):re>0&&(ee=U*S[v+d]*S[$+d]/re,S[v+i]+=L*ee,S[v+s]+=oe*ee)),M=G[M+z],M<0)break;continue}else for(U=C.scalingRatio,P=0;P<ne;P+=O)for(V=0;V<P;V+=O)L=S[P+o]-S[V+o],oe=S[P+l]-S[V+l],F===!0?(re=Math.sqrt(L*L+oe*oe)-S[P+f]-S[V+f],re>0?(ee=U*S[P+d]*S[V+d]/re/re,S[P+i]+=L*ee,S[P+s]+=oe*ee,S[V+i]-=L*ee,S[V+s]-=oe*ee):re<0&&(ee=100*U*S[P+d]*S[V+d],S[P+i]+=L*ee,S[P+s]+=oe*ee,S[V+i]-=L*ee,S[V+s]-=oe*ee)):(re=Math.sqrt(L*L+oe*oe),re>0&&(ee=U*S[P+d]*S[V+d]/re/re,S[P+i]+=L*ee,S[P+s]+=oe*ee,S[V+i]-=L*ee,S[V+s]-=oe*ee));for(Y=C.gravity/C.scalingRatio,U=C.scalingRatio,v=0;v<ne;v+=O)ee=0,L=S[v+o],oe=S[v+l],re=Math.sqrt(Math.pow(L,2)+Math.pow(oe,2)),C.strongGravityMode?re>0&&(ee=U*S[v+d]*Y):re>0&&(ee=U*S[v+d]*Y/re),S[v+i]-=L*ee,S[v+s]-=oe*ee;for(U=1*(C.outboundAttractionDistribution?q:1),J=0;J<se;J+=w)P=B[J+y],V=B[J+k],X=B[J+I],ue=Math.pow(X,C.edgeWeightInfluence),L=S[P+o]-S[V+o],oe=S[P+l]-S[V+l],F===!0?(re=Math.sqrt(L*L+oe*oe)-S[P+f]-S[V+f],C.linLogMode?C.outboundAttractionDistribution?re>0&&(ee=-U*ue*Math.log(1+re)/re/S[P+d]):re>0&&(ee=-U*ue*Math.log(1+re)/re):C.outboundAttractionDistribution?re>0&&(ee=-U*ue/S[P+d]):re>0&&(ee=-U*ue)):(re=Math.sqrt(Math.pow(L,2)+Math.pow(oe,2)),C.linLogMode?C.outboundAttractionDistribution?re>0&&(ee=-U*ue*Math.log(1+re)/re/S[P+d]):re>0&&(ee=-U*ue*Math.log(1+re)/re):C.outboundAttractionDistribution?(re=1,ee=-U*ue/S[P+d]):(re=1,ee=-U*ue)),re>0&&(S[P+i]+=L*ee,S[P+s]+=oe*ee,S[V+i]-=L*ee,S[V+s]-=oe*ee);var Pe,Ye,Ke,Re,st,ze;if(F===!0)for(v=0;v<ne;v+=O)S[v+b]!==1&&(Pe=Math.sqrt(Math.pow(S[v+i],2)+Math.pow(S[v+s],2)),Pe>K&&(S[v+i]=S[v+i]*K/Pe,S[v+s]=S[v+s]*K/Pe),Ye=S[v+d]*Math.sqrt((S[v+c]-S[v+i])*(S[v+c]-S[v+i])+(S[v+u]-S[v+s])*(S[v+u]-S[v+s])),Ke=Math.sqrt((S[v+c]+S[v+i])*(S[v+c]+S[v+i])+(S[v+u]+S[v+s])*(S[v+u]+S[v+s]))/2,Re=.1*Math.log(1+Ke)/(1+Math.sqrt(Ye)),st=S[v+o]+S[v+i]*(Re/C.slowDown),S[v+o]=st,ze=S[v+l]+S[v+s]*(Re/C.slowDown),S[v+l]=ze);else for(v=0;v<ne;v+=O)S[v+b]!==1&&(Ye=S[v+d]*Math.sqrt((S[v+c]-S[v+i])*(S[v+c]-S[v+i])+(S[v+u]-S[v+s])*(S[v+u]-S[v+s])),Ke=Math.sqrt((S[v+c]+S[v+i])*(S[v+c]+S[v+i])+(S[v+u]+S[v+s])*(S[v+u]+S[v+s]))/2,Re=S[v+h]*Math.log(1+Ke)/(1+Math.sqrt(Ye)),S[v+h]=Math.min(1,Math.sqrt(Re*(Math.pow(S[v+i],2)+Math.pow(S[v+s],2))/(1+Math.sqrt(Ye)))),st=S[v+o]+S[v+i]*(Re/C.slowDown),S[v+o]=st,ze=S[v+l]+S[v+s]*(Re/C.slowDown),S[v+l]=ze);return{}}})();var a=n.exports;self.addEventListener("message",function(o){var l=o.data;t=new Float32Array(l.nodes),l.edges&&(r=new Float32Array(l.edges)),a(l.settings,t,r),self.postMessage({nodes:t.buffer},[t.buffer])})}),Rr}var Ar,Ao;function rf(){if(Ao)return Ar;Ao=1;var e=tf(),t=We(),r=Nn().createEdgeWeightGetter,n=ms(),a=vs();function o(l,i){if(i=i||{},!t(l))throw new Error("graphology-layout-forceatlas2/worker: the given graph is not a valid graphology instance.");var s=r("getEdgeWeight"in i?i.getEdgeWeight:"weight").fromEntry,c=n.assign({},a,i.settings),u=n.validateSettings(c);if(u)throw new Error("graphology-layout-forceatlas2/worker: "+u.message);this.worker=null,this.graph=l,this.settings=c,this.getEdgeWeight=s,this.matrices=null,this.running=!1,this.killed=!1,this.outputReducer=typeof i.outputReducer=="function"?i.outputReducer:null,this.handleMessage=this.handleMessage.bind(this);var d=void 0,h=this;this.handleGraphUpdate=function(){h.worker&&h.worker.terminate(),d&&clearTimeout(d),d=setTimeout(function(){d=void 0,h.spawnWorker()},0)},l.on("nodeAdded",this.handleGraphUpdate),l.on("edgeAdded",this.handleGraphUpdate),l.on("nodeDropped",this.handleGraphUpdate),l.on("edgeDropped",this.handleGraphUpdate),this.spawnWorker()}return o.prototype.isRunning=function(){return this.running},o.prototype.spawnWorker=function(){this.worker&&this.worker.terminate(),this.worker=n.createWorker(e),this.worker.addEventListener("message",this.handleMessage),this.running&&(this.running=!1,this.start())},o.prototype.handleMessage=function(l){if(this.running){var i=new Float32Array(l.data.nodes);n.assignLayoutChanges(this.graph,i,this.outputReducer),this.outputReducer&&n.readGraphPositions(this.graph,i),this.matrices.nodes=i,this.askForIterations()}},o.prototype.askForIterations=function(l){var i=this.matrices,s={settings:this.settings,nodes:i.nodes.buffer},c=[i.nodes.buffer];return l&&(s.edges=i.edges.buffer,c.push(i.edges.buffer)),this.worker.postMessage(s,c),this},o.prototype.start=function(){if(this.killed)throw new Error("graphology-layout-forceatlas2/worker.start: layout was killed.");return this.running?this:(this.matrices=n.graphToByteArrays(this.graph,this.getEdgeWeight),this.running=!0,this.askForIterations(!0),this)},o.prototype.stop=function(){return this.running=!1,this},o.prototype.kill=function(){if(this.killed)return this;this.running=!1,this.killed=!0,this.matrices=null,this.worker.terminate(),this.graph.removeListener("nodeAdded",this.handleGraphUpdate),this.graph.removeListener("edgeAdded",this.handleGraphUpdate),this.graph.removeListener("nodeDropped",this.handleGraphUpdate),this.graph.removeListener("edgeDropped",this.handleGraphUpdate)},Ar=o,Ar}var nf=rf();const of=He(nf);function ys(e={iterations:100}){return _t(ef,e)}function af(e={}){return jn(of,e)}var jr,jo;function sf(){if(jo)return jr;jo=1;var e=0,t=1,r=2,n=3;function a(l,i){return l+"§"+i}function o(){return .01*(.5-Math.random())}return jr=function(i,s){var c=i.margin,u=i.ratio,d=i.expansion,h=i.gridSize,f=i.speed,b,y,k,I,E,j,A=!0,N=s.length,z=N/n|0,m=new Float32Array(z),_=new Float32Array(z),x=1/0,T=1/0,R=-1/0,O=-1/0;for(b=0;b<N;b+=n)k=s[b+e],I=s[b+t],j=s[b+r]*u+c,x=Math.min(x,k-j),R=Math.max(R,k+j),T=Math.min(T,I-j),O=Math.max(O,I+j);var w=R-x,H=O-T,K=(x+R)/2,D=(T+O)/2;x=K-d*w/2,R=K+d*w/2,T=D-d*H/2,O=D+d*H/2;var C=new Array(h*h),S=C.length,B;for(B=0;B<S;B++)C[B]=[];var ae,M,v,P,V,$,J,X,Y,ie;for(b=0;b<N;b+=n)for(k=s[b+e],I=s[b+t],j=s[b+r]*u+c,ae=k-j,M=k+j,v=I-j,P=I+j,V=Math.floor(h*(ae-x)/(R-x)),$=Math.floor(h*(M-x)/(R-x)),J=Math.floor(h*(v-T)/(O-T)),X=Math.floor(h*(P-T)/(O-T)),Y=V;Y<=$;Y++)for(ie=J;ie<=X;ie++)C[Y*h+ie].push(b);var ne,se=new Set,F,Q,q,U,L,oe,ue,re,ee,G,ge,pe,ye;for(B=0;B<S;B++)for(ne=C[B],b=0,E=ne.length;b<E;b++)for(F=ne[b],q=s[F+e],L=s[F+t],ue=s[F+r],y=b+1;y<E;y++)Q=ne[y],ee=a(F,Q),!(S>1&&se.has(ee))&&(S>1&&se.add(ee),U=s[Q+e],oe=s[Q+t],re=s[Q+r],G=U-q,ge=oe-L,pe=Math.sqrt(G*G+ge*ge),ye=pe<ue*u+c+(re*u+c),ye&&(A=!1,Q=Q/n|0,pe>0?(m[Q]+=G/pe*(1+ue),_[Q]+=ge/pe*(1+ue)):(m[Q]+=w*o(),_[Q]+=H*o())));for(b=0,y=0;b<N;b+=n,y++)s[b+e]+=m[y]*.1*f,s[b+t]+=_[y]*.1*f;return{converged:A}},jr}var lt={},No;function bs(){if(No)return lt;No=1;var e=3;return lt.validateSettings=function(t){return"gridSize"in t&&typeof t.gridSize!="number"||t.gridSize<=0?{message:"the `gridSize` setting should be a positive number."}:"margin"in t&&typeof t.margin!="number"||t.margin<0?{message:"the `margin` setting should be 0 or a positive number."}:"expansion"in t&&typeof t.expansion!="number"||t.expansion<=0?{message:"the `expansion` setting should be a positive number."}:"ratio"in t&&typeof t.ratio!="number"||t.ratio<=0?{message:"the `ratio` setting should be a positive number."}:"speed"in t&&typeof t.speed!="number"||t.speed<=0?{message:"the `speed` setting should be a positive number."}:null},lt.graphToByteArray=function(t,r){var n=t.order,a=new Float32Array(n*e),o=0;return t.forEachNode(function(l,i){typeof r=="function"&&(i=r(l,i)),a[o]=i.x,a[o+1]=i.y,a[o+2]=i.size||1,o+=e}),a},lt.assignLayoutChanges=function(t,r,n){var a=0;t.forEachNode(function(o){var l={x:r[a],y:r[a+1]};typeof n=="function"&&(l=n(o,l)),t.mergeNodeAttributes(o,l),a+=e})},lt.collectLayoutChanges=function(t,r,n){var a={},o=0;return t.forEachNode(function(l){var i={x:r[o],y:r[o+1]};typeof n=="function"&&(i=n(l,i)),a[l]=i,o+=e}),a},lt.createWorker=function(r){var n=window.URL||window.webkitURL,a=r.toString(),o=n.createObjectURL(new Blob(["("+a+").call(this);"],{type:"text/javascript"})),l=new Worker(o);return n.revokeObjectURL(o),l},lt}var Nr,Io;function ws(){return Io||(Io=1,Nr={gridSize:20,margin:5,expansion:1.1,ratio:1,speed:3}),Nr}var Ir,Lo;function lf(){if(Lo)return Ir;Lo=1;var e=We(),t=sf(),r=bs(),n=ws(),a=500;function o(i,s,c){if(!e(s))throw new Error("graphology-layout-noverlap: the given graph is not a valid graphology instance.");typeof c=="number"?c={maxIterations:c}:c=c||{};var u=c.maxIterations||a;if(typeof u!="number"||u<=0)throw new Error("graphology-layout-force: you should provide a positive number of maximum iterations.");var d=Object.assign({},n,c.settings),h=r.validateSettings(d);if(h)throw new Error("graphology-layout-noverlap: "+h.message);var f=r.graphToByteArray(s,c.inputReducer),b=!1,y;for(y=0;y<u&&!b;y++)b=t(d,f).converged;if(i){r.assignLayoutChanges(s,f,c.outputReducer);return}return r.collectLayoutChanges(s,f,c.outputReducer)}var l=o.bind(null,!1);return l.assign=o.bind(null,!0),Ir=l,Ir}var cf=lf();const uf=He(cf);var Lr,Po;function df(){return Po||(Po=1,Lr=function(){var t,r={};(function(){var a=0,o=1,l=2,i=3;function s(u,d){return u+"§"+d}function c(){return .01*(.5-Math.random())}r.exports=function(d,h){var f=d.margin,b=d.ratio,y=d.expansion,k=d.gridSize,I=d.speed,E,j,A,N,z,m,_=!0,x=h.length,T=x/i|0,R=new Float32Array(T),O=new Float32Array(T),w=1/0,H=1/0,K=-1/0,D=-1/0;for(E=0;E<x;E+=i)A=h[E+a],N=h[E+o],m=h[E+l]*b+f,w=Math.min(w,A-m),K=Math.max(K,A+m),H=Math.min(H,N-m),D=Math.max(D,N+m);var C=K-w,S=D-H,B=(w+K)/2,ae=(H+D)/2;w=B-y*C/2,K=B+y*C/2,H=ae-y*S/2,D=ae+y*S/2;var M=new Array(k*k),v=M.length,P;for(P=0;P<v;P++)M[P]=[];var V,$,J,X,Y,ie,ne,se,F,Q;for(E=0;E<x;E+=i)for(A=h[E+a],N=h[E+o],m=h[E+l]*b+f,V=A-m,$=A+m,J=N-m,X=N+m,Y=Math.floor(k*(V-w)/(K-w)),ie=Math.floor(k*($-w)/(K-w)),ne=Math.floor(k*(J-H)/(D-H)),se=Math.floor(k*(X-H)/(D-H)),F=Y;F<=ie;F++)for(Q=ne;Q<=se;Q++)M[F*k+Q].push(E);var q,U=new Set,L,oe,ue,re,ee,G,ge,pe,ye,we,de,me,Ne;for(P=0;P<v;P++)for(q=M[P],E=0,z=q.length;E<z;E++)for(L=q[E],ue=h[L+a],ee=h[L+o],ge=h[L+l],j=E+1;j<z;j++)oe=q[j],ye=s(L,oe),!(v>1&&U.has(ye))&&(v>1&&U.add(ye),re=h[oe+a],G=h[oe+o],pe=h[oe+l],we=re-ue,de=G-ee,me=Math.sqrt(we*we+de*de),Ne=me<ge*b+f+(pe*b+f),Ne&&(_=!1,oe=oe/i|0,me>0?(R[oe]+=we/me*(1+ge),O[oe]+=de/me*(1+ge)):(R[oe]+=C*c(),O[oe]+=S*c())));for(E=0,j=0;E<x;E+=i,j++)h[E+a]+=R[j]*.1*I,h[E+o]+=O[j]*.1*I;return{converged:_}}})();var n=r.exports;self.addEventListener("message",function(a){var o=a.data;t=new Float32Array(o.nodes);var l=n(o.settings,t);self.postMessage({result:l,nodes:t.buffer},[t.buffer])})}),Lr}var Pr,zo;function ff(){if(zo)return Pr;zo=1;var e=df(),t=We(),r=bs(),n=ws();function a(o,l){if(l=l||{},!t(o))throw new Error("graphology-layout-noverlap/worker: the given graph is not a valid graphology instance.");var i=Object.assign({},n,l.settings),s=r.validateSettings(i);if(s)throw new Error("graphology-layout-noverlap/worker: "+s.message);this.worker=null,this.graph=o,this.settings=i,this.matrices=null,this.running=!1,this.killed=!1,this.inputReducer=l.inputReducer,this.outputReducer=l.outputReducer,this.callbacks={onConverged:typeof l.onConverged=="function"?l.onConverged:null},this.handleMessage=this.handleMessage.bind(this);var c=!1,u=this;this.handleAddition=function(){c||(c=!0,u.spawnWorker(),setTimeout(function(){c=!1},0))},o.on("nodeAdded",this.handleAddition),o.on("edgeAdded",this.handleAddition),this.spawnWorker()}return a.prototype.isRunning=function(){return this.running},a.prototype.spawnWorker=function(){this.worker&&this.worker.terminate(),this.worker=r.createWorker(e),this.worker.addEventListener("message",this.handleMessage),this.running&&(this.running=!1,this.start())},a.prototype.handleMessage=function(o){if(this.running){var l=new Float32Array(o.data.nodes);if(r.assignLayoutChanges(this.graph,l,this.outputReducer),this.matrices.nodes=l,o.data.result.converged){this.callbacks.onConverged&&this.callbacks.onConverged(),this.stop();return}this.askForIterations()}},a.prototype.askForIterations=function(){var o=this.matrices,l={settings:this.settings,nodes:o.nodes.buffer},i=[o.nodes.buffer];return this.worker.postMessage(l,i),this},a.prototype.start=function(){if(this.killed)throw new Error("graphology-layout-noverlap/worker.start: layout was killed.");return this.running?this:(this.matrices={nodes:r.graphToByteArray(this.graph,this.inputReducer)},this.running=!0,this.askForIterations(),this)},a.prototype.stop=function(){return this.running=!1,this},a.prototype.kill=function(){if(this.killed)return this;this.running=!1,this.killed=!0,this.matrices=null,this.worker.terminate(),this.graph.removeListener("nodeAdded",this.handleAddition),this.graph.removeListener("edgeAdded",this.handleAddition)},Pr=a,Pr}var hf=ff();const gf=He(hf);function pf(e={}){return _t(uf,e)}function mf(e={}){return jn(gf,e)}var zr,Do;function vf(){if(Do)return zr;Do=1;var e=At(),t=We(),r={dimensions:["x","y"],center:.5,rng:Math.random,scale:1};function n(o,l,i){if(!t(l))throw new Error("graphology-layout/random: the given graph is not a valid graphology instance.");i=e(i,r);var s=i.dimensions;if(!Array.isArray(s)||s.length<1)throw new Error("graphology-layout/random: given dimensions are invalid.");var c=s.length,u=i.center,d=i.rng,h=i.scale,f=(u-.5)*h;function b(k){for(var I=0;I<c;I++)k[s[I]]=d()*h+f;return k}if(!o){var y={};return l.forEachNode(function(k){y[k]=b({})}),y}l.updateEachNodeAttributes(function(k,I){return b(I),I},{attributes:s})}var a=n.bind(null,!1);return a.assign=n.bind(null,!0),zr=a,zr}var yf=vf();const bf=He(yf);function wf(e={}){return _t(bf,e)}var Oo=1,xf=.9,Sf=.8,_f=.17,Dr=.1,Or=.999,Ef=.9999,Cf=.99,kf=/[\\\/_+.#"@\[\(\{&]/,Tf=/[\\\/_+.#"@\[\(\{&]/g,Rf=/[\s-]/,xs=/[\s-]/g;function fn(e,t,r,n,a,o,l){if(o===t.length)return a===e.length?Oo:Cf;var i=`${a},${o}`;if(l[i]!==void 0)return l[i];for(var s=n.charAt(o),c=r.indexOf(s,a),u=0,d,h,f,b;c>=0;)d=fn(e,t,r,n,c+1,o+1,l),d>u&&(c===a?d*=Oo:kf.test(e.charAt(c-1))?(d*=Sf,f=e.slice(a,c-1).match(Tf),f&&a>0&&(d*=Math.pow(Or,f.length))):Rf.test(e.charAt(c-1))?(d*=xf,b=e.slice(a,c-1).match(xs),b&&a>0&&(d*=Math.pow(Or,b.length))):(d*=_f,a>0&&(d*=Math.pow(Or,c-a))),e.charAt(c)!==t.charAt(o)&&(d*=Ef)),(d<Dr&&r.charAt(c-1)===n.charAt(o+1)||n.charAt(o+1)===n.charAt(o)&&r.charAt(c-1)!==n.charAt(o))&&(h=fn(e,t,r,n,c+1,o+2,l),h*Dr>d&&(d=h*Dr)),d>u&&(u=d),c=r.indexOf(s,c+1);return l[i]=u,u}function Go(e){return e.toLowerCase().replace(xs," ")}function Af(e,t,r){return e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,fn(e,t,Go(e),Go(t),0,0,{})}var Gr={exports:{}},Fr={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fo;function jf(){if(Fo)return Fr;Fo=1;var e=gi();function t(d,h){return d===h&&(d!==0||1/d===1/h)||d!==d&&h!==h}var r=typeof Object.is=="function"?Object.is:t,n=e.useState,a=e.useEffect,o=e.useLayoutEffect,l=e.useDebugValue;function i(d,h){var f=h(),b=n({inst:{value:f,getSnapshot:h}}),y=b[0].inst,k=b[1];return o(function(){y.value=f,y.getSnapshot=h,s(y)&&k({inst:y})},[d,f,h]),a(function(){return s(y)&&k({inst:y}),d(function(){s(y)&&k({inst:y})})},[d]),l(f),f}function s(d){var h=d.getSnapshot;d=d.value;try{var f=h();return!r(d,f)}catch{return!0}}function c(d,h){return h()}var u=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?c:i;return Fr.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:u,Fr}var Mo;function Nf(){return Mo||(Mo=1,Gr.exports=jf()),Gr.exports}var If=Nf(),Tt='[cmdk-group=""]',Mr='[cmdk-group-items=""]',Lf='[cmdk-group-heading=""]',In='[cmdk-item=""]',$o=`${In}:not([aria-disabled="true"])`,hn="cmdk-item-select",dt="data-value",Pf=(e,t,r)=>Af(e,t,r),Ss=p.createContext(void 0),jt=()=>p.useContext(Ss),_s=p.createContext(void 0),Ln=()=>p.useContext(_s),Es=p.createContext(void 0),Cs=p.forwardRef((e,t)=>{let r=yt(()=>{var v,P;return{search:"",value:(P=(v=e.value)!=null?v:e.defaultValue)!=null?P:"",filtered:{count:0,items:new Map,groups:new Set}}}),n=yt(()=>new Set),a=yt(()=>new Map),o=yt(()=>new Map),l=yt(()=>new Set),i=ks(e),{label:s,children:c,value:u,onValueChange:d,filter:h,shouldFilter:f,loop:b,disablePointerSelection:y=!1,vimBindings:k=!0,...I}=e,E=ft(),j=ft(),A=ft(),N=p.useRef(null),z=Uf();gt(()=>{if(u!==void 0){let v=u.trim();r.current.value=v,m.emit()}},[u]),gt(()=>{z(6,w)},[]);let m=p.useMemo(()=>({subscribe:v=>(l.current.add(v),()=>l.current.delete(v)),snapshot:()=>r.current,setState:(v,P,V)=>{var $,J,X;if(!Object.is(r.current[v],P)){if(r.current[v]=P,v==="search")O(),T(),z(1,R);else if(v==="value"&&(V||z(5,w),(($=i.current)==null?void 0:$.value)!==void 0)){let Y=P??"";(X=(J=i.current).onValueChange)==null||X.call(J,Y);return}m.emit()}},emit:()=>{l.current.forEach(v=>v())}}),[]),_=p.useMemo(()=>({value:(v,P,V)=>{var $;P!==(($=o.current.get(v))==null?void 0:$.value)&&(o.current.set(v,{value:P,keywords:V}),r.current.filtered.items.set(v,x(P,V)),z(2,()=>{T(),m.emit()}))},item:(v,P)=>(n.current.add(v),P&&(a.current.has(P)?a.current.get(P).add(v):a.current.set(P,new Set([v]))),z(3,()=>{O(),T(),r.current.value||R(),m.emit()}),()=>{o.current.delete(v),n.current.delete(v),r.current.filtered.items.delete(v);let V=H();z(4,()=>{O(),(V==null?void 0:V.getAttribute("id"))===v&&R(),m.emit()})}),group:v=>(a.current.has(v)||a.current.set(v,new Set),()=>{o.current.delete(v),a.current.delete(v)}),filter:()=>i.current.shouldFilter,label:s||e["aria-label"],getDisablePointerSelection:()=>i.current.disablePointerSelection,listId:E,inputId:A,labelId:j,listInnerRef:N}),[]);function x(v,P){var V,$;let J=($=(V=i.current)==null?void 0:V.filter)!=null?$:Pf;return v?J(v,r.current.search,P):0}function T(){if(!r.current.search||i.current.shouldFilter===!1)return;let v=r.current.filtered.items,P=[];r.current.filtered.groups.forEach($=>{let J=a.current.get($),X=0;J.forEach(Y=>{let ie=v.get(Y);X=Math.max(ie,X)}),P.push([$,X])});let V=N.current;K().sort(($,J)=>{var X,Y;let ie=$.getAttribute("id"),ne=J.getAttribute("id");return((X=v.get(ne))!=null?X:0)-((Y=v.get(ie))!=null?Y:0)}).forEach($=>{let J=$.closest(Mr);J?J.appendChild($.parentElement===J?$:$.closest(`${Mr} > *`)):V.appendChild($.parentElement===V?$:$.closest(`${Mr} > *`))}),P.sort(($,J)=>J[1]-$[1]).forEach($=>{var J;let X=(J=N.current)==null?void 0:J.querySelector(`${Tt}[${dt}="${encodeURIComponent($[0])}"]`);X==null||X.parentElement.appendChild(X)})}function R(){let v=K().find(V=>V.getAttribute("aria-disabled")!=="true"),P=v==null?void 0:v.getAttribute(dt);m.setState("value",P||void 0)}function O(){var v,P,V,$;if(!r.current.search||i.current.shouldFilter===!1){r.current.filtered.count=n.current.size;return}r.current.filtered.groups=new Set;let J=0;for(let X of n.current){let Y=(P=(v=o.current.get(X))==null?void 0:v.value)!=null?P:"",ie=($=(V=o.current.get(X))==null?void 0:V.keywords)!=null?$:[],ne=x(Y,ie);r.current.filtered.items.set(X,ne),ne>0&&J++}for(let[X,Y]of a.current)for(let ie of Y)if(r.current.filtered.items.get(ie)>0){r.current.filtered.groups.add(X);break}r.current.filtered.count=J}function w(){var v,P,V;let $=H();$&&(((v=$.parentElement)==null?void 0:v.firstChild)===$&&((V=(P=$.closest(Tt))==null?void 0:P.querySelector(Lf))==null||V.scrollIntoView({block:"nearest"})),$.scrollIntoView({block:"nearest"}))}function H(){var v;return(v=N.current)==null?void 0:v.querySelector(`${In}[aria-selected="true"]`)}function K(){var v;return Array.from(((v=N.current)==null?void 0:v.querySelectorAll($o))||[])}function D(v){let P=K()[v];P&&m.setState("value",P.getAttribute(dt))}function C(v){var P;let V=H(),$=K(),J=$.findIndex(Y=>Y===V),X=$[J+v];(P=i.current)!=null&&P.loop&&(X=J+v<0?$[$.length-1]:J+v===$.length?$[0]:$[J+v]),X&&m.setState("value",X.getAttribute(dt))}function S(v){let P=H(),V=P==null?void 0:P.closest(Tt),$;for(;V&&!$;)V=v>0?Bf(V,Tt):Vf(V,Tt),$=V==null?void 0:V.querySelector($o);$?m.setState("value",$.getAttribute(dt)):C(v)}let B=()=>D(K().length-1),ae=v=>{v.preventDefault(),v.metaKey?B():v.altKey?S(1):C(1)},M=v=>{v.preventDefault(),v.metaKey?D(0):v.altKey?S(-1):C(-1)};return p.createElement(Ee.div,{ref:t,tabIndex:-1,...I,"cmdk-root":"",onKeyDown:v=>{var P;if((P=I.onKeyDown)==null||P.call(I,v),!v.defaultPrevented)switch(v.key){case"n":case"j":{k&&v.ctrlKey&&ae(v);break}case"ArrowDown":{ae(v);break}case"p":case"k":{k&&v.ctrlKey&&M(v);break}case"ArrowUp":{M(v);break}case"Home":{v.preventDefault(),D(0);break}case"End":{v.preventDefault(),B();break}case"Enter":if(!v.nativeEvent.isComposing&&v.keyCode!==229){v.preventDefault();let V=H();if(V){let $=new Event(hn);V.dispatchEvent($)}}}}},p.createElement("label",{"cmdk-label":"",htmlFor:_.inputId,id:_.labelId,style:Wf},s),lr(e,v=>p.createElement(_s.Provider,{value:m},p.createElement(Ss.Provider,{value:_},v))))}),zf=p.forwardRef((e,t)=>{var r,n;let a=ft(),o=p.useRef(null),l=p.useContext(Es),i=jt(),s=ks(e),c=(n=(r=s.current)==null?void 0:r.forceMount)!=null?n:l==null?void 0:l.forceMount;gt(()=>{if(!c)return i.item(a,l==null?void 0:l.id)},[c]);let u=Ts(a,o,[e.value,e.children,o],e.keywords),d=Ln(),h=pt(z=>z.value&&z.value===u.current),f=pt(z=>c||i.filter()===!1?!0:z.search?z.filtered.items.get(a)>0:!0);p.useEffect(()=>{let z=o.current;if(!(!z||e.disabled))return z.addEventListener(hn,b),()=>z.removeEventListener(hn,b)},[f,e.onSelect,e.disabled]);function b(){var z,m;y(),(m=(z=s.current).onSelect)==null||m.call(z,u.current)}function y(){d.setState("value",u.current,!0)}if(!f)return null;let{disabled:k,value:I,onSelect:E,forceMount:j,keywords:A,...N}=e;return p.createElement(Ee.div,{ref:Rt([o,t]),...N,id:a,"cmdk-item":"",role:"option","aria-disabled":!!k,"aria-selected":!!h,"data-disabled":!!k,"data-selected":!!h,onPointerMove:k||i.getDisablePointerSelection()?void 0:y,onClick:k?void 0:b},e.children)}),Df=p.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:a,...o}=e,l=ft(),i=p.useRef(null),s=p.useRef(null),c=ft(),u=jt(),d=pt(f=>a||u.filter()===!1?!0:f.search?f.filtered.groups.has(l):!0);gt(()=>u.group(l),[]),Ts(l,i,[e.value,e.heading,s]);let h=p.useMemo(()=>({id:l,forceMount:a}),[a]);return p.createElement(Ee.div,{ref:Rt([i,t]),...o,"cmdk-group":"",role:"presentation",hidden:d?void 0:!0},r&&p.createElement("div",{ref:s,"cmdk-group-heading":"","aria-hidden":!0,id:c},r),lr(e,f=>p.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?c:void 0},p.createElement(Es.Provider,{value:h},f))))}),Of=p.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,a=p.useRef(null),o=pt(l=>!l.search);return!r&&!o?null:p.createElement(Ee.div,{ref:Rt([a,t]),...n,"cmdk-separator":"",role:"separator"})}),Gf=p.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,a=e.value!=null,o=Ln(),l=pt(u=>u.search),i=pt(u=>u.value),s=jt(),c=p.useMemo(()=>{var u;let d=(u=s.listInnerRef.current)==null?void 0:u.querySelector(`${In}[${dt}="${encodeURIComponent(i)}"]`);return d==null?void 0:d.getAttribute("id")},[]);return p.useEffect(()=>{e.value!=null&&o.setState("search",e.value)},[e.value]),p.createElement(Ee.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":s.listId,"aria-labelledby":s.labelId,"aria-activedescendant":c,id:s.inputId,type:"text",value:a?e.value:l,onChange:u=>{a||o.setState("search",u.target.value),r==null||r(u.target.value)}})}),Ff=p.forwardRef((e,t)=>{let{children:r,label:n="Suggestions",...a}=e,o=p.useRef(null),l=p.useRef(null),i=jt();return p.useEffect(()=>{if(l.current&&o.current){let s=l.current,c=o.current,u,d=new ResizeObserver(()=>{u=requestAnimationFrame(()=>{let h=s.offsetHeight;c.style.setProperty("--cmdk-list-height",h.toFixed(1)+"px")})});return d.observe(s),()=>{cancelAnimationFrame(u),d.unobserve(s)}}},[]),p.createElement(Ee.div,{ref:Rt([o,t]),...a,"cmdk-list":"",role:"listbox","aria-label":n,id:i.listId},lr(e,s=>p.createElement("div",{ref:Rt([l,i.listInnerRef]),"cmdk-list-sizer":""},s)))}),Mf=p.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:a,contentClassName:o,container:l,...i}=e;return p.createElement(wa,{open:r,onOpenChange:n},p.createElement(va,{container:l},p.createElement(xn,{"cmdk-overlay":"",className:a}),p.createElement(Sn,{"aria-label":e.label,"cmdk-dialog":"",className:o},p.createElement(Cs,{ref:t,...i}))))}),$f=p.forwardRef((e,t)=>pt(r=>r.filtered.count===0)?p.createElement(Ee.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Hf=p.forwardRef((e,t)=>{let{progress:r,children:n,label:a="Loading...",...o}=e;return p.createElement(Ee.div,{ref:t,...o,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":a},lr(e,l=>p.createElement("div",{"aria-hidden":!0},l)))}),je=Object.assign(Cs,{List:Ff,Item:zf,Input:Gf,Group:Df,Separator:Of,Dialog:Mf,Empty:$f,Loading:Hf});function Bf(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}function Vf(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}function ks(e){let t=p.useRef(e);return gt(()=>{t.current=e}),t}var gt=typeof window>"u"?p.useEffect:p.useLayoutEffect;function yt(e){let t=p.useRef();return t.current===void 0&&(t.current=e()),t}function Rt(e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}function pt(e){let t=Ln(),r=()=>e(t.snapshot());return If.useSyncExternalStore(t.subscribe,r,r)}function Ts(e,t,r,n=[]){let a=p.useRef(),o=jt();return gt(()=>{var l;let i=(()=>{var c;for(let u of r){if(typeof u=="string")return u.trim();if(typeof u=="object"&&"current"in u)return u.current?(c=u.current.textContent)==null?void 0:c.trim():a.current}})(),s=n.map(c=>c.trim());o.value(e,i,s),(l=t.current)==null||l.setAttribute(dt,i),a.current=i}),a}var Uf=()=>{let[e,t]=p.useState(),r=yt(()=>new Map);return gt(()=>{r.current.forEach(n=>n()),r.current=new Map},[e]),(n,a)=>{r.current.set(n,a),t({})}};function qf(e){let t=e.type;return typeof t=="function"?t(e.props):"render"in t?t.render(e.props):e}function lr({asChild:e,children:t},r){return e&&p.isValidElement(t)?p.cloneElement(qf(t),{ref:t.ref},r(t.props.children)):r(t)}var Wf={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};const cr=p.forwardRef(({className:e,...t},r)=>g.jsx(je,{ref:r,className:fe("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",e),...t}));cr.displayName=je.displayName;const Pn=p.forwardRef(({className:e,...t},r)=>g.jsxs("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[g.jsx(bu,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),g.jsx(je.Input,{ref:r,className:fe("placeholder:text-muted-foreground flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none disabled:cursor-not-allowed disabled:opacity-50",e),...t})]}));Pn.displayName=je.Input.displayName;const ur=p.forwardRef(({className:e,...t},r)=>g.jsx(je.List,{ref:r,className:fe("max-h-[300px] overflow-x-hidden overflow-y-auto",e),...t}));ur.displayName=je.List.displayName;const zn=p.forwardRef((e,t)=>g.jsx(je.Empty,{ref:t,className:"py-6 text-center text-sm",...e}));zn.displayName=je.Empty.displayName;const Et=p.forwardRef(({className:e,...t},r)=>g.jsx(je.Group,{ref:r,className:fe("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...t}));Et.displayName=je.Group.displayName;const Xf=p.forwardRef(({className:e,...t},r)=>g.jsx(je.Separator,{ref:r,className:fe("bg-border -mx-1 h-px",e),...t}));Xf.displayName=je.Separator.displayName;const Ct=p.forwardRef(({className:e,...t},r)=>g.jsx(je.Item,{ref:r,className:fe("data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",e),...t}));Ct.displayName=je.Item.displayName;const Yf=({layout:e,autoRunFor:t,mainLayout:r})=>{const n=Be(),[a,o]=p.useState(!1),l=p.useRef(null),{t:i}=Se(),s=p.useCallback(()=>{if(n)try{const u=n.getGraph();if(!u||u.order===0)return;const d=r.positions();ha(u,d,{duration:300})}catch(u){console.error("Error updating positions:",u),l.current&&(window.clearInterval(l.current),l.current=null,o(!1))}},[n,r]),c=p.useCallback(()=>{if(a){console.log("Stopping layout animation"),l.current&&(window.clearInterval(l.current),l.current=null);try{typeof e.kill=="function"?(e.kill(),console.log("Layout algorithm killed")):typeof e.stop=="function"&&(e.stop(),console.log("Layout algorithm stopped"))}catch(u){console.error("Error stopping layout algorithm:",u)}o(!1)}else console.log("Starting layout animation"),s(),l.current=window.setInterval(()=>{s()},200),o(!0),setTimeout(()=>{if(l.current){console.log("Auto-stopping layout animation after 3 seconds"),window.clearInterval(l.current),l.current=null,o(!1);try{typeof e.kill=="function"?e.kill():typeof e.stop=="function"&&e.stop()}catch(u){console.error("Error stopping layout algorithm:",u)}}},3e3)},[a,e,s]);return p.useEffect(()=>{if(!n){console.log("No sigma instance available");return}let u=null;return t!==void 0&&t>-1&&n.getGraph().order>0&&(console.log("Auto-starting layout animation"),s(),l.current=window.setInterval(()=>{s()},200),o(!0),t>0&&(u=window.setTimeout(()=>{console.log("Auto-stopping layout animation after timeout"),l.current&&(window.clearInterval(l.current),l.current=null),o(!1)},t))),()=>{l.current&&(window.clearInterval(l.current),l.current=null),u&&window.clearTimeout(u),o(!1)}},[t,n,s]),g.jsx(be,{size:"icon",onClick:c,tooltip:i(a?"graphPanel.sideBar.layoutsControl.stopAnimation":"graphPanel.sideBar.layoutsControl.startAnimation"),variant:Ie,children:a?g.jsx(au,{}):g.jsx(cu,{})})},Kf=()=>{const e=Be(),{t}=Se(),[r,n]=p.useState("Circular"),[a,o]=p.useState(!1),l=Z.use.graphLayoutMaxIterations(),i=Hd(),s=Gd(),c=wf(),u=pf({maxIterations:l,settings:{margin:5,expansion:1.1,gridSize:1,ratio:1,speed:3}}),d=Yd({maxIterations:l,settings:{attraction:3e-4,repulsion:.02,gravity:.02,inertia:.4,maxMove:100}}),h=ys({iterations:l}),f=mf(),b=Kd(),y=af(),k=p.useMemo(()=>({Circular:{layout:i},Circlepack:{layout:s},Random:{layout:c},Noverlaps:{layout:u,worker:f},"Force Directed":{layout:d,worker:b},"Force Atlas":{layout:h,worker:y}}),[s,i,d,h,u,c,b,f,y]),I=p.useCallback(E=>{console.debug("Running layout:",E);const{positions:j}=k[E].layout;try{const A=e.getGraph();if(!A){console.error("No graph available");return}const N=j();console.log("Positions calculated, animating nodes"),ha(A,N,{duration:400}),n(E)}catch(A){console.error("Error running layout:",A)}},[k,e]);return g.jsxs("div",{children:[g.jsx("div",{children:k[r]&&"worker"in k[r]&&g.jsx(Yf,{layout:k[r].worker,mainLayout:k[r].layout})}),g.jsx("div",{children:g.jsxs(Tn,{open:a,onOpenChange:o,children:[g.jsx(Rn,{asChild:!0,children:g.jsx(be,{size:"icon",variant:Ie,onClick:()=>o(E=>!E),tooltip:t("graphPanel.sideBar.layoutsControl.layoutGraph"),children:g.jsx(Yc,{})})}),g.jsx(ir,{side:"right",align:"start",sideOffset:8,collisionPadding:5,sticky:"always",className:"p-1 min-w-auto",children:g.jsx(cr,{children:g.jsx(ur,{children:g.jsx(Et,{children:Object.keys(k).map(E=>g.jsx(Ct,{onSelect:()=>{I(E)},className:"cursor-pointer text-xs",children:t(`graphPanel.sideBar.layoutsControl.layouts.${E}`)},E))})})})})]})})]})},Qf=()=>{const e=p.useContext(Ra);if(e===void 0)throw new Error("useTheme must be used within a ThemeProvider");return e},zt=e=>!!(e.type.startsWith("mouse")&&e.buttons!==0),Jf=({disableHoverEffect:e})=>{const t=Be(),r=ga(),n=bi(),a=Z.use.graphLayoutMaxIterations(),{assign:o}=ys({iterations:a}),{theme:l}=Qf(),i=Z.use.enableHideUnselectedEdges(),s=Z.use.enableEdgeEvents(),c=Z.use.showEdgeLabel(),u=Z.use.showNodeLabel(),d=Z.use.minEdgeSize(),h=Z.use.maxEdgeSize(),f=te.use.selectedNode(),b=te.use.focusedNode(),y=te.use.selectedEdge(),k=te.use.focusedEdge(),I=te.use.sigmaGraph();return p.useEffect(()=>{if(I&&t){try{typeof t.setGraph=="function"?(t.setGraph(I),console.log("Binding graph to sigma instance")):(t.graph=I,console.warn("Simgma missing setGraph function, set graph property directly"))}catch(E){console.error("Error setting graph on sigma instance:",E)}o(),console.log("Initial layout applied to graph")}},[t,I,o,a]),p.useEffect(()=>{t&&(te.getState().sigmaInstance||(console.log("Setting sigma instance from GraphControl"),te.getState().setSigmaInstance(t)))},[t]),p.useEffect(()=>{const{setFocusedNode:E,setSelectedNode:j,setFocusedEdge:A,setSelectedEdge:N,clearSelection:z}=te.getState(),m={enterNode:_=>{zt(_.event.original)||t.getGraph().hasNode(_.node)&&E(_.node)},leaveNode:_=>{zt(_.event.original)||E(null)},clickNode:_=>{t.getGraph().hasNode(_.node)&&(j(_.node),N(null))},clickStage:()=>z()};s&&(m.clickEdge=_=>{N(_.edge),j(null)},m.enterEdge=_=>{zt(_.event.original)||A(_.edge)},m.leaveEdge=_=>{zt(_.event.original)||A(null)}),r(m)},[r,s]),p.useEffect(()=>{if(t&&I){const E=t.getGraph();let j=Number.MAX_SAFE_INTEGER,A=0;E.forEachEdge(z=>{const m=E.getEdgeAttribute(z,"originalWeight")||1;typeof m=="number"&&(j=Math.min(j,m),A=Math.max(A,m))});const N=A-j;if(N>0){const z=h-d;E.forEachEdge(m=>{const _=E.getEdgeAttribute(m,"originalWeight")||1;if(typeof _=="number"){const x=d+z*Math.pow((_-j)/N,.5);E.setEdgeAttribute(m,"size",x)}})}else E.forEachEdge(z=>{E.setEdgeAttribute(z,"size",d)});t.refresh()}},[t,I,d,h]),p.useEffect(()=>{const E=l==="dark",j=E?Vi:void 0,A=E?Xi:void 0;n({enableEdgeEvents:s,renderEdgeLabels:c,renderLabels:u,nodeReducer:(N,z)=>{const m=t.getGraph(),_={...z,highlighted:z.highlighted||!1,labelColor:j};if(!e){_.highlighted=!1;const x=b||f,T=k||y;if(x&&m.hasNode(x))try{(N===x||m.neighbors(x).includes(N))&&(_.highlighted=!0,N===f&&(_.borderColor=Wi))}catch(R){console.error("Error in nodeReducer:",R)}else if(T&&m.hasEdge(T))m.extremities(T).includes(N)&&(_.highlighted=!0,_.size=3);else return _;_.highlighted?E&&(_.labelColor=Ui):_.color=qi}return _},edgeReducer:(N,z)=>{const m=t.getGraph(),_={...z,hidden:!1,labelColor:j,color:A};if(!e){const x=b||f;if(x&&m.hasNode(x))try{i?m.extremities(N).includes(x)||(_.hidden=!0):m.extremities(N).includes(x)&&(_.color=Wn)}catch(T){console.error("Error in edgeReducer:",T)}else{const T=y&&m.hasEdge(y)?y:null,R=k&&m.hasEdge(k)?k:null;(T||R)&&(N===T?_.color=Yi:N===R?_.color=Wn:i&&(_.hidden=!0))}}return _}})},[f,b,y,k,n,t,e,l,i,s,c,u]),null},Zf=()=>{const{zoomIn:e,zoomOut:t,reset:r}=da({duration:200,factor:1.5}),n=Be(),{t:a}=Se(),o=p.useCallback(()=>e(),[e]),l=p.useCallback(()=>t(),[t]),i=p.useCallback(()=>{if(n)try{n.setCustomBBox(null),n.refresh();const u=n.getGraph();if(!(u!=null&&u.order)||u.nodes().length===0){r();return}n.getCamera().animate({x:.5,y:.5,ratio:1.1},{duration:1e3})}catch(u){console.error("Error resetting zoom:",u),r()}},[n,r]),s=p.useCallback(()=>{if(!n)return;const u=n.getCamera(),h=u.angle+Math.PI/8;u.animate({angle:h},{duration:200})},[n]),c=p.useCallback(()=>{if(!n)return;const u=n.getCamera(),h=u.angle-Math.PI/8;u.animate({angle:h},{duration:200})},[n]);return g.jsxs(g.Fragment,{children:[g.jsx(be,{variant:Ie,onClick:s,tooltip:a("graphPanel.sideBar.zoomControl.rotateCamera"),size:"icon",children:g.jsx(pu,{})}),g.jsx(be,{variant:Ie,onClick:c,tooltip:a("graphPanel.sideBar.zoomControl.rotateCameraCounterClockwise"),size:"icon",children:g.jsx(hu,{})}),g.jsx(be,{variant:Ie,onClick:i,tooltip:a("graphPanel.sideBar.zoomControl.resetZoom"),size:"icon",children:g.jsx(Vc,{})}),g.jsx(be,{variant:Ie,onClick:o,tooltip:a("graphPanel.sideBar.zoomControl.zoomIn"),size:"icon",children:g.jsx(Nu,{})}),g.jsx(be,{variant:Ie,onClick:l,tooltip:a("graphPanel.sideBar.zoomControl.zoomOut"),size:"icon",children:g.jsx(Lu,{})})]})},eh=()=>{const{isFullScreen:e,toggle:t}=wi(),{t:r}=Se();return g.jsx(g.Fragment,{children:e?g.jsx(be,{variant:Ie,onClick:t,tooltip:r("graphPanel.sideBar.fullScreenControl.windowed"),size:"icon",children:g.jsx(ru,{})}):g.jsx(be,{variant:Ie,onClick:t,tooltip:r("graphPanel.sideBar.fullScreenControl.fullScreen"),size:"icon",children:g.jsx(eu,{})})})};var Dn="Checkbox",[th,zp]=wn(Dn),[rh,nh]=th(Dn),Rs=p.forwardRef((e,t)=>{const{__scopeCheckbox:r,name:n,checked:a,defaultChecked:o,required:l,disabled:i,value:s="on",onCheckedChange:c,form:u,...d}=e,[h,f]=p.useState(null),b=Xe(t,A=>f(A)),y=p.useRef(!1),k=h?u||!!h.closest("form"):!0,[I=!1,E]=ma({prop:a,defaultProp:o,onChange:c}),j=p.useRef(I);return p.useEffect(()=>{const A=h==null?void 0:h.form;if(A){const N=()=>E(j.current);return A.addEventListener("reset",N),()=>A.removeEventListener("reset",N)}},[h,E]),g.jsxs(rh,{scope:r,state:I,disabled:i,children:[g.jsx(Ee.button,{type:"button",role:"checkbox","aria-checked":ot(I)?"mixed":I,"aria-required":l,"data-state":Ns(I),"data-disabled":i?"":void 0,disabled:i,value:s,...d,ref:b,onKeyDown:Ce(e.onKeyDown,A=>{A.key==="Enter"&&A.preventDefault()}),onClick:Ce(e.onClick,A=>{E(N=>ot(N)?!0:!N),k&&(y.current=A.isPropagationStopped(),y.current||A.stopPropagation())})}),k&&g.jsx(oh,{control:h,bubbles:!y.current,name:n,value:s,checked:I,required:l,disabled:i,form:u,style:{transform:"translateX(-100%)"},defaultChecked:ot(o)?!1:o})]})});Rs.displayName=Dn;var As="CheckboxIndicator",js=p.forwardRef((e,t)=>{const{__scopeCheckbox:r,forceMount:n,...a}=e,o=nh(As,r);return g.jsx(St,{present:n||ot(o.state)||o.state===!0,children:g.jsx(Ee.span,{"data-state":Ns(o.state),"data-disabled":o.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});js.displayName=As;var oh=e=>{const{control:t,checked:r,bubbles:n=!0,defaultChecked:a,...o}=e,l=p.useRef(null),i=Oi(r),s=Gi(t);p.useEffect(()=>{const u=l.current,d=window.HTMLInputElement.prototype,f=Object.getOwnPropertyDescriptor(d,"checked").set;if(i!==r&&f){const b=new Event("click",{bubbles:n});u.indeterminate=ot(r),f.call(u,ot(r)?!1:r),u.dispatchEvent(b)}},[i,r,n]);const c=p.useRef(ot(r)?!1:r);return g.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a??c.current,...o,tabIndex:-1,ref:l,style:{...e.style,...s,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function ot(e){return e==="indeterminate"}function Ns(e){return ot(e)?"indeterminate":e?"checked":"unchecked"}var Is=Rs,ah=js;const Ls=p.forwardRef(({className:e,...t},r)=>g.jsx(Is,{ref:r,className:fe("peer border-primary ring-offset-background focus-visible:ring-ring data-[state=checked]:bg-muted data-[state=checked]:text-muted-foreground h-4 w-4 shrink-0 rounded-sm border focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:g.jsx(ah,{className:fe("flex items-center justify-center text-current"),children:g.jsx(Va,{className:"h-4 w-4"})})}));Ls.displayName=Is.displayName;var sh="Separator",Ho="horizontal",ih=["horizontal","vertical"],Ps=p.forwardRef((e,t)=>{const{decorative:r,orientation:n=Ho,...a}=e,o=lh(n)?n:Ho,i=r?{role:"none"}:{"aria-orientation":o==="vertical"?o:void 0,role:"separator"};return g.jsx(Ee.div,{"data-orientation":o,...i,...a,ref:t})});Ps.displayName=sh;function lh(e){return ih.includes(e)}var zs=Ps;const bt=p.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...n},a)=>g.jsx(zs,{ref:a,decorative:r,orientation:t,className:fe("bg-border shrink-0",t==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",e),...n}));bt.displayName=zs.displayName;const tt=({checked:e,onCheckedChange:t,label:r})=>{const n=`checkbox-${r.toLowerCase().replace(/\s+/g,"-")}`;return g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx(Ls,{id:n,checked:e,onCheckedChange:t}),g.jsx("label",{htmlFor:n,className:"text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:r})]})},$r=({value:e,onEditFinished:t,label:r,min:n,max:a,defaultValue:o})=>{const{t:l}=Se(),[i,s]=p.useState(e),c=`input-${r.toLowerCase().replace(/\s+/g,"-")}`;p.useEffect(()=>{s(e)},[e]);const u=p.useCallback(f=>{const b=f.target.value.trim();if(b.length===0){s(null);return}const y=Number.parseInt(b);if(!isNaN(y)&&y!==i){if(n!==void 0&&y<n||a!==void 0&&y>a)return;s(y)}},[i,n,a]),d=p.useCallback(()=>{i!==null&&e!==i&&t(i)},[e,i,t]),h=p.useCallback(()=>{o!==void 0&&e!==o&&(s(o),t(o))},[o,e,t]);return g.jsxs("div",{className:"flex flex-col gap-2",children:[g.jsx("label",{htmlFor:c,className:"text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:r}),g.jsxs("div",{className:"flex items-center gap-1",children:[g.jsx(Wt,{id:c,type:"number",value:i===null?"":i,onChange:u,className:"h-6 w-full min-w-0 pr-1",min:n,max:a,onBlur:d,onKeyDown:f=>{f.key==="Enter"&&d()}}),o!==void 0&&g.jsx(be,{variant:"ghost",size:"icon",className:"h-6 w-6 flex-shrink-0 hover:bg-muted text-muted-foreground hover:text-foreground",onClick:h,type:"button",title:l("graphPanel.sideBar.settings.resetToDefault"),children:g.jsx(qa,{className:"h-3.5 w-3.5"})})]})]})};function ch(){const[e,t]=p.useState(!1),r=Z.use.showPropertyPanel(),n=Z.use.showNodeSearchBar(),a=Z.use.showNodeLabel(),o=Z.use.enableEdgeEvents(),l=Z.use.enableNodeDrag(),i=Z.use.enableHideUnselectedEdges(),s=Z.use.showEdgeLabel(),c=Z.use.minEdgeSize(),u=Z.use.maxEdgeSize(),d=Z.use.graphQueryMaxDepth(),h=Z.use.graphMaxNodes(),f=Z.use.backendMaxGraphNodes(),b=Z.use.graphLayoutMaxIterations(),y=Z.use.enableHealthCheck(),k=p.useCallback(()=>Z.setState(w=>({enableNodeDrag:!w.enableNodeDrag})),[]),I=p.useCallback(()=>Z.setState(w=>({enableEdgeEvents:!w.enableEdgeEvents})),[]),E=p.useCallback(()=>Z.setState(w=>({enableHideUnselectedEdges:!w.enableHideUnselectedEdges})),[]),j=p.useCallback(()=>Z.setState(w=>({showEdgeLabel:!w.showEdgeLabel})),[]),A=p.useCallback(()=>Z.setState(w=>({showPropertyPanel:!w.showPropertyPanel})),[]),N=p.useCallback(()=>Z.setState(w=>({showNodeSearchBar:!w.showNodeSearchBar})),[]),z=p.useCallback(()=>Z.setState(w=>({showNodeLabel:!w.showNodeLabel})),[]),m=p.useCallback(()=>Z.setState(w=>({enableHealthCheck:!w.enableHealthCheck})),[]),_=p.useCallback(w=>{if(w<1)return;Z.setState({graphQueryMaxDepth:w});const H=Z.getState().queryLabel;Z.getState().setQueryLabel(""),setTimeout(()=>{Z.getState().setQueryLabel(H)},300)},[]),x=p.useCallback(w=>{const H=f||1e3;w<1||w>H||Z.getState().setGraphMaxNodes(w,!0)},[f]),T=p.useCallback(w=>{w<1||Z.setState({graphLayoutMaxIterations:w})},[]),{t:R}=Se(),O=()=>t(!1);return g.jsx(g.Fragment,{children:g.jsxs(Tn,{open:e,onOpenChange:t,children:[g.jsx(Rn,{asChild:!0,children:g.jsx(be,{variant:Ie,tooltip:R("graphPanel.sideBar.settings.settings"),size:"icon",children:g.jsx(Su,{})})}),g.jsx(ir,{side:"right",align:"end",sideOffset:8,collisionPadding:5,className:"p-2 max-w-[200px]",onCloseAutoFocus:w=>w.preventDefault(),children:g.jsxs("div",{className:"flex flex-col gap-2",children:[g.jsx(tt,{checked:y,onCheckedChange:m,label:R("graphPanel.sideBar.settings.healthCheck")}),g.jsx(bt,{}),g.jsx(tt,{checked:r,onCheckedChange:A,label:R("graphPanel.sideBar.settings.showPropertyPanel")}),g.jsx(tt,{checked:n,onCheckedChange:N,label:R("graphPanel.sideBar.settings.showSearchBar")}),g.jsx(bt,{}),g.jsx(tt,{checked:a,onCheckedChange:z,label:R("graphPanel.sideBar.settings.showNodeLabel")}),g.jsx(tt,{checked:l,onCheckedChange:k,label:R("graphPanel.sideBar.settings.nodeDraggable")}),g.jsx(bt,{}),g.jsx(tt,{checked:s,onCheckedChange:j,label:R("graphPanel.sideBar.settings.showEdgeLabel")}),g.jsx(tt,{checked:i,onCheckedChange:E,label:R("graphPanel.sideBar.settings.hideUnselectedEdges")}),g.jsx(tt,{checked:o,onCheckedChange:I,label:R("graphPanel.sideBar.settings.edgeEvents")}),g.jsxs("div",{className:"flex flex-col gap-2",children:[g.jsx("label",{htmlFor:"edge-size-min",className:"text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:R("graphPanel.sideBar.settings.edgeSizeRange")}),g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx(Wt,{id:"edge-size-min",type:"number",value:c,onChange:w=>{const H=Number(w.target.value);!isNaN(H)&&H>=1&&H<=u&&Z.setState({minEdgeSize:H})},className:"h-6 w-16 min-w-0 pr-1",min:1,max:Math.min(u,10)}),g.jsx("span",{children:"-"}),g.jsxs("div",{className:"flex items-center gap-1",children:[g.jsx(Wt,{id:"edge-size-max",type:"number",value:u,onChange:w=>{const H=Number(w.target.value);!isNaN(H)&&H>=c&&H>=1&&H<=10&&Z.setState({maxEdgeSize:H})},className:"h-6 w-16 min-w-0 pr-1",min:c,max:10}),g.jsx(be,{variant:"ghost",size:"icon",className:"h-6 w-6 flex-shrink-0 hover:bg-muted text-muted-foreground hover:text-foreground",onClick:()=>Z.setState({minEdgeSize:1,maxEdgeSize:5}),type:"button",title:R("graphPanel.sideBar.settings.resetToDefault"),children:g.jsx(qa,{className:"h-3.5 w-3.5"})})]})]})]}),g.jsx(bt,{}),g.jsx($r,{label:R("graphPanel.sideBar.settings.maxQueryDepth"),min:1,value:d,defaultValue:3,onEditFinished:_}),g.jsx($r,{label:`${R("graphPanel.sideBar.settings.maxNodes")} (≤ ${f||1e3})`,min:1,max:f||1e3,value:h,defaultValue:f||1e3,onEditFinished:x}),g.jsx($r,{label:R("graphPanel.sideBar.settings.maxLayoutIterations"),min:1,max:30,value:b,defaultValue:15,onEditFinished:T}),g.jsx(bt,{}),g.jsx(be,{onClick:O,variant:"outline",size:"sm",className:"ml-auto px-4",children:R("graphPanel.sideBar.settings.save")})]})})]})})}const uh="ENTRIES",Ds="KEYS",Os="VALUES",_e="";class Hr{constructor(t,r){const n=t._tree,a=Array.from(n.keys());this.set=t,this._type=r,this._path=a.length>0?[{node:n,keys:a}]:[]}next(){const t=this.dive();return this.backtrack(),t}dive(){if(this._path.length===0)return{done:!0,value:void 0};const{node:t,keys:r}=mt(this._path);if(mt(r)===_e)return{done:!1,value:this.result()};const n=t.get(mt(r));return this._path.push({node:n,keys:Array.from(n.keys())}),this.dive()}backtrack(){if(this._path.length===0)return;const t=mt(this._path).keys;t.pop(),!(t.length>0)&&(this._path.pop(),this.backtrack())}key(){return this.set._prefix+this._path.map(({keys:t})=>mt(t)).filter(t=>t!==_e).join("")}value(){return mt(this._path).node.get(_e)}result(){switch(this._type){case Os:return this.value();case Ds:return this.key();default:return[this.key(),this.value()]}}[Symbol.iterator](){return this}}const mt=e=>e[e.length-1],dh=(e,t,r)=>{const n=new Map;if(t===void 0)return n;const a=t.length+1,o=a+r,l=new Uint8Array(o*a).fill(r+1);for(let i=0;i<a;++i)l[i]=i;for(let i=1;i<o;++i)l[i*a]=i;return Gs(e,t,r,n,l,1,a,""),n},Gs=(e,t,r,n,a,o,l,i)=>{const s=o*l;e:for(const c of e.keys())if(c===_e){const u=a[s-1];u<=r&&n.set(i,[e.get(c),u])}else{let u=o;for(let d=0;d<c.length;++d,++u){const h=c[d],f=l*u,b=f-l;let y=a[f];const k=Math.max(0,u-r-1),I=Math.min(l-1,u+r);for(let E=k;E<I;++E){const j=h!==t[E],A=a[b+E]+ +j,N=a[b+E+1]+1,z=a[f+E]+1,m=a[f+E+1]=Math.min(A,N,z);m<y&&(y=m)}if(y>r)continue e}Gs(e.get(c),t,r,n,a,u,l,i+c)}};class nt{constructor(t=new Map,r=""){this._size=void 0,this._tree=t,this._prefix=r}atPrefix(t){if(!t.startsWith(this._prefix))throw new Error("Mismatched prefix");const[r,n]=Qt(this._tree,t.slice(this._prefix.length));if(r===void 0){const[a,o]=On(n);for(const l of a.keys())if(l!==_e&&l.startsWith(o)){const i=new Map;return i.set(l.slice(o.length),a.get(l)),new nt(i,t)}}return new nt(r,t)}clear(){this._size=void 0,this._tree.clear()}delete(t){return this._size=void 0,fh(this._tree,t)}entries(){return new Hr(this,uh)}forEach(t){for(const[r,n]of this)t(r,n,this)}fuzzyGet(t,r){return dh(this._tree,t,r)}get(t){const r=gn(this._tree,t);return r!==void 0?r.get(_e):void 0}has(t){const r=gn(this._tree,t);return r!==void 0&&r.has(_e)}keys(){return new Hr(this,Ds)}set(t,r){if(typeof t!="string")throw new Error("key must be a string");return this._size=void 0,Br(this._tree,t).set(_e,r),this}get size(){if(this._size)return this._size;this._size=0;const t=this.entries();for(;!t.next().done;)this._size+=1;return this._size}update(t,r){if(typeof t!="string")throw new Error("key must be a string");this._size=void 0;const n=Br(this._tree,t);return n.set(_e,r(n.get(_e))),this}fetch(t,r){if(typeof t!="string")throw new Error("key must be a string");this._size=void 0;const n=Br(this._tree,t);let a=n.get(_e);return a===void 0&&n.set(_e,a=r()),a}values(){return new Hr(this,Os)}[Symbol.iterator](){return this.entries()}static from(t){const r=new nt;for(const[n,a]of t)r.set(n,a);return r}static fromObject(t){return nt.from(Object.entries(t))}}const Qt=(e,t,r=[])=>{if(t.length===0||e==null)return[e,r];for(const n of e.keys())if(n!==_e&&t.startsWith(n))return r.push([e,n]),Qt(e.get(n),t.slice(n.length),r);return r.push([e,t]),Qt(void 0,"",r)},gn=(e,t)=>{if(t.length===0||e==null)return e;for(const r of e.keys())if(r!==_e&&t.startsWith(r))return gn(e.get(r),t.slice(r.length))},Br=(e,t)=>{const r=t.length;e:for(let n=0;e&&n<r;){for(const o of e.keys())if(o!==_e&&t[n]===o[0]){const l=Math.min(r-n,o.length);let i=1;for(;i<l&&t[n+i]===o[i];)++i;const s=e.get(o);if(i===o.length)e=s;else{const c=new Map;c.set(o.slice(i),s),e.set(t.slice(n,n+i),c),e.delete(o),e=c}n+=i;continue e}const a=new Map;return e.set(t.slice(n),a),a}return e},fh=(e,t)=>{const[r,n]=Qt(e,t);if(r!==void 0){if(r.delete(_e),r.size===0)Fs(n);else if(r.size===1){const[a,o]=r.entries().next().value;Ms(n,a,o)}}},Fs=e=>{if(e.length===0)return;const[t,r]=On(e);if(t.delete(r),t.size===0)Fs(e.slice(0,-1));else if(t.size===1){const[n,a]=t.entries().next().value;n!==_e&&Ms(e.slice(0,-1),n,a)}},Ms=(e,t,r)=>{if(e.length===0)return;const[n,a]=On(e);n.set(a+t,r),n.delete(a)},On=e=>e[e.length-1],Gn="or",$s="and",hh="and_not";class at{constructor(t){if((t==null?void 0:t.fields)==null)throw new Error('MiniSearch: option "fields" must be provided');const r=t.autoVacuum==null||t.autoVacuum===!0?qr:t.autoVacuum;this._options={...Ur,...t,autoVacuum:r,searchOptions:{...Bo,...t.searchOptions||{}},autoSuggestOptions:{...yh,...t.autoSuggestOptions||{}}},this._index=new nt,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldIds={},this._fieldLength=new Map,this._avgFieldLength=[],this._nextId=0,this._storedFields=new Map,this._dirtCount=0,this._currentVacuum=null,this._enqueuedVacuum=null,this._enqueuedVacuumConditions=mn,this.addFields(this._options.fields)}add(t){const{extractField:r,tokenize:n,processTerm:a,fields:o,idField:l}=this._options,i=r(t,l);if(i==null)throw new Error(`MiniSearch: document does not have ID field "${l}"`);if(this._idToShortId.has(i))throw new Error(`MiniSearch: duplicate ID ${i}`);const s=this.addDocumentId(i);this.saveStoredFields(s,t);for(const c of o){const u=r(t,c);if(u==null)continue;const d=n(u.toString(),c),h=this._fieldIds[c],f=new Set(d).size;this.addFieldLength(s,h,this._documentCount-1,f);for(const b of d){const y=a(b,c);if(Array.isArray(y))for(const k of y)this.addTerm(h,s,k);else y&&this.addTerm(h,s,y)}}}addAll(t){for(const r of t)this.add(r)}addAllAsync(t,r={}){const{chunkSize:n=10}=r,a={chunk:[],promise:Promise.resolve()},{chunk:o,promise:l}=t.reduce(({chunk:i,promise:s},c,u)=>(i.push(c),(u+1)%n===0?{chunk:[],promise:s.then(()=>new Promise(d=>setTimeout(d,0))).then(()=>this.addAll(i))}:{chunk:i,promise:s}),a);return l.then(()=>this.addAll(o))}remove(t){const{tokenize:r,processTerm:n,extractField:a,fields:o,idField:l}=this._options,i=a(t,l);if(i==null)throw new Error(`MiniSearch: document does not have ID field "${l}"`);const s=this._idToShortId.get(i);if(s==null)throw new Error(`MiniSearch: cannot remove document with ID ${i}: it is not in the index`);for(const c of o){const u=a(t,c);if(u==null)continue;const d=r(u.toString(),c),h=this._fieldIds[c],f=new Set(d).size;this.removeFieldLength(s,h,this._documentCount,f);for(const b of d){const y=n(b,c);if(Array.isArray(y))for(const k of y)this.removeTerm(h,s,k);else y&&this.removeTerm(h,s,y)}}this._storedFields.delete(s),this._documentIds.delete(s),this._idToShortId.delete(i),this._fieldLength.delete(s),this._documentCount-=1}removeAll(t){if(t)for(const r of t)this.remove(r);else{if(arguments.length>0)throw new Error("Expected documents to be present. Omit the argument to remove all documents.");this._index=new nt,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldLength=new Map,this._avgFieldLength=[],this._storedFields=new Map,this._nextId=0}}discard(t){const r=this._idToShortId.get(t);if(r==null)throw new Error(`MiniSearch: cannot discard document with ID ${t}: it is not in the index`);this._idToShortId.delete(t),this._documentIds.delete(r),this._storedFields.delete(r),(this._fieldLength.get(r)||[]).forEach((n,a)=>{this.removeFieldLength(r,a,this._documentCount,n)}),this._fieldLength.delete(r),this._documentCount-=1,this._dirtCount+=1,this.maybeAutoVacuum()}maybeAutoVacuum(){if(this._options.autoVacuum===!1)return;const{minDirtFactor:t,minDirtCount:r,batchSize:n,batchWait:a}=this._options.autoVacuum;this.conditionalVacuum({batchSize:n,batchWait:a},{minDirtCount:r,minDirtFactor:t})}discardAll(t){const r=this._options.autoVacuum;try{this._options.autoVacuum=!1;for(const n of t)this.discard(n)}finally{this._options.autoVacuum=r}this.maybeAutoVacuum()}replace(t){const{idField:r,extractField:n}=this._options,a=n(t,r);this.discard(a),this.add(t)}vacuum(t={}){return this.conditionalVacuum(t)}conditionalVacuum(t,r){return this._currentVacuum?(this._enqueuedVacuumConditions=this._enqueuedVacuumConditions&&r,this._enqueuedVacuum!=null?this._enqueuedVacuum:(this._enqueuedVacuum=this._currentVacuum.then(()=>{const n=this._enqueuedVacuumConditions;return this._enqueuedVacuumConditions=mn,this.performVacuuming(t,n)}),this._enqueuedVacuum)):this.vacuumConditionsMet(r)===!1?Promise.resolve():(this._currentVacuum=this.performVacuuming(t),this._currentVacuum)}async performVacuuming(t,r){const n=this._dirtCount;if(this.vacuumConditionsMet(r)){const a=t.batchSize||pn.batchSize,o=t.batchWait||pn.batchWait;let l=1;for(const[i,s]of this._index){for(const[c,u]of s)for(const[d]of u)this._documentIds.has(d)||(u.size<=1?s.delete(c):u.delete(d));this._index.get(i).size===0&&this._index.delete(i),l%a===0&&await new Promise(c=>setTimeout(c,o)),l+=1}this._dirtCount-=n}await null,this._currentVacuum=this._enqueuedVacuum,this._enqueuedVacuum=null}vacuumConditionsMet(t){if(t==null)return!0;let{minDirtCount:r,minDirtFactor:n}=t;return r=r||qr.minDirtCount,n=n||qr.minDirtFactor,this.dirtCount>=r&&this.dirtFactor>=n}get isVacuuming(){return this._currentVacuum!=null}get dirtCount(){return this._dirtCount}get dirtFactor(){return this._dirtCount/(1+this._documentCount+this._dirtCount)}has(t){return this._idToShortId.has(t)}getStoredFields(t){const r=this._idToShortId.get(t);if(r!=null)return this._storedFields.get(r)}search(t,r={}){const{searchOptions:n}=this._options,a={...n,...r},o=this.executeQuery(t,r),l=[];for(const[i,{score:s,terms:c,match:u}]of o){const d=c.length||1,h={id:this._documentIds.get(i),score:s*d,terms:Object.keys(u),queryTerms:c,match:u};Object.assign(h,this._storedFields.get(i)),(a.filter==null||a.filter(h))&&l.push(h)}return t===at.wildcard&&a.boostDocument==null||l.sort(Uo),l}autoSuggest(t,r={}){r={...this._options.autoSuggestOptions,...r};const n=new Map;for(const{score:o,terms:l}of this.search(t,r)){const i=l.join(" "),s=n.get(i);s!=null?(s.score+=o,s.count+=1):n.set(i,{score:o,terms:l,count:1})}const a=[];for(const[o,{score:l,terms:i,count:s}]of n)a.push({suggestion:o,terms:i,score:l/s});return a.sort(Uo),a}get documentCount(){return this._documentCount}get termCount(){return this._index.size}static loadJSON(t,r){if(r==null)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJS(JSON.parse(t),r)}static async loadJSONAsync(t,r){if(r==null)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJSAsync(JSON.parse(t),r)}static getDefault(t){if(Ur.hasOwnProperty(t))return Vr(Ur,t);throw new Error(`MiniSearch: unknown option "${t}"`)}static loadJS(t,r){const{index:n,documentIds:a,fieldLength:o,storedFields:l,serializationVersion:i}=t,s=this.instantiateMiniSearch(t,r);s._documentIds=Dt(a),s._fieldLength=Dt(o),s._storedFields=Dt(l);for(const[c,u]of s._documentIds)s._idToShortId.set(u,c);for(const[c,u]of n){const d=new Map;for(const h of Object.keys(u)){let f=u[h];i===1&&(f=f.ds),d.set(parseInt(h,10),Dt(f))}s._index.set(c,d)}return s}static async loadJSAsync(t,r){const{index:n,documentIds:a,fieldLength:o,storedFields:l,serializationVersion:i}=t,s=this.instantiateMiniSearch(t,r);s._documentIds=await Ot(a),s._fieldLength=await Ot(o),s._storedFields=await Ot(l);for(const[u,d]of s._documentIds)s._idToShortId.set(d,u);let c=0;for(const[u,d]of n){const h=new Map;for(const f of Object.keys(d)){let b=d[f];i===1&&(b=b.ds),h.set(parseInt(f,10),await Ot(b))}++c%1e3===0&&await Hs(0),s._index.set(u,h)}return s}static instantiateMiniSearch(t,r){const{documentCount:n,nextId:a,fieldIds:o,averageFieldLength:l,dirtCount:i,serializationVersion:s}=t;if(s!==1&&s!==2)throw new Error("MiniSearch: cannot deserialize an index created with an incompatible version");const c=new at(r);return c._documentCount=n,c._nextId=a,c._idToShortId=new Map,c._fieldIds=o,c._avgFieldLength=l,c._dirtCount=i||0,c._index=new nt,c}executeQuery(t,r={}){if(t===at.wildcard)return this.executeWildcardQuery(r);if(typeof t!="string"){const h={...r,...t,queries:void 0},f=t.queries.map(b=>this.executeQuery(b,h));return this.combineResults(f,h.combineWith)}const{tokenize:n,processTerm:a,searchOptions:o}=this._options,l={tokenize:n,processTerm:a,...o,...r},{tokenize:i,processTerm:s}=l,d=i(t).flatMap(h=>s(h)).filter(h=>!!h).map(vh(l)).map(h=>this.executeQuerySpec(h,l));return this.combineResults(d,l.combineWith)}executeQuerySpec(t,r){const n={...this._options.searchOptions,...r},a=(n.fields||this._options.fields).reduce((y,k)=>({...y,[k]:Vr(n.boost,k)||1}),{}),{boostDocument:o,weights:l,maxFuzzy:i,bm25:s}=n,{fuzzy:c,prefix:u}={...Bo.weights,...l},d=this._index.get(t.term),h=this.termResults(t.term,t.term,1,t.termBoost,d,a,o,s);let f,b;if(t.prefix&&(f=this._index.atPrefix(t.term)),t.fuzzy){const y=t.fuzzy===!0?.2:t.fuzzy,k=y<1?Math.min(i,Math.round(t.term.length*y)):y;k&&(b=this._index.fuzzyGet(t.term,k))}if(f)for(const[y,k]of f){const I=y.length-t.term.length;if(!I)continue;b==null||b.delete(y);const E=u*y.length/(y.length+.3*I);this.termResults(t.term,y,E,t.termBoost,k,a,o,s,h)}if(b)for(const y of b.keys()){const[k,I]=b.get(y);if(!I)continue;const E=c*y.length/(y.length+I);this.termResults(t.term,y,E,t.termBoost,k,a,o,s,h)}return h}executeWildcardQuery(t){const r=new Map,n={...this._options.searchOptions,...t};for(const[a,o]of this._documentIds){const l=n.boostDocument?n.boostDocument(o,"",this._storedFields.get(a)):1;r.set(a,{score:l,terms:[],match:{}})}return r}combineResults(t,r=Gn){if(t.length===0)return new Map;const n=r.toLowerCase(),a=gh[n];if(!a)throw new Error(`Invalid combination operator: ${r}`);return t.reduce(a)||new Map}toJSON(){const t=[];for(const[r,n]of this._index){const a={};for(const[o,l]of n)a[o]=Object.fromEntries(l);t.push([r,a])}return{documentCount:this._documentCount,nextId:this._nextId,documentIds:Object.fromEntries(this._documentIds),fieldIds:this._fieldIds,fieldLength:Object.fromEntries(this._fieldLength),averageFieldLength:this._avgFieldLength,storedFields:Object.fromEntries(this._storedFields),dirtCount:this._dirtCount,index:t,serializationVersion:2}}termResults(t,r,n,a,o,l,i,s,c=new Map){if(o==null)return c;for(const u of Object.keys(l)){const d=l[u],h=this._fieldIds[u],f=o.get(h);if(f==null)continue;let b=f.size;const y=this._avgFieldLength[h];for(const k of f.keys()){if(!this._documentIds.has(k)){this.removeTerm(h,k,r),b-=1;continue}const I=i?i(this._documentIds.get(k),r,this._storedFields.get(k)):1;if(!I)continue;const E=f.get(k),j=this._fieldLength.get(k)[h],A=mh(E,b,this._documentCount,j,y,s),N=n*a*d*I*A,z=c.get(k);if(z){z.score+=N,bh(z.terms,t);const m=Vr(z.match,r);m?m.push(u):z.match[r]=[u]}else c.set(k,{score:N,terms:[t],match:{[r]:[u]}})}}return c}addTerm(t,r,n){const a=this._index.fetch(n,qo);let o=a.get(t);if(o==null)o=new Map,o.set(r,1),a.set(t,o);else{const l=o.get(r);o.set(r,(l||0)+1)}}removeTerm(t,r,n){if(!this._index.has(n)){this.warnDocumentChanged(r,t,n);return}const a=this._index.fetch(n,qo),o=a.get(t);o==null||o.get(r)==null?this.warnDocumentChanged(r,t,n):o.get(r)<=1?o.size<=1?a.delete(t):o.delete(r):o.set(r,o.get(r)-1),this._index.get(n).size===0&&this._index.delete(n)}warnDocumentChanged(t,r,n){for(const a of Object.keys(this._fieldIds))if(this._fieldIds[a]===r){this._options.logger("warn",`MiniSearch: document with ID ${this._documentIds.get(t)} has changed before removal: term "${n}" was not present in field "${a}". Removing a document after it has changed can corrupt the index!`,"version_conflict");return}}addDocumentId(t){const r=this._nextId;return this._idToShortId.set(t,r),this._documentIds.set(r,t),this._documentCount+=1,this._nextId+=1,r}addFields(t){for(let r=0;r<t.length;r++)this._fieldIds[t[r]]=r}addFieldLength(t,r,n,a){let o=this._fieldLength.get(t);o==null&&this._fieldLength.set(t,o=[]),o[r]=a;const i=(this._avgFieldLength[r]||0)*n+a;this._avgFieldLength[r]=i/(n+1)}removeFieldLength(t,r,n,a){if(n===1){this._avgFieldLength[r]=0;return}const o=this._avgFieldLength[r]*n-a;this._avgFieldLength[r]=o/(n-1)}saveStoredFields(t,r){const{storeFields:n,extractField:a}=this._options;if(n==null||n.length===0)return;let o=this._storedFields.get(t);o==null&&this._storedFields.set(t,o={});for(const l of n){const i=a(r,l);i!==void 0&&(o[l]=i)}}}at.wildcard=Symbol("*");const Vr=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0,gh={[Gn]:(e,t)=>{for(const r of t.keys()){const n=e.get(r);if(n==null)e.set(r,t.get(r));else{const{score:a,terms:o,match:l}=t.get(r);n.score=n.score+a,n.match=Object.assign(n.match,l),Vo(n.terms,o)}}return e},[$s]:(e,t)=>{const r=new Map;for(const n of t.keys()){const a=e.get(n);if(a==null)continue;const{score:o,terms:l,match:i}=t.get(n);Vo(a.terms,l),r.set(n,{score:a.score+o,terms:a.terms,match:Object.assign(a.match,i)})}return r},[hh]:(e,t)=>{for(const r of t.keys())e.delete(r);return e}},ph={k:1.2,b:.7,d:.5},mh=(e,t,r,n,a,o)=>{const{k:l,b:i,d:s}=o;return Math.log(1+(r-t+.5)/(t+.5))*(s+e*(l+1)/(e+l*(1-i+i*n/a)))},vh=e=>(t,r,n)=>{const a=typeof e.fuzzy=="function"?e.fuzzy(t,r,n):e.fuzzy||!1,o=typeof e.prefix=="function"?e.prefix(t,r,n):e.prefix===!0,l=typeof e.boostTerm=="function"?e.boostTerm(t,r,n):1;return{term:t,fuzzy:a,prefix:o,termBoost:l}},Ur={idField:"id",extractField:(e,t)=>e[t],tokenize:e=>e.split(wh),processTerm:e=>e.toLowerCase(),fields:void 0,searchOptions:void 0,storeFields:[],logger:(e,t)=>{typeof(console==null?void 0:console[e])=="function"&&console[e](t)},autoVacuum:!0},Bo={combineWith:Gn,prefix:!1,fuzzy:!1,maxFuzzy:6,boost:{},weights:{fuzzy:.45,prefix:.375},bm25:ph},yh={combineWith:$s,prefix:(e,t,r)=>t===r.length-1},pn={batchSize:1e3,batchWait:10},mn={minDirtFactor:.1,minDirtCount:20},qr={...pn,...mn},bh=(e,t)=>{e.includes(t)||e.push(t)},Vo=(e,t)=>{for(const r of t)e.includes(r)||e.push(r)},Uo=({score:e},{score:t})=>t-e,qo=()=>new Map,Dt=e=>{const t=new Map;for(const r of Object.keys(e))t.set(parseInt(r,10),e[r]);return t},Ot=async e=>{const t=new Map;let r=0;for(const n of Object.keys(e))t.set(parseInt(n,10),e[n]),++r%1e3===0&&await Hs(0);return t},Hs=e=>new Promise(t=>setTimeout(t,e)),wh=/[\n\r\p{Z}\p{P}]+/u,xh={index:new at({fields:[]})};p.createContext(xh);const vn=({label:e,color:t,hidden:r,labels:n={}})=>W.createElement("div",{className:"node"},W.createElement("span",{className:"render "+(r?"circle":"disc"),style:{backgroundColor:t||"#000"}}),W.createElement("span",{className:`label ${r?"text-muted":""} ${e?"":"text-italic"}`},e||n.no_label||"No label")),Sh=({id:e,labels:t})=>{const r=Be(),n=p.useMemo(()=>{const a=r.getGraph().getNodeAttributes(e),o=r.getSetting("nodeReducer");return Object.assign(Object.assign({color:r.getSetting("defaultNodeColor")},a),o?o(e,a):{})},[r,e]);return W.createElement(vn,Object.assign({},n,{labels:t}))},_h=({label:e,color:t,source:r,target:n,hidden:a,directed:o,labels:l={}})=>W.createElement("div",{className:"edge"},W.createElement(vn,Object.assign({},r,{labels:l})),W.createElement("div",{className:"body"},W.createElement("div",{className:"render"},W.createElement("span",{className:a?"dotted":"dash",style:{borderColor:t||"#000"}})," ",o&&W.createElement("span",{className:"arrow",style:{borderTopColor:t||"#000"}})),W.createElement("span",{className:`label ${a?"text-muted":""} ${e?"":"fst-italic"}`},e||l.no_label||"No label")),W.createElement(vn,Object.assign({},n,{labels:l}))),Eh=({id:e,labels:t})=>{const r=Be(),n=p.useMemo(()=>{const a=r.getGraph().getEdgeAttributes(e),o=r.getSetting("nodeReducer"),l=r.getSetting("edgeReducer"),i=r.getGraph().getNodeAttributes(r.getGraph().source(e)),s=r.getGraph().getNodeAttributes(r.getGraph().target(e));return Object.assign(Object.assign(Object.assign({color:r.getSetting("defaultEdgeColor"),directed:r.getGraph().isDirected(e)},a),l?l(e,a):{}),{source:Object.assign(Object.assign({color:r.getSetting("defaultNodeColor")},i),o?o(e,i):{}),target:Object.assign(Object.assign({color:r.getSetting("defaultNodeColor")},s),o?o(e,s):{})})},[r,e]);return W.createElement(_h,Object.assign({},n,{labels:t}))};function Bs(e,t){const[r,n]=p.useState(e);return p.useEffect(()=>{const a=setTimeout(()=>{n(e)},t);return()=>{clearTimeout(a)}},[e,t]),r}function Ch({fetcher:e,preload:t,filterFn:r,renderOption:n,getOptionValue:a,notFound:o,loadingSkeleton:l,label:i,placeholder:s="Select...",value:c,onChange:u,onFocus:d,disabled:h=!1,className:f,noResultsMessage:b}){const[y,k]=p.useState(!1),[I,E]=p.useState(!1),[j,A]=p.useState([]),[N,z]=p.useState(!1),[m,_]=p.useState(null),[x,T]=p.useState(""),R=Bs(x,t?0:150),O=p.useRef(null);p.useEffect(()=>{k(!0)},[]),p.useEffect(()=>{const C=S=>{O.current&&!O.current.contains(S.target)&&I&&E(!1)};return document.addEventListener("mousedown",C),()=>{document.removeEventListener("mousedown",C)}},[I]);const w=p.useCallback(async C=>{try{z(!0),_(null);const S=await e(C);A(S)}catch(S){_(S instanceof Error?S.message:"Failed to fetch options")}finally{z(!1)}},[e]);p.useEffect(()=>{y&&(t?R&&A(C=>C.filter(S=>r?r(S,R):!0)):w(R))},[y,R,t,r,w]),p.useEffect(()=>{!y||!c||w(c)},[y,c,w]);const H=p.useCallback(C=>{u(C),requestAnimationFrame(()=>{const S=document.activeElement;S==null||S.blur(),E(!1)})},[u]),K=p.useCallback(()=>{E(!0),w(x)},[x,w]),D=p.useCallback(C=>{C.target.closest(".cmd-item")&&C.preventDefault()},[]);return g.jsx("div",{ref:O,className:fe(h&&"cursor-not-allowed opacity-50",f),onMouseDown:D,children:g.jsxs(cr,{shouldFilter:!1,className:"bg-transparent",children:[g.jsxs("div",{children:[g.jsx(Pn,{placeholder:s,value:x,className:"max-h-8",onFocus:K,onValueChange:C=>{T(C),I||E(!0)}}),N&&g.jsx("div",{className:"absolute top-1/2 right-2 flex -translate-y-1/2 transform items-center",children:g.jsx(Ua,{className:"h-4 w-4 animate-spin"})})]}),g.jsxs(ur,{hidden:!I,children:[m&&g.jsx("div",{className:"text-destructive p-4 text-center",children:m}),N&&j.length===0&&(l||g.jsx(kh,{})),!N&&!m&&j.length===0&&(o||g.jsx(zn,{children:b??`No ${i.toLowerCase()} found.`})),g.jsx(Et,{children:j.map((C,S)=>g.jsxs(W.Fragment,{children:[g.jsx(Ct,{value:a(C),onSelect:H,onMouseMove:()=>d(a(C)),className:"truncate cmd-item",children:n(C)},a(C)+`${S}`),S!==j.length-1&&g.jsx("div",{className:"bg-foreground/10 h-[1px]"},`divider-${S}`)]},a(C)+`-fragment-${S}`))})]})]})})}function kh(){return g.jsx(Et,{children:g.jsx(Ct,{disabled:!0,children:g.jsxs("div",{className:"flex w-full items-center gap-2",children:[g.jsx("div",{className:"bg-muted h-6 w-6 animate-pulse rounded-full"}),g.jsxs("div",{className:"flex flex-1 flex-col gap-1",children:[g.jsx("div",{className:"bg-muted h-4 w-24 animate-pulse rounded"}),g.jsx("div",{className:"bg-muted h-3 w-16 animate-pulse rounded"})]})]})})})}const Wr="__message_item",Th=({id:e})=>{const t=te.use.sigmaGraph();return t!=null&&t.hasNode(e)?g.jsx(Sh,{id:e}):null};function Rh(e){return g.jsxs("div",{children:[e.type==="nodes"&&g.jsx(Th,{id:e.id}),e.type==="edges"&&g.jsx(Eh,{id:e.id}),e.type==="message"&&g.jsx("div",{children:e.message})]})}const Ah=({onChange:e,onFocus:t,value:r})=>{const{t:n}=Se(),a=te.use.sigmaGraph(),o=te.use.searchEngine();p.useEffect(()=>{a&&te.getState().resetSearchEngine()},[a]),p.useEffect(()=>{if(!a||a.nodes().length===0||o)return;const i=new at({idField:"id",fields:["label"],searchOptions:{prefix:!0,fuzzy:.2,boost:{label:2}}}),s=a.nodes().map(c=>({id:c,label:a.getNodeAttribute(c,"label")}));i.addAll(s),te.getState().setSearchEngine(i)},[a,o]);const l=p.useCallback(async i=>{if(t&&t(null),!a||!o)return[];if(a.nodes().length===0)return[];if(!i)return a.nodes().filter(u=>a.hasNode(u)).slice(0,Nt).map(u=>({id:u,type:"nodes"}));let s=o.search(i).filter(c=>a.hasNode(c.id)).map(c=>({id:c.id,type:"nodes"}));if(s.length<5){const c=new Set(s.map(d=>d.id)),u=a.nodes().filter(d=>{if(c.has(d))return!1;const h=a.getNodeAttribute(d,"label");return h&&typeof h=="string"&&!h.toLowerCase().startsWith(i.toLowerCase())&&h.toLowerCase().includes(i.toLowerCase())}).map(d=>({id:d,type:"nodes"}));s=[...s,...u]}return s.length<=Nt?s:[...s.slice(0,Nt),{type:"message",id:Wr,message:n("graphPanel.search.message",{count:s.length-Nt})}]},[a,o,t,n]);return g.jsx(Ch,{className:"bg-background/60 w-24 rounded-xl border-1 opacity-60 backdrop-blur-lg transition-all hover:w-fit hover:opacity-100",fetcher:l,renderOption:Rh,getOptionValue:i=>i.id,value:r&&r.type!=="message"?r.id:null,onChange:i=>{i!==Wr&&e(i?{id:i,type:"nodes"}:null)},onFocus:i=>{i!==Wr&&t&&t(i?{id:i,type:"nodes"}:null)},label:"item",placeholder:n("graphPanel.search.placeholder")})},jh=({...e})=>g.jsx(Ah,{...e});function Nh({fetcher:e,preload:t,filterFn:r,renderOption:n,getOptionValue:a,getDisplayValue:o,notFound:l,loadingSkeleton:i,label:s,placeholder:c="Select...",value:u,onChange:d,disabled:h=!1,className:f,triggerClassName:b,searchInputClassName:y,noResultsMessage:k,triggerTooltip:I,clearable:E=!0}){const[j,A]=p.useState(!1),[N,z]=p.useState(!1),[m,_]=p.useState([]),[x,T]=p.useState(!1),[R,O]=p.useState(null),[w,H]=p.useState(u),[K,D]=p.useState(null),[C,S]=p.useState(""),B=Bs(C,t?0:150),[ae,M]=p.useState([]),[v,P]=p.useState(null);p.useEffect(()=>{A(!0),H(u)},[u]),p.useEffect(()=>{u&&(!m.length||!K)?P(g.jsx("div",{children:u})):K&&P(null)},[u,m.length,K]),p.useEffect(()=>{if(u&&m.length>0){const $=m.find(J=>a(J)===u);$&&D($)}},[u,m,a]),p.useEffect(()=>{j||(async()=>{try{T(!0),O(null);const J=await e(u);M(J),_(J)}catch(J){O(J instanceof Error?J.message:"Failed to fetch options")}finally{T(!1)}})()},[j,e,u]),p.useEffect(()=>{const $=async()=>{try{T(!0),O(null);const J=await e(B);M(J),_(J)}catch(J){O(J instanceof Error?J.message:"Failed to fetch options")}finally{T(!1)}};j&&t?t&&_(B?ae.filter(J=>r?r(J,B):!0):ae):$()},[e,B,j,t,r]);const V=p.useCallback($=>{const J=E&&$===w?"":$;H(J),D(m.find(X=>a(X)===J)||null),d(J),z(!1)},[w,d,E,m,a]);return g.jsxs(Tn,{open:N,onOpenChange:z,children:[g.jsx(Rn,{asChild:!0,children:g.jsxs(be,{variant:"outline",role:"combobox","aria-expanded":N,className:fe("justify-between",h&&"cursor-not-allowed opacity-50",b),disabled:h,tooltip:I,side:"bottom",children:[u==="*"?g.jsx("div",{children:"*"}):K?o(K):v||c,g.jsx(Dc,{className:"opacity-50",size:10})]})}),g.jsx(ir,{className:fe("p-0",f),onCloseAutoFocus:$=>$.preventDefault(),align:"start",sideOffset:8,collisionPadding:5,children:g.jsxs(cr,{shouldFilter:!1,children:[g.jsxs("div",{className:"relative w-full border-b",children:[g.jsx(Pn,{placeholder:`Search ${s.toLowerCase()}...`,value:C,onValueChange:$=>{S($)},className:y}),x&&m.length>0&&g.jsx("div",{className:"absolute top-1/2 right-2 flex -translate-y-1/2 transform items-center",children:g.jsx(Ua,{className:"h-4 w-4 animate-spin"})})]}),g.jsxs(ur,{children:[R&&g.jsx("div",{className:"text-destructive p-4 text-center",children:R}),x&&m.length===0&&(i||g.jsx(Ih,{})),!x&&!R&&m.length===0&&(l||g.jsx(zn,{children:k??`No ${s.toLowerCase()} found.`})),g.jsx(Et,{children:m.map($=>g.jsxs(Ct,{value:a($),onSelect:V,className:"truncate",children:[n($),g.jsx(Va,{className:fe("ml-auto h-3 w-3",w===a($)?"opacity-100":"opacity-0")})]},a($)))})]})]})})]})}function Ih(){return g.jsx(Et,{children:g.jsx(Ct,{disabled:!0,children:g.jsxs("div",{className:"flex w-full items-center gap-2",children:[g.jsx("div",{className:"bg-muted h-6 w-6 animate-pulse rounded-full"}),g.jsxs("div",{className:"flex flex-1 flex-col gap-1",children:[g.jsx("div",{className:"bg-muted h-4 w-24 animate-pulse rounded"}),g.jsx("div",{className:"bg-muted h-3 w-16 animate-pulse rounded"})]})]})})})}const Lh=()=>{const{t:e}=Se(),t=Z.use.queryLabel(),r=te.use.allDatabaseLabels(),n=te.use.labelsFetchAttempted(),a=p.useCallback(()=>{const i=new at({idField:"id",fields:["value"],searchOptions:{prefix:!0,fuzzy:.2,boost:{label:2}}}),s=r.map((c,u)=>({id:u,value:c}));return i.addAll(s),{labels:r,searchEngine:i}},[r]),o=p.useCallback(async i=>{const{labels:s,searchEngine:c}=a();let u=s;if(i&&(u=c.search(i).map(d=>s[d.id]),u.length<15)){const d=new Set(u),h=s.filter(f=>d.has(f)?!1:f&&typeof f=="string"&&!f.toLowerCase().startsWith(i.toLowerCase())&&f.toLowerCase().includes(i.toLowerCase()));u=[...u,...h]}return u.length<=Xn?u:[...u.slice(0,Xn),"..."]},[a]);p.useEffect(()=>{n&&(r.length>1?t&&t!=="*"&&!r.includes(t)?(console.log(`Label "${t}" not in available labels, setting to "*"`),Z.getState().setQueryLabel("*")):console.log(`Label "${t}" is valid`):t&&r.length<=1&&t&&t!=="*"&&(console.log("Available labels list is empty, setting label to empty"),Z.getState().setQueryLabel("")),te.getState().setLabelsFetchAttempted(!1))},[r,t,n]);const l=p.useCallback(()=>{te.getState().setLabelsFetchAttempted(!1),te.getState().setGraphDataFetchAttempted(!1),te.getState().setLastSuccessfulQueryLabel("");const i=Z.getState().queryLabel;i?(Z.getState().setQueryLabel(""),setTimeout(()=>{Z.getState().setQueryLabel(i)},0)):Z.getState().setQueryLabel("*")},[]);return g.jsxs("div",{className:"flex items-center",children:[g.jsx(be,{size:"icon",variant:Ie,onClick:l,tooltip:e("graphPanel.graphLabels.refreshTooltip"),className:"mr-2",children:g.jsx(du,{className:"h-4 w-4"})}),g.jsx(Nh,{className:"min-w-[300px]",triggerClassName:"max-h-8",searchInputClassName:"max-h-8",triggerTooltip:e("graphPanel.graphLabels.selectTooltip"),fetcher:o,renderOption:i=>g.jsx("div",{children:i}),getOptionValue:i=>i,getDisplayValue:i=>g.jsx("div",{children:i}),notFound:g.jsx("div",{className:"py-6 text-center text-sm",children:"No labels found"}),label:e("graphPanel.graphLabels.label"),placeholder:e("graphPanel.graphLabels.placeholder"),value:t!==null?t:"*",onChange:i=>{const s=Z.getState().queryLabel;i==="..."&&(i="*"),i===s&&i!=="*"&&(i="*"),te.getState().setGraphDataFetchAttempted(!1),Z.getState().setQueryLabel(i)},clearable:!1})]})},Vs=({text:e,className:t,tooltipClassName:r,tooltip:n,side:a,onClick:o})=>n?g.jsx(Ma,{delayDuration:200,children:g.jsxs($a,{children:[g.jsx(Ha,{asChild:!0,children:g.jsx("label",{className:fe(t,o!==void 0?"cursor-pointer":void 0),onClick:o,children:e})}),g.jsx(kn,{side:a,className:r,children:n})]})}):g.jsx("label",{className:fe(t,o!==void 0?"cursor-pointer":void 0),onClick:o,children:e});var Gt={exports:{}},Ph=Gt.exports,Wo;function zh(){return Wo||(Wo=1,function(e){(function(t,r,n){function a(s){var c=this,u=i();c.next=function(){var d=2091639*c.s0+c.c*23283064365386963e-26;return c.s0=c.s1,c.s1=c.s2,c.s2=d-(c.c=d|0)},c.c=1,c.s0=u(" "),c.s1=u(" "),c.s2=u(" "),c.s0-=u(s),c.s0<0&&(c.s0+=1),c.s1-=u(s),c.s1<0&&(c.s1+=1),c.s2-=u(s),c.s2<0&&(c.s2+=1),u=null}function o(s,c){return c.c=s.c,c.s0=s.s0,c.s1=s.s1,c.s2=s.s2,c}function l(s,c){var u=new a(s),d=c&&c.state,h=u.next;return h.int32=function(){return u.next()*4294967296|0},h.double=function(){return h()+(h()*2097152|0)*11102230246251565e-32},h.quick=h,d&&(typeof d=="object"&&o(d,u),h.state=function(){return o(u,{})}),h}function i(){var s=4022871197,c=function(u){u=String(u);for(var d=0;d<u.length;d++){s+=u.charCodeAt(d);var h=.02519603282416938*s;s=h>>>0,h-=s,h*=s,s=h>>>0,h-=s,s+=h*4294967296}return(s>>>0)*23283064365386963e-26};return c}r&&r.exports?r.exports=l:this.alea=l})(Ph,e)}(Gt)),Gt.exports}var Ft={exports:{}},Dh=Ft.exports,Xo;function Oh(){return Xo||(Xo=1,function(e){(function(t,r,n){function a(i){var s=this,c="";s.x=0,s.y=0,s.z=0,s.w=0,s.next=function(){var d=s.x^s.x<<11;return s.x=s.y,s.y=s.z,s.z=s.w,s.w^=s.w>>>19^d^d>>>8},i===(i|0)?s.x=i:c+=i;for(var u=0;u<c.length+64;u++)s.x^=c.charCodeAt(u)|0,s.next()}function o(i,s){return s.x=i.x,s.y=i.y,s.z=i.z,s.w=i.w,s}function l(i,s){var c=new a(i),u=s&&s.state,d=function(){return(c.next()>>>0)/4294967296};return d.double=function(){do var h=c.next()>>>11,f=(c.next()>>>0)/4294967296,b=(h+f)/(1<<21);while(b===0);return b},d.int32=c.next,d.quick=d,u&&(typeof u=="object"&&o(u,c),d.state=function(){return o(c,{})}),d}r&&r.exports?r.exports=l:this.xor128=l})(Dh,e)}(Ft)),Ft.exports}var Mt={exports:{}},Gh=Mt.exports,Yo;function Fh(){return Yo||(Yo=1,function(e){(function(t,r,n){function a(i){var s=this,c="";s.next=function(){var d=s.x^s.x>>>2;return s.x=s.y,s.y=s.z,s.z=s.w,s.w=s.v,(s.d=s.d+362437|0)+(s.v=s.v^s.v<<4^(d^d<<1))|0},s.x=0,s.y=0,s.z=0,s.w=0,s.v=0,i===(i|0)?s.x=i:c+=i;for(var u=0;u<c.length+64;u++)s.x^=c.charCodeAt(u)|0,u==c.length&&(s.d=s.x<<10^s.x>>>4),s.next()}function o(i,s){return s.x=i.x,s.y=i.y,s.z=i.z,s.w=i.w,s.v=i.v,s.d=i.d,s}function l(i,s){var c=new a(i),u=s&&s.state,d=function(){return(c.next()>>>0)/4294967296};return d.double=function(){do var h=c.next()>>>11,f=(c.next()>>>0)/4294967296,b=(h+f)/(1<<21);while(b===0);return b},d.int32=c.next,d.quick=d,u&&(typeof u=="object"&&o(u,c),d.state=function(){return o(c,{})}),d}r&&r.exports?r.exports=l:this.xorwow=l})(Gh,e)}(Mt)),Mt.exports}var $t={exports:{}},Mh=$t.exports,Ko;function $h(){return Ko||(Ko=1,function(e){(function(t,r,n){function a(i){var s=this;s.next=function(){var u=s.x,d=s.i,h,f;return h=u[d],h^=h>>>7,f=h^h<<24,h=u[d+1&7],f^=h^h>>>10,h=u[d+3&7],f^=h^h>>>3,h=u[d+4&7],f^=h^h<<7,h=u[d+7&7],h=h^h<<13,f^=h^h<<9,u[d]=f,s.i=d+1&7,f};function c(u,d){var h,f=[];if(d===(d|0))f[0]=d;else for(d=""+d,h=0;h<d.length;++h)f[h&7]=f[h&7]<<15^d.charCodeAt(h)+f[h+1&7]<<13;for(;f.length<8;)f.push(0);for(h=0;h<8&&f[h]===0;++h);for(h==8?f[7]=-1:f[h],u.x=f,u.i=0,h=256;h>0;--h)u.next()}c(s,i)}function o(i,s){return s.x=i.x.slice(),s.i=i.i,s}function l(i,s){i==null&&(i=+new Date);var c=new a(i),u=s&&s.state,d=function(){return(c.next()>>>0)/4294967296};return d.double=function(){do var h=c.next()>>>11,f=(c.next()>>>0)/4294967296,b=(h+f)/(1<<21);while(b===0);return b},d.int32=c.next,d.quick=d,u&&(u.x&&o(u,c),d.state=function(){return o(c,{})}),d}r&&r.exports?r.exports=l:this.xorshift7=l})(Mh,e)}($t)),$t.exports}var Ht={exports:{}},Hh=Ht.exports,Qo;function Bh(){return Qo||(Qo=1,function(e){(function(t,r,n){function a(i){var s=this;s.next=function(){var u=s.w,d=s.X,h=s.i,f,b;return s.w=u=u+1640531527|0,b=d[h+34&127],f=d[h=h+1&127],b^=b<<13,f^=f<<17,b^=b>>>15,f^=f>>>12,b=d[h]=b^f,s.i=h,b+(u^u>>>16)|0};function c(u,d){var h,f,b,y,k,I=[],E=128;for(d===(d|0)?(f=d,d=null):(d=d+"\0",f=0,E=Math.max(E,d.length)),b=0,y=-32;y<E;++y)d&&(f^=d.charCodeAt((y+32)%d.length)),y===0&&(k=f),f^=f<<10,f^=f>>>15,f^=f<<4,f^=f>>>13,y>=0&&(k=k+1640531527|0,h=I[y&127]^=f+k,b=h==0?b+1:0);for(b>=128&&(I[(d&&d.length||0)&127]=-1),b=127,y=4*128;y>0;--y)f=I[b+34&127],h=I[b=b+1&127],f^=f<<13,h^=h<<17,f^=f>>>15,h^=h>>>12,I[b]=f^h;u.w=k,u.X=I,u.i=b}c(s,i)}function o(i,s){return s.i=i.i,s.w=i.w,s.X=i.X.slice(),s}function l(i,s){i==null&&(i=+new Date);var c=new a(i),u=s&&s.state,d=function(){return(c.next()>>>0)/4294967296};return d.double=function(){do var h=c.next()>>>11,f=(c.next()>>>0)/4294967296,b=(h+f)/(1<<21);while(b===0);return b},d.int32=c.next,d.quick=d,u&&(u.X&&o(u,c),d.state=function(){return o(c,{})}),d}r&&r.exports?r.exports=l:this.xor4096=l})(Hh,e)}(Ht)),Ht.exports}var Bt={exports:{}},Vh=Bt.exports,Jo;function Uh(){return Jo||(Jo=1,function(e){(function(t,r,n){function a(i){var s=this,c="";s.next=function(){var d=s.b,h=s.c,f=s.d,b=s.a;return d=d<<25^d>>>7^h,h=h-f|0,f=f<<24^f>>>8^b,b=b-d|0,s.b=d=d<<20^d>>>12^h,s.c=h=h-f|0,s.d=f<<16^h>>>16^b,s.a=b-d|0},s.a=0,s.b=0,s.c=-1640531527,s.d=1367130551,i===Math.floor(i)?(s.a=i/4294967296|0,s.b=i|0):c+=i;for(var u=0;u<c.length+20;u++)s.b^=c.charCodeAt(u)|0,s.next()}function o(i,s){return s.a=i.a,s.b=i.b,s.c=i.c,s.d=i.d,s}function l(i,s){var c=new a(i),u=s&&s.state,d=function(){return(c.next()>>>0)/4294967296};return d.double=function(){do var h=c.next()>>>11,f=(c.next()>>>0)/4294967296,b=(h+f)/(1<<21);while(b===0);return b},d.int32=c.next,d.quick=d,u&&(typeof u=="object"&&o(u,c),d.state=function(){return o(c,{})}),d}r&&r.exports?r.exports=l:this.tychei=l})(Vh,e)}(Bt)),Bt.exports}var Vt={exports:{}};const qh={},Wh=Object.freeze(Object.defineProperty({__proto__:null,default:qh},Symbol.toStringTag,{value:"Module"})),Xh=pi(Wh);var Yh=Vt.exports,Zo;function Kh(){return Zo||(Zo=1,function(e){(function(t,r,n){var a=256,o=6,l=52,i="random",s=n.pow(a,o),c=n.pow(2,l),u=c*2,d=a-1,h;function f(A,N,z){var m=[];N=N==!0?{entropy:!0}:N||{};var _=I(k(N.entropy?[A,j(r)]:A??E(),3),m),x=new b(m),T=function(){for(var R=x.g(o),O=s,w=0;R<c;)R=(R+w)*a,O*=a,w=x.g(1);for(;R>=u;)R/=2,O/=2,w>>>=1;return(R+w)/O};return T.int32=function(){return x.g(4)|0},T.quick=function(){return x.g(4)/4294967296},T.double=T,I(j(x.S),r),(N.pass||z||function(R,O,w,H){return H&&(H.S&&y(H,x),R.state=function(){return y(x,{})}),w?(n[i]=R,O):R})(T,_,"global"in N?N.global:this==n,N.state)}function b(A){var N,z=A.length,m=this,_=0,x=m.i=m.j=0,T=m.S=[];for(z||(A=[z++]);_<a;)T[_]=_++;for(_=0;_<a;_++)T[_]=T[x=d&x+A[_%z]+(N=T[_])],T[x]=N;(m.g=function(R){for(var O,w=0,H=m.i,K=m.j,D=m.S;R--;)O=D[H=d&H+1],w=w*a+D[d&(D[H]=D[K=d&K+O])+(D[K]=O)];return m.i=H,m.j=K,w})(a)}function y(A,N){return N.i=A.i,N.j=A.j,N.S=A.S.slice(),N}function k(A,N){var z=[],m=typeof A,_;if(N&&m=="object")for(_ in A)try{z.push(k(A[_],N-1))}catch{}return z.length?z:m=="string"?A:A+"\0"}function I(A,N){for(var z=A+"",m,_=0;_<z.length;)N[d&_]=d&(m^=N[d&_]*19)+z.charCodeAt(_++);return j(N)}function E(){try{var A;return h&&(A=h.randomBytes)?A=A(a):(A=new Uint8Array(a),(t.crypto||t.msCrypto).getRandomValues(A)),j(A)}catch{var N=t.navigator,z=N&&N.plugins;return[+new Date,t,z,t.screen,j(r)]}}function j(A){return String.fromCharCode.apply(0,A)}if(I(n.random(),r),e.exports){e.exports=f;try{h=Xh}catch{}}else n["seed"+i]=f})(typeof self<"u"?self:Yh,[],Math)}(Vt)),Vt.exports}var Xr,ea;function Qh(){if(ea)return Xr;ea=1;var e=zh(),t=Oh(),r=Fh(),n=$h(),a=Bh(),o=Uh(),l=Kh();return l.alea=e,l.xor128=t,l.xorwow=r,l.xorshift7=n,l.xor4096=a,l.tychei=o,Xr=l,Xr}var Jh=Qh();const yn=He(Jh),Zh={unknown:"unknown",未知:"unknown",other:"unknown",category:"category",类别:"category",type:"category",分类:"category",organization:"organization",组织:"organization",org:"organization",company:"organization",公司:"organization",机构:"organization",event:"event",事件:"event",activity:"event",活动:"event",person:"person",人物:"person",people:"person",human:"person",人:"person",animal:"animal",动物:"animal",creature:"animal",生物:"animal",geo:"geo",地理:"geo",geography:"geo",地域:"geo",location:"location",地点:"location",place:"location",address:"location",位置:"location",地址:"location",technology:"technology",技术:"technology",tech:"technology",科技:"technology",equipment:"equipment",设备:"equipment",device:"equipment",装备:"equipment",weapon:"weapon",武器:"weapon",arms:"weapon",军火:"weapon",object:"object",物品:"object",stuff:"object",物体:"object",group:"group",群组:"group",community:"group",社区:"group"},ta={unknown:"#f4d371",category:"#e3493b",organization:"#0f705d",event:"#00bfa0",person:"#4169E1",animal:"#84a3e1",geo:"#ff99cc",location:"#cf6d17",technology:"#b300b3",equipment:"#2F4F4F",weapon:"#4421af",object:"#00cc00",group:"#0f558a"},eg=["#5a2c6d","#0000ff","#cd071e","#00CED1","#9b3a31","#b2e061","#bd7ebe","#6ef7b3","#003366","#DEB887"],ra=e=>{const t="#5D6D7E",r=e?e.toLowerCase():"unknown",n=te.getState().typeColorMap;if(n.has(r))return n.get(r)||t;const a=Zh[r];if(a){const c=ta[a],u=new Map(n);return u.set(r,c),te.setState({typeColorMap:u}),c}const o=new Set(Array.from(n.entries()).filter(([,c])=>!Object.values(ta).includes(c)).map(([,c])=>c)),i=eg.find(c=>!o.has(c))||t,s=new Map(n);return s.set(r,i),te.setState({typeColorMap:s}),i},tg=e=>{if(!e)return console.log("Graph validation failed: graph is null"),!1;if(!Array.isArray(e.nodes)||!Array.isArray(e.edges))return console.log("Graph validation failed: nodes or edges is not an array"),!1;if(e.nodes.length===0)return console.log("Graph validation failed: nodes array is empty"),!1;for(const t of e.nodes)if(!t.id||!t.labels||!t.properties)return console.log("Graph validation failed: invalid node structure"),!1;for(const t of e.edges)if(!t.id||!t.source||!t.target)return console.log("Graph validation failed: invalid edge structure"),!1;for(const t of e.edges){const r=e.getNode(t.source),n=e.getNode(t.target);if(r==null||n==null)return console.log("Graph validation failed: edge references non-existent node"),!1}return console.log("Graph validation passed"),!0},rg=async(e,t,r)=>{let n=null;if(!te.getState().lastSuccessfulQueryLabel){console.log("Last successful queryLabel is empty");try{await te.getState().fetchAllDatabaseLabels()}catch(i){console.error("Failed to fetch all database labels:",i)}}te.getState().setLabelsFetchAttempted(!0);const o=e||"*";try{console.log(`Fetching graph label: ${o}, depth: ${t}, nodes: ${r}`),n=await Ca(o,t,r)}catch(i){return En.getState().setErrorMessage(rr(i),"Query Graphs Error!"),null}let l=null;if(n){const i={},s={};for(let h=0;h<n.nodes.length;h++){const f=n.nodes[h];i[f.id]=h,f.x=Math.random(),f.y=Math.random(),f.degree=0,f.size=10}for(let h=0;h<n.edges.length;h++){const f=n.edges[h];s[f.id]=h;const b=i[f.source],y=i[f.target];if(b!==void 0&&b!==void 0){const k=n.nodes[b],I=n.nodes[y];if(!k){console.error(`Source node ${f.source} is undefined`);continue}if(!I){console.error(`Target node ${f.target} is undefined`);continue}k.degree+=1,I.degree+=1}}let c=Number.MAX_SAFE_INTEGER,u=0;for(const h of n.nodes)c=Math.min(c,h.degree),u=Math.max(u,h.degree);const d=u-c;if(d>0){const h=Jr-ut;for(const f of n.nodes)f.size=Math.round(ut+h*Math.pow((f.degree-c)/d,.5))}l=new rl,l.nodes=n.nodes,l.edges=n.edges,l.nodeIdMap=i,l.edgeIdMap=s,tg(l)||(l=null,console.warn("Invalid graph data")),console.log("Graph data loaded")}return{rawGraph:l,is_truncated:n.is_truncated}},ng=e=>{var i,s;const t=Z.getState().minEdgeSize,r=Z.getState().maxEdgeSize;if(!e||!e.nodes.length)return console.log("No graph data available, skipping sigma graph creation"),null;const n=new Yr;for(const c of(e==null?void 0:e.nodes)??[]){yn(c.id+Date.now().toString(),{global:!0});const u=Math.random(),d=Math.random();n.addNode(c.id,{label:c.labels.join(", "),color:c.color,x:u,y:d,size:c.size,borderColor:Qr,borderSize:.2})}for(const c of(e==null?void 0:e.edges)??[]){const u=((i=c.properties)==null?void 0:i.weight)!==void 0?Number(c.properties.weight):1;c.dynamicId=n.addEdge(c.source,c.target,{label:((s=c.properties)==null?void 0:s.keywords)||void 0,size:u,originalWeight:u,type:"curvedNoArrow"})}let a=Number.MAX_SAFE_INTEGER,o=0;n.forEachEdge(c=>{const u=n.getEdgeAttribute(c,"originalWeight")||1;a=Math.min(a,u),o=Math.max(o,u)});const l=o-a;if(l>0){const c=r-t;n.forEachEdge(u=>{const d=n.getEdgeAttribute(u,"originalWeight")||1,h=t+c*Math.pow((d-a)/l,.5);n.setEdgeAttribute(u,"size",h)})}else n.forEachEdge(c=>{n.setEdgeAttribute(c,"size",t)});return n},og=()=>{const{t:e}=Se(),t=Z.use.queryLabel(),r=te.use.rawGraph(),n=te.use.sigmaGraph(),a=Z.use.graphQueryMaxDepth(),o=Z.use.graphMaxNodes(),l=te.use.isFetching(),i=te.use.nodeToExpand(),s=te.use.nodeToPrune(),c=p.useRef(!1),u=p.useRef(!1),d=p.useRef(!1),h=p.useCallback(I=>(r==null?void 0:r.getNode(I))||null,[r]),f=p.useCallback((I,E=!0)=>(r==null?void 0:r.getEdge(I,E))||null,[r]),b=p.useRef(!1);p.useEffect(()=>{if(!t&&(r!==null||n!==null)){const I=te.getState();I.reset(),I.setGraphDataFetchAttempted(!1),I.setLabelsFetchAttempted(!1),c.current=!1,u.current=!1}},[t,r,n]),p.useEffect(()=>{if(!b.current&&!(!t&&d.current)&&!l&&!te.getState().graphDataFetchAttempted){b.current=!0,te.getState().setGraphDataFetchAttempted(!0);const I=te.getState();I.setIsFetching(!0),I.clearSelection(),I.sigmaGraph&&I.sigmaGraph.forEachNode(z=>{var m;(m=I.sigmaGraph)==null||m.setNodeAttribute(z,"highlighted",!1)}),console.log("Preparing graph data...");const E=t,j=a,A=o;let N;E?N=rg(E,j,A):(console.log("Query label is empty, show empty graph"),N=Promise.resolve({rawGraph:null,is_truncated:!1})),N.then(z=>{const m=te.getState(),_=z==null?void 0:z.rawGraph;if(_&&_.nodes&&_.nodes.forEach(x=>{var R;const T=(R=x.properties)==null?void 0:R.entity_type;x.color=ra(T)}),z!=null&&z.is_truncated&&rt.info(e("graphPanel.dataIsTruncated","Graph data is truncated to Max Nodes")),m.reset(),!_||!_.nodes||_.nodes.length===0){const x=new Yr;x.addNode("empty-graph-node",{label:e("graphPanel.emptyGraph"),color:"#5D6D7E",x:.5,y:.5,size:15,borderColor:Qr,borderSize:.2}),m.setSigmaGraph(x),m.setRawGraph(null),m.setGraphIsEmpty(!0);const T=En.getState().message,R=T&&T.includes("Authentication required");!R&&E&&Z.getState().setQueryLabel(""),R?console.log("Keep queryLabel for post-login reload"):m.setLastSuccessfulQueryLabel(""),console.log(`Graph data is empty, created graph with empty graph node. Auth error: ${R}`)}else{const x=ng(_);_.buildDynamicMap(),m.setSigmaGraph(x),m.setRawGraph(_),m.setGraphIsEmpty(!1),m.setLastSuccessfulQueryLabel(E),m.setMoveToSelectedNode(!0)}c.current=!0,u.current=!0,b.current=!1,m.setIsFetching(!1),(!_||!_.nodes||_.nodes.length===0)&&!E&&(d.current=!0)}).catch(z=>{console.error("Error fetching graph data:",z);const m=te.getState();m.setIsFetching(!1),c.current=!1,b.current=!1,m.setGraphDataFetchAttempted(!1),m.setLastSuccessfulQueryLabel("")})}},[t,a,o,l,e]),p.useEffect(()=>{i&&((async E=>{var j,A,N,z,m,_;if(!(!E||!n||!r))try{const x=r.getNode(E);if(!x){console.error("Node not found:",E);return}const T=x.labels[0];if(!T){console.error("Node has no label:",E);return}const R=await Ca(T,2,1e3);if(!R||!R.nodes||!R.edges){console.error("Failed to fetch extended graph");return}const O=[];for(const F of R.nodes){yn(F.id,{global:!0});const Q=(j=F.properties)==null?void 0:j.entity_type,q=ra(Q);O.push({id:F.id,labels:F.labels,properties:F.properties,size:10,x:Math.random(),y:Math.random(),color:q,degree:0})}const w=[];for(const F of R.edges)w.push({id:F.id,source:F.source,target:F.target,type:F.type,properties:F.properties,dynamicId:""});const H={};n.forEachNode(F=>{H[F]={x:n.getNodeAttribute(F,"x"),y:n.getNodeAttribute(F,"y")}});const K=new Set(n.nodes()),D=new Set,C=new Set,S=1;let B=0,ae=Number.MAX_SAFE_INTEGER,M=0;n.forEachNode(F=>{const Q=n.degree(F);B=Math.max(B,Q)}),n.forEachEdge(F=>{const Q=n.getEdgeAttribute(F,"originalWeight")||1;ae=Math.min(ae,Q),M=Math.max(M,Q)});for(const F of O){if(K.has(F.id))continue;w.some(q=>q.source===E&&q.target===F.id||q.target===E&&q.source===F.id)&&D.add(F.id)}const v=new Map,P=new Map,V=new Set;for(const F of w){const Q=K.has(F.source)||D.has(F.source),q=K.has(F.target)||D.has(F.target);Q&&q?(C.add(F.id),D.has(F.source)?v.set(F.source,(v.get(F.source)||0)+1):K.has(F.source)&&P.set(F.source,(P.get(F.source)||0)+1),D.has(F.target)?v.set(F.target,(v.get(F.target)||0)+1):K.has(F.target)&&P.set(F.target,(P.get(F.target)||0)+1)):(n.hasNode(F.source)?V.add(F.source):D.has(F.source)&&(V.add(F.source),v.set(F.source,(v.get(F.source)||0)+1)),n.hasNode(F.target)?V.add(F.target):D.has(F.target)&&(V.add(F.target),v.set(F.target,(v.get(F.target)||0)+1)))}const $=(F,Q,q,U)=>{const L=U-q||1,oe=Jr-ut;for(const ue of Q)if(F.hasNode(ue)){let re=F.degree(ue);re+=1;const ee=Math.min(re,U+1),G=Math.round(ut+oe*Math.pow((ee-q)/L,.5));F.setNodeAttribute(ue,"size",G)}},J=(F,Q,q)=>{const U=Z.getState().minEdgeSize,L=Z.getState().maxEdgeSize,oe=q-Q||1,ue=L-U;F.forEachEdge(re=>{const ee=F.getEdgeAttribute(re,"originalWeight")||1,G=U+ue*Math.pow((ee-Q)/oe,.5);F.setEdgeAttribute(re,"size",G)})};if(D.size===0){$(n,V,S,B),rt.info(e("graphPanel.propertiesView.node.noNewNodes"));return}for(const[,F]of v.entries())B=Math.max(B,F);for(const[F,Q]of P.entries()){const U=n.degree(F)+Q;B=Math.max(B,U)}const X=B-S||1,Y=Jr-ut,ie=((A=te.getState().sigmaInstance)==null?void 0:A.getCamera().ratio)||1,ne=Math.max(Math.sqrt(x.size)*4,Math.sqrt(D.size)*3)/ie;yn(Date.now().toString(),{global:!0});const se=Math.random()*2*Math.PI;console.log("nodeSize:",x.size,"nodesToAdd:",D.size),console.log("cameraRatio:",Math.round(ie*100)/100,"spreadFactor:",Math.round(ne*100)/100);for(const F of D){const Q=O.find(ee=>ee.id===F),q=v.get(F)||0,U=Math.min(q,B+1),L=Math.round(ut+Y*Math.pow((U-S)/X,.5)),oe=2*Math.PI*(Array.from(D).indexOf(F)/D.size),ue=((N=H[F])==null?void 0:N.x)||H[x.id].x+Math.cos(se+oe)*ne,re=((z=H[F])==null?void 0:z.y)||H[x.id].y+Math.sin(se+oe)*ne;n.addNode(F,{label:Q.labels.join(", "),color:Q.color,x:ue,y:re,size:L,borderColor:Qr,borderSize:.2}),r.getNode(F)||(Q.size=L,Q.x=ue,Q.y=re,Q.degree=q,r.nodes.push(Q),r.nodeIdMap[F]=r.nodes.length-1)}for(const F of C){const Q=w.find(U=>U.id===F);if(n.hasEdge(Q.source,Q.target))continue;const q=((m=Q.properties)==null?void 0:m.weight)!==void 0?Number(Q.properties.weight):1;ae=Math.min(ae,q),M=Math.max(M,q),Q.dynamicId=n.addEdge(Q.source,Q.target,{label:((_=Q.properties)==null?void 0:_.keywords)||void 0,size:q,originalWeight:q,type:"curvedNoArrow"}),r.getEdge(Q.id,!1)?console.error("Edge already exists in rawGraph:",Q.id):(r.edges.push(Q),r.edgeIdMap[Q.id]=r.edges.length-1,r.edgeDynamicIdMap[Q.dynamicId]=r.edges.length-1)}if(r.buildDynamicMap(),te.getState().resetSearchEngine(),$(n,V,S,B),J(n,ae,M),n.hasNode(E)){const F=n.degree(E),Q=Math.min(F,B+1),q=Math.round(ut+Y*Math.pow((Q-S)/X,.5));n.setNodeAttribute(E,"size",q),x.size=q,x.degree=F}}catch(x){console.error("Error expanding node:",x)}})(i),window.setTimeout(()=>{te.getState().triggerNodeExpand(null)},0))},[i,n,r,e]);const y=p.useCallback((I,E)=>{const j=new Set([I]);return E.forEachNode(A=>{if(A===I)return;const N=E.neighbors(A);N.length===1&&N[0]===I&&j.add(A)}),j},[]);return p.useEffect(()=>{s&&((E=>{if(!(!E||!n||!r))try{const j=te.getState();if(!n.hasNode(E)){console.error("Node not found:",E);return}const A=y(E,n);if(A.size===n.nodes().length){rt.error(e("graphPanel.propertiesView.node.deleteAllNodesError"));return}j.clearSelection();for(const N of A){n.dropNode(N);const z=r.nodeIdMap[N];if(z!==void 0){const m=r.edges.filter(_=>_.source===N||_.target===N);for(const _ of m){const x=r.edgeIdMap[_.id];if(x!==void 0){r.edges.splice(x,1);for(const[T,R]of Object.entries(r.edgeIdMap))R>x&&(r.edgeIdMap[T]=R-1);delete r.edgeIdMap[_.id],delete r.edgeDynamicIdMap[_.dynamicId]}}r.nodes.splice(z,1);for(const[_,x]of Object.entries(r.nodeIdMap))x>z&&(r.nodeIdMap[_]=x-1);delete r.nodeIdMap[N]}}r.buildDynamicMap(),te.getState().resetSearchEngine(),A.size>1&&rt.info(e("graphPanel.propertiesView.node.nodesRemoved",{count:A.size}))}catch(j){console.error("Error pruning node:",j)}})(s),window.setTimeout(()=>{te.getState().triggerNodePrune(null)},0))},[s,n,r,y,e]),{lightrageGraph:p.useCallback(()=>{if(n)return n;console.log("Creating new Sigma graph instance");const I=new Yr;return te.getState().setSigmaGraph(I),I},[n]),getNode:h,getEdge:f}},ag=({name:e})=>{const{t}=Se(),r=n=>{const a=`graphPanel.propertiesView.node.propertyNames.${n}`,o=t(a);return o===a?n:o};return g.jsx("span",{className:"text-primary/60 tracking-wide whitespace-nowrap",children:r(e)})},sg=({onClick:e})=>g.jsx("div",{children:g.jsx(iu,{className:"h-3 w-3 text-gray-500 hover:text-gray-700 cursor-pointer",onClick:e})}),ig=({value:e,onClick:t,tooltip:r})=>g.jsx("div",{className:"flex items-center gap-1 overflow-hidden",children:g.jsx(Vs,{className:"hover:bg-primary/20 rounded p-1 overflow-hidden text-ellipsis whitespace-nowrap",tooltipClassName:"max-w-80 -translate-x-15",text:e,tooltip:r||(typeof e=="string"?e:JSON.stringify(e,null,2)),side:"left",onClick:t})}),lg=({isOpen:e,onClose:t,onSave:r,propertyName:n,initialValue:a,isSubmitting:o=!1})=>{const{t:l}=Se(),[i,s]=p.useState(""),[c,u]=p.useState(null);p.useEffect(()=>{e&&s(a)},[e,a]);const d=b=>{const y=`graphPanel.propertiesView.node.propertyNames.${b}`,k=l(y);return k===y?b:k},h=b=>{switch(b){case"description":return{className:"max-h-[50vh] min-h-[10em] resize-y",style:{height:"70vh",minHeight:"20em",resize:"vertical"}};case"entity_id":return{rows:2,className:"",style:{}};case"keywords":return{rows:4,className:"",style:{}};default:return{rows:5,className:"",style:{}}}},f=async()=>{if(i.trim()!==""){u(null);try{await r(i),t()}catch(b){console.error("Save error:",b),u(typeof b=="object"&&b!==null&&b.message||l("common.saveFailed"))}}};return g.jsx(Pu,{open:e,onOpenChange:b=>!b&&t(),children:g.jsxs(Xa,{className:"sm:max-w-md",children:[g.jsxs(Ya,{children:[g.jsx(Qa,{children:l("graphPanel.propertiesView.editProperty",{property:d(n)})}),g.jsx(Ja,{children:l("graphPanel.propertiesView.editPropertyDescription")})]}),c&&g.jsx("div",{className:"bg-destructive/15 text-destructive px-4 py-2 rounded-md text-sm mt-2",children:c}),g.jsx("div",{className:"grid gap-4 py-4",children:(()=>{const b=h(n);return n==="description"?g.jsx("textarea",{value:i,onChange:y=>s(y.target.value),className:`border-input focus-visible:ring-ring flex w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 ${b.className}`,style:b.style,disabled:o}):g.jsx("textarea",{value:i,onChange:y=>s(y.target.value),rows:b.rows,className:`border-input focus-visible:ring-ring flex w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 ${b.className}`,disabled:o})})()}),g.jsxs(Ka,{children:[g.jsx(be,{type:"button",variant:"outline",onClick:t,disabled:o,children:l("common.cancel")}),g.jsx(be,{type:"button",onClick:f,disabled:o,children:o?g.jsxs(g.Fragment,{children:[g.jsx("span",{className:"mr-2",children:g.jsxs("svg",{className:"animate-spin h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[g.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),g.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),l("common.saving")]}):l("common.save")})]})]})})},cg=({name:e,value:t,onClick:r,nodeId:n,edgeId:a,entityId:o,dynamicId:l,entityType:i,sourceId:s,targetId:c,onValueChange:u,isEditable:d=!1,tooltip:h})=>{const{t:f}=Se(),[b,y]=p.useState(!1),[k,I]=p.useState(!1),[E,j]=p.useState(t);p.useEffect(()=>{j(t)},[t]);const A=()=>{d&&!b&&y(!0)},N=()=>{y(!1)},z=async m=>{if(k||m===String(E)){y(!1);return}I(!0);try{if(i==="node"&&o&&n){let _={[e]:m};if(e==="entity_id"){if(await cl(m)){rt.error(f("graphPanel.propertiesView.errors.duplicateName"));return}_={entity_name:m}}await il(o,_,!0);try{await te.getState().updateNodeAndSelect(n,o,e,m)}catch(x){throw console.error("Error updating node in graph:",x),new Error("Failed to update node in graph")}rt.success(f("graphPanel.propertiesView.success.entityUpdated"))}else if(i==="edge"&&s&&c&&a&&l){const _={[e]:m};await ll(s,c,_);try{await te.getState().updateEdgeAndSelect(a,l,s,c,e,m)}catch(x){throw console.error(`Error updating edge ${s}->${c} in graph:`,x),new Error("Failed to update edge in graph")}rt.success(f("graphPanel.propertiesView.success.relationUpdated"))}y(!1),j(m),u==null||u(m)}catch(_){console.error("Error updating property:",_),rt.error(f("graphPanel.propertiesView.errors.updateFailed"))}finally{I(!1)}};return g.jsxs("div",{className:"flex items-center gap-1 overflow-hidden",children:[g.jsx(ag,{name:e}),g.jsx(sg,{onClick:A}),":",g.jsx(ig,{value:E,onClick:r,tooltip:h||(typeof E=="string"?E:JSON.stringify(E,null,2))}),g.jsx(lg,{isOpen:b,onClose:N,onSave:z,propertyName:e,initialValue:String(E),isSubmitting:k})]})},ug=()=>{const{getNode:e,getEdge:t}=og(),r=te.use.selectedNode(),n=te.use.focusedNode(),a=te.use.selectedEdge(),o=te.use.focusedEdge(),l=te.use.graphDataVersion(),[i,s]=p.useState(null),[c,u]=p.useState(null);return p.useEffect(()=>{let d=null,h=null;n?(d="node",h=e(n)):r?(d="node",h=e(r)):o?(d="edge",h=t(o,!0)):a&&(d="edge",h=t(a,!0)),h?(d=="node"?s(dg(h)):s(fg(h)),u(d)):(s(null),u(null))},[n,r,o,a,l,s,u,e,t]),i?g.jsx("div",{className:"bg-background/80 max-w-xs rounded-lg border-2 p-2 text-xs backdrop-blur-lg",children:c=="node"?g.jsx(hg,{node:i}):g.jsx(gg,{edge:i})}):g.jsx(g.Fragment,{})},dg=e=>{const t=te.getState(),r=[];if(t.sigmaGraph&&t.rawGraph)try{if(!t.sigmaGraph.hasNode(e.id))return console.warn("Node not found in sigmaGraph:",e.id),{...e,relationships:[]};const n=t.sigmaGraph.edges(e.id);for(const a of n){if(!t.sigmaGraph.hasEdge(a))continue;const o=t.rawGraph.getEdge(a,!0);if(o){const i=e.id===o.source?o.target:o.source;if(!t.sigmaGraph.hasNode(i))continue;const s=t.rawGraph.getNode(i);s&&r.push({type:"Neighbour",id:i,label:s.properties.entity_id?s.properties.entity_id:s.labels.join(", ")})}}}catch(n){console.error("Error refining node properties:",n)}return{...e,relationships:r}},fg=e=>{const t=te.getState();let r,n;if(t.sigmaGraph&&t.rawGraph)try{if(!t.sigmaGraph.hasEdge(e.dynamicId))return console.warn("Edge not found in sigmaGraph:",e.id,"dynamicId:",e.dynamicId),{...e,sourceNode:void 0,targetNode:void 0};t.sigmaGraph.hasNode(e.source)&&(r=t.rawGraph.getNode(e.source)),t.sigmaGraph.hasNode(e.target)&&(n=t.rawGraph.getNode(e.target))}catch(a){console.error("Error refining edge properties:",a)}return{...e,sourceNode:r,targetNode:n}},$e=({name:e,value:t,onClick:r,tooltip:n,nodeId:a,edgeId:o,dynamicId:l,entityId:i,entityType:s,sourceId:c,targetId:u,isEditable:d=!1})=>{const{t:h}=Se(),f=b=>{const y=`graphPanel.propertiesView.node.propertyNames.${b}`,k=h(y);return k===y?b:k};return d&&(e==="description"||e==="entity_id"||e==="keywords")?g.jsx(cg,{name:e,value:t,onClick:r,nodeId:a,entityId:i,edgeId:o,dynamicId:l,entityType:s,sourceId:c,targetId:u,isEditable:!0,tooltip:n||(typeof t=="string"?t:JSON.stringify(t,null,2))}):g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx("span",{className:"text-primary/60 tracking-wide whitespace-nowrap",children:f(e)}),":",g.jsx(Vs,{className:"hover:bg-primary/20 rounded p-1 overflow-hidden text-ellipsis",tooltipClassName:"max-w-80 -translate-x-13",text:t,tooltip:n||(typeof t=="string"?t:JSON.stringify(t,null,2)),side:"left",onClick:r})]})},hg=({node:e})=>{const{t}=Se(),r=()=>{te.getState().triggerNodeExpand(e.id)},n=()=>{te.getState().triggerNodePrune(e.id)};return g.jsxs("div",{className:"flex flex-col gap-2",children:[g.jsxs("div",{className:"flex justify-between items-center",children:[g.jsx("h3",{className:"text-md pl-1 font-bold tracking-wide text-blue-700",children:t("graphPanel.propertiesView.node.title")}),g.jsxs("div",{className:"flex gap-3",children:[g.jsx(be,{size:"icon",variant:"ghost",className:"h-7 w-7 border border-gray-400 hover:bg-gray-200 dark:border-gray-600 dark:hover:bg-gray-700",onClick:r,tooltip:t("graphPanel.propertiesView.node.expandNode"),children:g.jsx(qc,{className:"h-4 w-4 text-gray-700 dark:text-gray-300"})}),g.jsx(be,{size:"icon",variant:"ghost",className:"h-7 w-7 border border-gray-400 hover:bg-gray-200 dark:border-gray-600 dark:hover:bg-gray-700",onClick:n,tooltip:t("graphPanel.propertiesView.node.pruneNode"),children:g.jsx(vu,{className:"h-4 w-4 text-gray-900 dark:text-gray-300"})})]})]}),g.jsxs("div",{className:"bg-primary/5 max-h-96 overflow-auto rounded p-1",children:[g.jsx($e,{name:t("graphPanel.propertiesView.node.id"),value:String(e.id)}),g.jsx($e,{name:t("graphPanel.propertiesView.node.labels"),value:e.labels.join(", "),onClick:()=>{te.getState().setSelectedNode(e.id,!0)}}),g.jsx($e,{name:t("graphPanel.propertiesView.node.degree"),value:e.degree})]}),g.jsx("h3",{className:"text-md pl-1 font-bold tracking-wide text-amber-700",children:t("graphPanel.propertiesView.node.properties")}),g.jsx("div",{className:"bg-primary/5 max-h-96 overflow-auto rounded p-1",children:Object.keys(e.properties).sort().map(a=>a==="created_at"?null:g.jsx($e,{name:a,value:e.properties[a],nodeId:String(e.id),entityId:e.properties.entity_id,entityType:"node",isEditable:a==="description"||a==="entity_id"},a))}),e.relationships.length>0&&g.jsxs(g.Fragment,{children:[g.jsx("h3",{className:"text-md pl-1 font-bold tracking-wide text-emerald-700",children:t("graphPanel.propertiesView.node.relationships")}),g.jsx("div",{className:"bg-primary/5 max-h-96 overflow-auto rounded p-1",children:e.relationships.map(({type:a,id:o,label:l})=>g.jsx($e,{name:a,value:l,onClick:()=>{te.getState().setSelectedNode(o,!0)}},o))})]})]})},gg=({edge:e})=>{const{t}=Se();return g.jsxs("div",{className:"flex flex-col gap-2",children:[g.jsx("h3",{className:"text-md pl-1 font-bold tracking-wide text-violet-700",children:t("graphPanel.propertiesView.edge.title")}),g.jsxs("div",{className:"bg-primary/5 max-h-96 overflow-auto rounded p-1",children:[g.jsx($e,{name:t("graphPanel.propertiesView.edge.id"),value:e.id}),e.type&&g.jsx($e,{name:t("graphPanel.propertiesView.edge.type"),value:e.type}),g.jsx($e,{name:t("graphPanel.propertiesView.edge.source"),value:e.sourceNode?e.sourceNode.labels.join(", "):e.source,onClick:()=>{te.getState().setSelectedNode(e.source,!0)}}),g.jsx($e,{name:t("graphPanel.propertiesView.edge.target"),value:e.targetNode?e.targetNode.labels.join(", "):e.target,onClick:()=>{te.getState().setSelectedNode(e.target,!0)}})]}),g.jsx("h3",{className:"text-md pl-1 font-bold tracking-wide text-amber-700",children:t("graphPanel.propertiesView.edge.properties")}),g.jsx("div",{className:"bg-primary/5 max-h-96 overflow-auto rounded p-1",children:Object.keys(e.properties).sort().map(r=>{var n,a;return r==="created_at"?null:g.jsx($e,{name:r,value:e.properties[r],edgeId:String(e.id),dynamicId:String(e.dynamicId),entityType:"edge",sourceId:((n=e.sourceNode)==null?void 0:n.properties.entity_id)||e.source,targetId:((a=e.targetNode)==null?void 0:a.properties.entity_id)||e.target,isEditable:r==="description"||r==="keywords"},r)})})]})},pg=()=>{const{t:e}=Se(),t=Z.use.graphQueryMaxDepth(),r=Z.use.graphMaxNodes();return g.jsxs("div",{className:"absolute bottom-4 left-[calc(1rem+2.5rem)] flex items-center gap-2 text-xs text-gray-400",children:[g.jsxs("div",{children:[e("graphPanel.sideBar.settings.depth"),": ",t]}),g.jsxs("div",{children:[e("graphPanel.sideBar.settings.max"),": ",r]})]})},Us=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("bg-card text-card-foreground rounded-xl border shadow",e),...t}));Us.displayName="Card";const mg=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("flex flex-col space-y-1.5 p-6",e),...t}));mg.displayName="CardHeader";const vg=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("leading-none font-semibold tracking-tight",e),...t}));vg.displayName="CardTitle";const yg=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("text-muted-foreground text-sm",e),...t}));yg.displayName="CardDescription";const bg=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("p-6 pt-0",e),...t}));bg.displayName="CardContent";const wg=p.forwardRef(({className:e,...t},r)=>g.jsx("div",{ref:r,className:fe("flex items-center p-6 pt-0",e),...t}));wg.displayName="CardFooter";function xg(e,t){return p.useReducer((r,n)=>t[r][n]??r,e)}var Fn="ScrollArea",[qs,Dp]=wn(Fn),[Sg,Le]=qs(Fn),Ws=p.forwardRef((e,t)=>{const{__scopeScrollArea:r,type:n="hover",dir:a,scrollHideDelay:o=600,...l}=e,[i,s]=p.useState(null),[c,u]=p.useState(null),[d,h]=p.useState(null),[f,b]=p.useState(null),[y,k]=p.useState(null),[I,E]=p.useState(0),[j,A]=p.useState(0),[N,z]=p.useState(!1),[m,_]=p.useState(!1),x=Xe(t,R=>s(R)),T=Fi(a);return g.jsx(Sg,{scope:r,type:n,dir:T,scrollHideDelay:o,scrollArea:i,viewport:c,onViewportChange:u,content:d,onContentChange:h,scrollbarX:f,onScrollbarXChange:b,scrollbarXEnabled:N,onScrollbarXEnabledChange:z,scrollbarY:y,onScrollbarYChange:k,scrollbarYEnabled:m,onScrollbarYEnabledChange:_,onCornerWidthChange:E,onCornerHeightChange:A,children:g.jsx(Ee.div,{dir:T,...l,ref:x,style:{position:"relative","--radix-scroll-area-corner-width":I+"px","--radix-scroll-area-corner-height":j+"px",...e.style}})})});Ws.displayName=Fn;var Xs="ScrollAreaViewport",Ys=p.forwardRef((e,t)=>{const{__scopeScrollArea:r,children:n,nonce:a,...o}=e,l=Le(Xs,r),i=p.useRef(null),s=Xe(t,i,l.onViewportChange);return g.jsxs(g.Fragment,{children:[g.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),g.jsx(Ee.div,{"data-radix-scroll-area-viewport":"",...o,ref:s,style:{overflowX:l.scrollbarXEnabled?"scroll":"hidden",overflowY:l.scrollbarYEnabled?"scroll":"hidden",...e.style},children:g.jsx("div",{ref:l.onContentChange,style:{minWidth:"100%",display:"table"},children:n})})]})});Ys.displayName=Xs;var Ve="ScrollAreaScrollbar",Mn=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,a=Le(Ve,e.__scopeScrollArea),{onScrollbarXEnabledChange:o,onScrollbarYEnabledChange:l}=a,i=e.orientation==="horizontal";return p.useEffect(()=>(i?o(!0):l(!0),()=>{i?o(!1):l(!1)}),[i,o,l]),a.type==="hover"?g.jsx(_g,{...n,ref:t,forceMount:r}):a.type==="scroll"?g.jsx(Eg,{...n,ref:t,forceMount:r}):a.type==="auto"?g.jsx(Ks,{...n,ref:t,forceMount:r}):a.type==="always"?g.jsx($n,{...n,ref:t}):null});Mn.displayName=Ve;var _g=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,a=Le(Ve,e.__scopeScrollArea),[o,l]=p.useState(!1);return p.useEffect(()=>{const i=a.scrollArea;let s=0;if(i){const c=()=>{window.clearTimeout(s),l(!0)},u=()=>{s=window.setTimeout(()=>l(!1),a.scrollHideDelay)};return i.addEventListener("pointerenter",c),i.addEventListener("pointerleave",u),()=>{window.clearTimeout(s),i.removeEventListener("pointerenter",c),i.removeEventListener("pointerleave",u)}}},[a.scrollArea,a.scrollHideDelay]),g.jsx(St,{present:r||o,children:g.jsx(Ks,{"data-state":o?"visible":"hidden",...n,ref:t})})}),Eg=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,a=Le(Ve,e.__scopeScrollArea),o=e.orientation==="horizontal",l=fr(()=>s("SCROLL_END"),100),[i,s]=xg("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return p.useEffect(()=>{if(i==="idle"){const c=window.setTimeout(()=>s("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(c)}},[i,a.scrollHideDelay,s]),p.useEffect(()=>{const c=a.viewport,u=o?"scrollLeft":"scrollTop";if(c){let d=c[u];const h=()=>{const f=c[u];d!==f&&(s("SCROLL"),l()),d=f};return c.addEventListener("scroll",h),()=>c.removeEventListener("scroll",h)}},[a.viewport,o,s,l]),g.jsx(St,{present:r||i!=="hidden",children:g.jsx($n,{"data-state":i==="hidden"?"hidden":"visible",...n,ref:t,onPointerEnter:Ce(e.onPointerEnter,()=>s("POINTER_ENTER")),onPointerLeave:Ce(e.onPointerLeave,()=>s("POINTER_LEAVE"))})})}),Ks=p.forwardRef((e,t)=>{const r=Le(Ve,e.__scopeScrollArea),{forceMount:n,...a}=e,[o,l]=p.useState(!1),i=e.orientation==="horizontal",s=fr(()=>{if(r.viewport){const c=r.viewport.offsetWidth<r.viewport.scrollWidth,u=r.viewport.offsetHeight<r.viewport.scrollHeight;l(i?c:u)}},10);return xt(r.viewport,s),xt(r.content,s),g.jsx(St,{present:n||o,children:g.jsx($n,{"data-state":o?"visible":"hidden",...a,ref:t})})}),$n=p.forwardRef((e,t)=>{const{orientation:r="vertical",...n}=e,a=Le(Ve,e.__scopeScrollArea),o=p.useRef(null),l=p.useRef(0),[i,s]=p.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=ti(i.viewport,i.content),u={...n,sizes:i,onSizesChange:s,hasThumb:c>0&&c<1,onThumbChange:h=>o.current=h,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:h=>l.current=h};function d(h,f){return jg(h,l.current,i,f)}return r==="horizontal"?g.jsx(Cg,{...u,ref:t,onThumbPositionChange:()=>{if(a.viewport&&o.current){const h=a.viewport.scrollLeft,f=na(h,i,a.dir);o.current.style.transform=`translate3d(${f}px, 0, 0)`}},onWheelScroll:h=>{a.viewport&&(a.viewport.scrollLeft=h)},onDragScroll:h=>{a.viewport&&(a.viewport.scrollLeft=d(h,a.dir))}}):r==="vertical"?g.jsx(kg,{...u,ref:t,onThumbPositionChange:()=>{if(a.viewport&&o.current){const h=a.viewport.scrollTop,f=na(h,i);o.current.style.transform=`translate3d(0, ${f}px, 0)`}},onWheelScroll:h=>{a.viewport&&(a.viewport.scrollTop=h)},onDragScroll:h=>{a.viewport&&(a.viewport.scrollTop=d(h))}}):null}),Cg=p.forwardRef((e,t)=>{const{sizes:r,onSizesChange:n,...a}=e,o=Le(Ve,e.__scopeScrollArea),[l,i]=p.useState(),s=p.useRef(null),c=Xe(t,s,o.onScrollbarXChange);return p.useEffect(()=>{s.current&&i(getComputedStyle(s.current))},[s]),g.jsx(Js,{"data-orientation":"horizontal",...a,ref:c,sizes:r,style:{bottom:0,left:o.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:o.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":dr(r)+"px",...e.style},onThumbPointerDown:u=>e.onThumbPointerDown(u.x),onDragScroll:u=>e.onDragScroll(u.x),onWheelScroll:(u,d)=>{if(o.viewport){const h=o.viewport.scrollLeft+u.deltaX;e.onWheelScroll(h),ni(h,d)&&u.preventDefault()}},onResize:()=>{s.current&&o.viewport&&l&&n({content:o.viewport.scrollWidth,viewport:o.viewport.offsetWidth,scrollbar:{size:s.current.clientWidth,paddingStart:Zt(l.paddingLeft),paddingEnd:Zt(l.paddingRight)}})}})}),kg=p.forwardRef((e,t)=>{const{sizes:r,onSizesChange:n,...a}=e,o=Le(Ve,e.__scopeScrollArea),[l,i]=p.useState(),s=p.useRef(null),c=Xe(t,s,o.onScrollbarYChange);return p.useEffect(()=>{s.current&&i(getComputedStyle(s.current))},[s]),g.jsx(Js,{"data-orientation":"vertical",...a,ref:c,sizes:r,style:{top:0,right:o.dir==="ltr"?0:void 0,left:o.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":dr(r)+"px",...e.style},onThumbPointerDown:u=>e.onThumbPointerDown(u.y),onDragScroll:u=>e.onDragScroll(u.y),onWheelScroll:(u,d)=>{if(o.viewport){const h=o.viewport.scrollTop+u.deltaY;e.onWheelScroll(h),ni(h,d)&&u.preventDefault()}},onResize:()=>{s.current&&o.viewport&&l&&n({content:o.viewport.scrollHeight,viewport:o.viewport.offsetHeight,scrollbar:{size:s.current.clientHeight,paddingStart:Zt(l.paddingTop),paddingEnd:Zt(l.paddingBottom)}})}})}),[Tg,Qs]=qs(Ve),Js=p.forwardRef((e,t)=>{const{__scopeScrollArea:r,sizes:n,hasThumb:a,onThumbChange:o,onThumbPointerUp:l,onThumbPointerDown:i,onThumbPositionChange:s,onDragScroll:c,onWheelScroll:u,onResize:d,...h}=e,f=Le(Ve,r),[b,y]=p.useState(null),k=Xe(t,x=>y(x)),I=p.useRef(null),E=p.useRef(""),j=f.viewport,A=n.content-n.viewport,N=ct(u),z=ct(s),m=fr(d,10);function _(x){if(I.current){const T=x.clientX-I.current.left,R=x.clientY-I.current.top;c({x:T,y:R})}}return p.useEffect(()=>{const x=T=>{const R=T.target;(b==null?void 0:b.contains(R))&&N(T,A)};return document.addEventListener("wheel",x,{passive:!1}),()=>document.removeEventListener("wheel",x,{passive:!1})},[j,b,A,N]),p.useEffect(z,[n,z]),xt(b,m),xt(f.content,m),g.jsx(Tg,{scope:r,scrollbar:b,hasThumb:a,onThumbChange:ct(o),onThumbPointerUp:ct(l),onThumbPositionChange:z,onThumbPointerDown:ct(i),children:g.jsx(Ee.div,{...h,ref:k,style:{position:"absolute",...h.style},onPointerDown:Ce(e.onPointerDown,x=>{x.button===0&&(x.target.setPointerCapture(x.pointerId),I.current=b.getBoundingClientRect(),E.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",f.viewport&&(f.viewport.style.scrollBehavior="auto"),_(x))}),onPointerMove:Ce(e.onPointerMove,_),onPointerUp:Ce(e.onPointerUp,x=>{const T=x.target;T.hasPointerCapture(x.pointerId)&&T.releasePointerCapture(x.pointerId),document.body.style.webkitUserSelect=E.current,f.viewport&&(f.viewport.style.scrollBehavior=""),I.current=null})})})}),Jt="ScrollAreaThumb",Zs=p.forwardRef((e,t)=>{const{forceMount:r,...n}=e,a=Qs(Jt,e.__scopeScrollArea);return g.jsx(St,{present:r||a.hasThumb,children:g.jsx(Rg,{ref:t,...n})})}),Rg=p.forwardRef((e,t)=>{const{__scopeScrollArea:r,style:n,...a}=e,o=Le(Jt,r),l=Qs(Jt,r),{onThumbPositionChange:i}=l,s=Xe(t,d=>l.onThumbChange(d)),c=p.useRef(void 0),u=fr(()=>{c.current&&(c.current(),c.current=void 0)},100);return p.useEffect(()=>{const d=o.viewport;if(d){const h=()=>{if(u(),!c.current){const f=Ng(d,i);c.current=f,i()}};return i(),d.addEventListener("scroll",h),()=>d.removeEventListener("scroll",h)}},[o.viewport,u,i]),g.jsx(Ee.div,{"data-state":l.hasThumb?"visible":"hidden",...a,ref:s,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...n},onPointerDownCapture:Ce(e.onPointerDownCapture,d=>{const f=d.target.getBoundingClientRect(),b=d.clientX-f.left,y=d.clientY-f.top;l.onThumbPointerDown({x:b,y})}),onPointerUp:Ce(e.onPointerUp,l.onThumbPointerUp)})});Zs.displayName=Jt;var Hn="ScrollAreaCorner",ei=p.forwardRef((e,t)=>{const r=Le(Hn,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return r.type!=="scroll"&&n?g.jsx(Ag,{...e,ref:t}):null});ei.displayName=Hn;var Ag=p.forwardRef((e,t)=>{const{__scopeScrollArea:r,...n}=e,a=Le(Hn,r),[o,l]=p.useState(0),[i,s]=p.useState(0),c=!!(o&&i);return xt(a.scrollbarX,()=>{var d;const u=((d=a.scrollbarX)==null?void 0:d.offsetHeight)||0;a.onCornerHeightChange(u),s(u)}),xt(a.scrollbarY,()=>{var d;const u=((d=a.scrollbarY)==null?void 0:d.offsetWidth)||0;a.onCornerWidthChange(u),l(u)}),c?g.jsx(Ee.div,{...n,ref:t,style:{width:o,height:i,position:"absolute",right:a.dir==="ltr"?0:void 0,left:a.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function Zt(e){return e?parseInt(e,10):0}function ti(e,t){const r=e/t;return isNaN(r)?0:r}function dr(e){const t=ti(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,n=(e.scrollbar.size-r)*t;return Math.max(n,18)}function jg(e,t,r,n="ltr"){const a=dr(r),o=a/2,l=t||o,i=a-l,s=r.scrollbar.paddingStart+l,c=r.scrollbar.size-r.scrollbar.paddingEnd-i,u=r.content-r.viewport,d=n==="ltr"?[0,u]:[u*-1,0];return ri([s,c],d)(e)}function na(e,t,r="ltr"){const n=dr(t),a=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,o=t.scrollbar.size-a,l=t.content-t.viewport,i=o-n,s=r==="ltr"?[0,l]:[l*-1,0],c=$i(e,s);return ri([0,l],[0,i])(c)}function ri(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}function ni(e,t){return e>0&&e<t}var Ng=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function a(){const o={left:e.scrollLeft,top:e.scrollTop},l=r.left!==o.left,i=r.top!==o.top;(l||i)&&t(),r=o,n=window.requestAnimationFrame(a)}(),()=>window.cancelAnimationFrame(n)};function fr(e,t){const r=ct(e),n=p.useRef(0);return p.useEffect(()=>()=>window.clearTimeout(n.current),[]),p.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(r,t)},[r,t])}function xt(e,t){const r=ct(t);Mi(()=>{let n=0;if(e){const a=new ResizeObserver(()=>{cancelAnimationFrame(n),n=window.requestAnimationFrame(r)});return a.observe(e),()=>{window.cancelAnimationFrame(n),a.unobserve(e)}}},[e,r])}var oi=Ws,Ig=Ys,Lg=ei;const ai=p.forwardRef(({className:e,children:t,...r},n)=>g.jsxs(oi,{ref:n,className:fe("relative overflow-hidden",e),...r,children:[g.jsx(Ig,{className:"h-full w-full rounded-[inherit]",children:t}),g.jsx(si,{}),g.jsx(Lg,{})]}));ai.displayName=oi.displayName;const si=p.forwardRef(({className:e,orientation:t="vertical",...r},n)=>g.jsx(Mn,{ref:n,orientation:t,className:fe("flex touch-none transition-colors select-none",t==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",t==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...r,children:g.jsx(Zs,{className:"bg-border relative flex-1 rounded-full"})}));si.displayName=Mn.displayName;const Pg=({className:e})=>{const{t}=Se(),r=te.use.typeColorMap();return!r||r.size===0?null:g.jsxs(Us,{className:`p-2 max-w-xs ${e}`,children:[g.jsx("h3",{className:"text-sm font-medium mb-2",children:t("graphPanel.legend")}),g.jsx(ai,{className:"max-h-80",children:g.jsx("div",{className:"flex flex-col gap-1",children:Array.from(r.entries()).map(([n,a])=>g.jsxs("div",{className:"flex items-center gap-2",children:[g.jsx("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:a}}),g.jsx("span",{className:"text-xs truncate",title:n,children:t(`graphPanel.nodeTypes.${n.toLowerCase()}`,n)})]},n))})})]})},zg=()=>{const{t:e}=Se(),t=Z.use.showLegend(),r=Z.use.setShowLegend(),n=p.useCallback(()=>{r(!t)},[t,r]);return g.jsx(be,{variant:Ie,onClick:n,tooltip:e("graphPanel.sideBar.legendControl.toggleLegend"),size:"icon",children:g.jsx(Nc,{})})},oa={allowInvalidContainer:!0,defaultNodeType:"default",defaultEdgeType:"curvedNoArrow",renderEdgeLabels:!1,edgeProgramClasses:{arrow:Si,curvedArrow:Nd,curvedNoArrow:jd},nodeProgramClasses:{default:hd,circel:xi,point:$u},labelGridCellSize:60,labelRenderedSizeThreshold:12,enableEdgeEvents:!0,labelColor:{color:"#000",attribute:"labelColor"},edgeLabelColor:{color:"#000",attribute:"labelColor"},edgeLabelSize:8,labelSize:12},Dg=()=>{const e=ga(),t=Be(),[r,n]=p.useState(null);return p.useEffect(()=>{e({downNode:a=>{n(a.node),t.getGraph().setNodeAttribute(a.node,"highlighted",!0)},mousemovebody:a=>{if(!r)return;const o=t.viewportToGraph(a);t.getGraph().setNodeAttribute(r,"x",o.x),t.getGraph().setNodeAttribute(r,"y",o.y),a.preventSigmaDefault(),a.original.preventDefault(),a.original.stopPropagation()},mouseup:()=>{r&&(n(null),t.getGraph().removeNodeAttribute(r,"highlighted"))},mousedown:a=>{a.original.buttons!==0&&!t.getCustomBBox()&&t.setCustomBBox(t.getBBox())}})},[e,t,r]),null},Op=()=>{const[e,t]=p.useState(oa),r=p.useRef(null),n=te.use.selectedNode(),a=te.use.focusedNode(),o=te.use.moveToSelectedNode(),l=te.use.isFetching(),i=Z.use.showPropertyPanel(),s=Z.use.showNodeSearchBar(),c=Z.use.enableNodeDrag(),u=Z.use.showLegend();p.useEffect(()=>{t(oa),console.log("Initialized sigma settings")},[]),p.useEffect(()=>()=>{const y=te.getState().sigmaInstance;if(y)try{y.kill(),te.getState().setSigmaInstance(null),console.log("Cleared sigma instance on Graphviewer unmount")}catch(k){console.error("Error cleaning up sigma instance:",k)}},[]);const d=p.useCallback(y=>{y===null?te.getState().setFocusedNode(null):y.type==="nodes"&&te.getState().setFocusedNode(y.id)},[]),h=p.useCallback(y=>{y===null?te.getState().setSelectedNode(null):y.type==="nodes"&&te.getState().setSelectedNode(y.id,!0)},[]),f=p.useMemo(()=>a??n,[a,n]),b=p.useMemo(()=>n?{type:"nodes",id:n}:null,[n]);return g.jsxs("div",{className:"relative h-full w-full overflow-hidden",children:[g.jsxs(_i,{settings:e,className:"!bg-background !size-full overflow-hidden",ref:r,children:[g.jsx(Jf,{}),c&&g.jsx(Dg,{}),g.jsx(Id,{node:f,move:o}),g.jsxs("div",{className:"absolute top-2 left-2 flex items-start gap-2",children:[g.jsx(Lh,{}),s&&g.jsx(jh,{value:b,onFocus:d,onChange:h})]}),g.jsxs("div",{className:"bg-background/60 absolute bottom-2 left-2 flex flex-col rounded-xl border-2 backdrop-blur-lg",children:[g.jsx(Kf,{}),g.jsx(Zf,{}),g.jsx(eh,{}),g.jsx(zg,{}),g.jsx(ch,{})]}),i&&g.jsx("div",{className:"absolute top-2 right-2",children:g.jsx(ug,{})}),u&&g.jsx("div",{className:"absolute bottom-10 right-2",children:g.jsx(Pg,{className:"bg-background/60 backdrop-blur-lg"})}),g.jsx(pg,{})]}),l&&g.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-background/80 z-10",children:g.jsxs("div",{className:"text-center",children:[g.jsx("div",{className:"mb-2 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"}),g.jsx("p",{children:"Loading Graph Data..."})]})})]})};export{Qf as $,hp as A,be as B,Us as C,Pu as D,xp as E,_p as F,Yg as G,Kg as H,Wt as I,mg as J,bg as K,dp as L,Ep as M,mp as N,pp as O,vp as P,yp as Q,du as R,ai as S,Np as T,Ip as U,Va as V,Ma as W,Ru as X,$a as Y,Ha as Z,kn as _,vg as a,wp as a0,kp as a1,Ji as a2,Jg as a3,Qg as a4,Hg as a5,Bs as a6,Ap as a7,no as a8,Wg as a9,Xg as aa,Tn as ab,Rn as ac,Rp as ad,ir as ae,qt as af,Bg as ag,Lp as ah,qg as ai,Cp as aj,Tp as ak,Ea as al,Kr as am,Vg as an,ip as ao,Op as ap,np as aq,ap as ar,sp as as,lp as at,yg as b,fe as c,rt as d,Sp as e,Zg as f,rr as g,Pp as h,Xa as i,Ya as j,Qa as k,Ja as l,ep as m,tp as n,Ls as o,Ka as p,rp as q,jp as r,Ug as s,bp as t,Se as u,fp as v,gp as w,op as x,En as y,Z as z};
